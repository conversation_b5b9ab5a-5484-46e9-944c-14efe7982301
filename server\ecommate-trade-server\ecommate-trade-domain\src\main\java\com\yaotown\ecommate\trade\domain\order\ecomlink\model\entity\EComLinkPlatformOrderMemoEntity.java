package com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class EComLinkPlatformOrderMemoEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 平台订单id
     */
    private Long platformOrderId;

    /**
     * 外部订单ID
     */
    private String extOrderId;

    /**
     * 订单来源平台
     */
    private String platform;

    /**
     * 订单所属企业id
     */
    private Long enterpriseId;

    /**
     * 订单供应商企业id
     */
    private Long supplierEnterpriseId;

    /**
     * 订单当前企业id(分销拆单时，当前企业id为拆分后的企业id)
     */
    private Long currentEnterpriseId;

    /**
     * 订单备注
     */
    private String orderMemo;

    /**
     * 买家留言
     */
    private String buyerMessage;

    /**
     * 买家备注
     */
    private String buyerMemo;

    /**
     * 卖家留言
     */
    private String sellerMessage;

    /**
     * 卖家备注
     */
    private String sellerMemo;

    /**
     * 卖家备注旗帜,1、2、3、4、5分别对应红、黄、绿、蓝、紫
     */
    private Integer sellerFlag;

    /**
     * 是否已删除(0:否, 1:是)
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 修改操作人
     */
    private Long modifierId;

    /**
     * 修改操作人名字
     */
    private String modifierName;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date updated;
}
