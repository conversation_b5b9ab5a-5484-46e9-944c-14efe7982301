package com.yaotown.ecommate.product.trigger.biz.convert;

import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierFreightTemplateEntity;
import com.yaotown.ecommate.product.trigger.biz.management.model.request.FreightTemplateReqDTO;
import com.yaotown.ecommate.product.trigger.biz.management.model.response.FreightTemplateRespDTO;
import com.yaotown.ecommate.product.types.util.MapStructConvertUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 运费模板转换类
 */
@Mapper(uses = MapStructConvertUtil.class)
public interface FreightTemplateConvert {

    FreightTemplateConvert INSTANCE = Mappers.getMapper(FreightTemplateConvert.class);
    /**
     * DTO转Entity
     */
    SupplierFreightTemplateEntity convertToEntity(FreightTemplateReqDTO dto);
    /**
     * Entity转DTO
     */
    FreightTemplateRespDTO convertToRespDTO(SupplierFreightTemplateEntity entity);
} 