<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaotown.ecommate.trade.infrastructure.mapper.PlatformOrderMemoMapper">
  <resultMap id="BaseResultMap" type="com.yaotown.ecommate.trade.infrastructure.po.PlatformOrderMemoPO">
    <!--@mbg.generated-->
    <!--@Table yt_platform_order_memo-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_order_id" jdbcType="BIGINT" property="platformOrderId" />
    <result column="ext_order_id" jdbcType="VARCHAR" property="extOrderId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="supplier_enterprise_id" jdbcType="BIGINT" property="supplierEnterpriseId" />
    <result column="current_enterprise_id" jdbcType="BIGINT" property="currentEnterpriseId" />
    <result column="order_memo" jdbcType="VARCHAR" property="orderMemo" />
    <result column="buyer_message" jdbcType="VARCHAR" property="buyerMessage" />
    <result column="buyer_memo" jdbcType="VARCHAR" property="buyerMemo" />
    <result column="seller_message" jdbcType="VARCHAR" property="sellerMessage" />
    <result column="seller_memo" jdbcType="VARCHAR" property="sellerMemo" />
    <result column="seller_flag" jdbcType="SMALLINT" property="sellerFlag" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier_name" jdbcType="VARCHAR" property="modifierName" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, platform_order_id, ext_order_id, platform, enterprise_id, supplier_enterprise_id, 
    current_enterprise_id, order_memo, buyer_message, buyer_memo, seller_message, seller_memo, 
    seller_flag, delete_flag, creator_id, creator_name, modifier_id, modifier_name, created, 
    updated
  </sql>
</mapper>