package com.yaotown.ecommate.product.domain.enterprise.service;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopPlatformTypeEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.valobj.ShopInfoSearchVO;

import java.util.List;

public interface IShopInfoService {


    /**
     * @param accountId
     * @param shopIds
     * @return
     */
    List<ShopInfoEntity> selectShopInfoList(Long accountId, List<Long> shopIds);

    ShopInfoEntity findShopInfo(Long shopId);

    void updateUnbind(Long shopId);

    void shopRefresh(Long shopId);

    List<ShopInfoEntity> selectByPlatformType(String platformType, Long accountId);

    List<ShopPlatformTypeEntity> selectShopType(Long accountId);

    PageData<ShopInfoEntity> selectShopInfoLists(QueryModel<Void> entityQueryModel,String platformType, Long accountId);

    PageData<ShopInfoEntity> selectShopInfoList(QueryModel<ShopInfoSearchVO> queryModel);

    /**
     * 获取店铺授权URL
     * 
     * @param platform 平台类型
     * @param redirectSuccessUrl 授权成功跳转URL
     * @param redirectFailureUrl 授权失败跳转URL
     * @return 授权URL
     */
    String shopAuthorization(String platform, String redirectSuccessUrl, String redirectFailureUrl);
    
    /**
     * 新增店铺信息
     *
     * @param shopInfo 店铺信息实体
     * @return 新增的店铺ID
     */
    Long createShop(ShopInfoEntity shopInfo);
    
    /**
     * 更新店铺信息
     * 
     * @param shopInfo 店铺信息实体
     * @return 更新是否成功
     */
    boolean updateShopInfo(ShopInfoEntity shopInfo);
}
