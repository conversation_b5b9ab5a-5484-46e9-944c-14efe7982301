package com.yaotown.ecommate.product.domain.product.management.repository;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierFreightTemplateEntity;

import java.util.List;

/**
 * 供应商运费模板仓储接口
 */
public interface ISupplierFreightTemplateRepository {

    /**
     * 新增运费模板
     *
     * @param entity 运费模板实体
     * @return 是否成功
     */
    boolean save(SupplierFreightTemplateEntity entity);

    /**
     * 批量新增运费模板
     *
     * @param entities 运费模板实体集合
     * @return 是否成功
     */
    boolean saveBatch(List<SupplierFreightTemplateEntity> entities);

    /**
     * 更新运费模板
     *
     * @param entity 运费模板实体
     * @return 是否成功
     */
    boolean update(SupplierFreightTemplateEntity entity);

    /**
     * 删除运费模板
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 删除运费模板（根据模板名称删除整个模板）
     *
     * @param templateName 模板名称
     * @param enterpriseId 企业ID
     * @return 是否成功
     */
    boolean deleteByTemplateName(String templateName, Long enterpriseId);

    /**
     * 根据ID查询运费模板
     *
     * @param id 主键
     * @param enterpriseId 企业ID
     * @return 运费模板实体
     */
    SupplierFreightTemplateEntity getById(Long id, Long enterpriseId);

    /**
     * 根据模板名称查询运费模板列表
     *
     * @param templateName 模板名称
     * @param enterpriseId 企业ID
     * @return 运费模板实体列表
     */
    List<SupplierFreightTemplateEntity> listByTemplateName(String templateName, Long enterpriseId);

    /**
     * 查询启用的运费模板列表
     *
     * @param enterpriseId 企业ID
     * @return 运费模板实体列表
     */
    List<SupplierFreightTemplateEntity> listActiveTemplate(Long enterpriseId);

    /**
     * 分页查询运费模板
     *
     * @param queryModel 查询条件
     * @param enterpriseId 企业ID
     * @return 分页结果
     */
    PageData<SupplierFreightTemplateEntity> page(QueryModel<String> queryModel, Long enterpriseId);
    
    /**
     * 获取企业的所有运费模板名称
     * 
     * @param enterpriseId 企业ID
     * @return 模板名称列表
     */
    List<String> listTemplateNames(Long enterpriseId);
} 