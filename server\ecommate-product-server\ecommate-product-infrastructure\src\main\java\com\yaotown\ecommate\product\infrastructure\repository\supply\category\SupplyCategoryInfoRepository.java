package com.yaotown.ecommate.product.infrastructure.repository.supply.category;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.common.core.enums.BooleanEnum;
import com.yaotown.ecommate.common.core.enums.ShowStatusEnum;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.redis.util.RedisUtil;
import com.yaotown.ecommate.product.domain.product.listing.model.valobj.CategoryInfoVO;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.entity.CategoryEntity;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.valobj.CategoryMappingVO;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.valobj.SupplyCategoryInfoVO;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.valobj.SupplyCategoryTreeInfoVO;
import com.yaotown.ecommate.product.domain.product.supplycenter.repository.ISupplyCategoryInfoRepository;
import com.yaotown.ecommate.product.infrastructure.convert.SupplyCategoryInfoConvert;
import com.yaotown.ecommate.product.infrastructure.mapper.supply.category.CategoryInfoMapper;
import com.yaotown.ecommate.product.infrastructure.mapper.supply.category.CategoryMappingMapper;
import com.yaotown.ecommate.product.infrastructure.po.supply.category.CategoryMappingPO;
import com.yaotown.ecommate.product.infrastructure.po.supply.category.CategoryPO;
import com.yaotown.ecommate.product.types.constant.ProductRedisConstant;
import com.yaotown.ecommate.product.types.enums.erp.ErpPlatformTypeEnum;
import com.yaotown.ecommate.product.types.util.ProductRedisKeyUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yaotown.ecommate.product.types.constant.erp.SupplyCategoryConstant.TOP_CATEGORY_PARENT_ID;

/**
 * <AUTHOR>
 * @date 2025/4/28
 */
@Slf4j
@Repository
@AllArgsConstructor
public class SupplyCategoryInfoRepository implements ISupplyCategoryInfoRepository {

    private final CategoryInfoMapper categoryInfoMapper;
    private final CategoryMappingMapper categoryMappingMapper;
    private final RedisUtil redisUtil;


    @Override
    public List<SupplyCategoryTreeInfoVO> queryByParentIdAndPlatform(String parentId, String platform, Integer showStatus) {
        List<SupplyCategoryTreeInfoVO> voList = SupplyCategoryInfoConvert.INSTANCE.toTreeVOList(categoryInfoMapper.selectListByParentIdAndPlatform(parentId, platform, showStatus));
        this.loadChildren(voList, platform, showStatus);
        return voList;
    }

    @Override
    public List<SupplyCategoryTreeInfoVO> queryByParentIdAndPlatformWithCache(String parentId, String platform, Integer showStatus) {
        String redisKey = ProductRedisKeyUtil.supplyCategoryInfoParentKey(platform, parentId, showStatus);
        List<SupplyCategoryTreeInfoVO> cacheList = (List<SupplyCategoryTreeInfoVO>) redisUtil.get(redisKey);
        if (CollUtil.isNotEmpty(cacheList)) {
            return cacheList;
        }
        List<SupplyCategoryTreeInfoVO> supplyCategoryInfoVOS = queryByParentIdAndPlatform(parentId, platform, showStatus);
        redisUtil.set(redisKey, supplyCategoryInfoVOS, ProductRedisConstant.SUPPLY_CATEGORY_INFO_PARENT_EXPIRE_TIME);
        return supplyCategoryInfoVOS;
    }

    @Override
    public List<SupplyCategoryInfoVO> queryTopListByPlatform(String platform, Integer showStatus) {
        return SupplyCategoryInfoConvert.INSTANCE.toVOList(categoryInfoMapper.selectListByParentIdAndPlatform(TOP_CATEGORY_PARENT_ID, platform, showStatus));
    }


    @Override
    public List<SupplyCategoryInfoVO> queryTopListByPlatformWithCache(String platform, Integer showStatus) {
        String redisKey = ProductRedisKeyUtil.supplyCategoryInfoTopKey(platform, showStatus);
        List<SupplyCategoryInfoVO> cacheList = (List<SupplyCategoryInfoVO>) redisUtil.get(redisKey);
        if (CollUtil.isNotEmpty(cacheList)) {
            return cacheList;
        }
        List<SupplyCategoryInfoVO> supplyCategoryInfoVOS = queryTopListByPlatform(platform, showStatus);
        redisUtil.set(redisKey, supplyCategoryInfoVOS, ProductRedisConstant.SUPPLY_CATEGORY_INFO_PARENT_EXPIRE_TIME);
        return supplyCategoryInfoVOS;
    }

    @Override
    public List<SupplyCategoryInfoVO> queryLeafListByPlatform(String platform, Integer showStatus) {
        return SupplyCategoryInfoConvert.INSTANCE.toVOList(categoryInfoMapper.selectLeafListByPlatform(null, platform, showStatus));
    }

    @Override
    public List<String> getLeafCategoryIds(String categoryId, String platform, Integer showStatus) {
        List<String> leafIds = new ArrayList<>();
        // 获取当前类目及其子树信息
        List<SupplyCategoryTreeInfoVO> categoryTree = queryByParentIdAndPlatform(categoryId, platform, showStatus);
        // 递归收集叶子节点ID
        collectLeafIds(categoryTree, leafIds);
        // 检查当前类目自身是否为叶子节点（假设通过mapper获取当前类目信息）
        CategoryPO categoryPO = categoryInfoMapper.selectByIdAndPlatform(categoryId, platform);
        if (categoryPO != null && categoryPO.getLeaf() == BooleanEnum.TRUE.getId()) {
            leafIds.add(categoryPO.getId());
        }
        return leafIds;
    }

    @Override
    public CategoryInfoVO getCategoryById(String platform, String categoryId) {
        CategoryPO categoryPO = categoryInfoMapper.selectByIdAndPlatform(categoryId, platform);
        CategoryInfoVO categoryInfoVO = new CategoryInfoVO();
        categoryInfoVO.setId(categoryPO.getId());
        categoryInfoVO.setPlatform(categoryPO.getPlatform());
        categoryInfoVO.setName(categoryPO.getName());
        return categoryInfoVO;
    }

    @Override
    public PageData<CategoryInfoVO> queryLeafCategories(QueryModel<CategoryInfoVO> queryModel) {
        CategoryInfoVO param = queryModel.getParam();
        param.setPlatform(ErpPlatformTypeEnum.YHT.getValue());
        // 获取指定平台的叶子节点类目，显示状态为可见（1）
        return queryModel.queryPageData(() ->
                        categoryInfoMapper.selectLeafListByPlatform(param.getName(), param.getPlatform(), BooleanEnum.TRUE.getId()),
                SupplyCategoryInfoConvert.INSTANCE::toInfoVO);
    }

    @Override
    public PageData<CategoryInfoVO> queryUntaggedCategories(QueryModel<?> queryModel, List<String> excludeCategoryIds) {
        // 获取类目名称查询参数
        final String nameKeyword = getNameKeywordFromParam(queryModel.getParam());

        // 设置固定平台值YHT
        String platform = ErpPlatformTypeEnum.YHT.getValue();

        // 查询未关联标签的类目
        return queryModel.queryPageData(() ->
                        categoryInfoMapper.selectUntaggedCategories(
                                nameKeyword,
                                platform,
                                BooleanEnum.TRUE.getId(),
                                excludeCategoryIds),
                SupplyCategoryInfoConvert.INSTANCE::toInfoVO);
    }

    @Override
    public SupplyCategoryInfoVO queryById(String id, String platform) {
        return SupplyCategoryInfoConvert.INSTANCE.toPdInfoVO(categoryInfoMapper.selectByIdAndPlatform(id, platform));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplyCategoryInfoVO save(CategoryEntity category) {
        CategoryPO po = SupplyCategoryInfoConvert.INSTANCE.toPO(category);
        // 检查父节点是否为叶子节点，如果是则更新为非叶子节点
        if (!TOP_CATEGORY_PARENT_ID.equals(category.getParentId())) {
            CategoryPO parentPO = categoryInfoMapper.selectByIdAndPlatform(category.getParentId(), po.getPlatform());
            if (parentPO != null && Integer.valueOf(BooleanEnum.TRUE.getId()).equals(parentPO.getLeaf())) {
                // 更新父节点为非叶子节点(只更新叶子节点字段)
                CategoryPO updatePO = new CategoryPO();
                updatePO.setId(parentPO.getId());
                updatePO.setLeaf(BooleanEnum.FALSE.getId());
                // 更新父节点缓存
                parentPO.setLeaf(BooleanEnum.FALSE.getId());
                categoryInfoMapper.updateByIdAndPlatform(updatePO);
                // 修改相关缓存
                handleCategoryTreeCache(po.getPlatform(), parentPO, "UPDATE", po.getParentId());
            }
        }
        //如果父节点下存在子节点，则当前节点设置为同级节点
        List<CategoryPO> categoryPOS = categoryInfoMapper.selectListByParentIdAndPlatform(category.getParentId(), po.getPlatform(), BooleanEnum.TRUE.getId());
        if (CollUtil.isNotEmpty(categoryPOS)) {
            CategoryPO oldPO = categoryPOS.get(0);
            po.setLeaf(oldPO.getLeaf());
        } else {
            // 设置为叶子节点
            po.setLeaf(BooleanEnum.TRUE.getId());
        }
        // 设置未删除标志
        po.setDeleteFlag(BooleanEnum.FALSE.getId());

        // 保存新节点
        int result = categoryInfoMapper.insert(po);

        // 新增相关缓存
        handleCategoryTreeCache(po.getPlatform(), po, "ADD", po.getParentId());

        return SupplyCategoryInfoConvert.INSTANCE.toPdInfoVO(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SupplyCategoryInfoVO update(CategoryEntity category) {
        CategoryPO po = SupplyCategoryInfoConvert.INSTANCE.toPO(category);

        // 获取原始数据
        CategoryPO oldPO = categoryInfoMapper.selectByIdAndPlatform(po.getId(), po.getPlatform());
        if (oldPO == null) {
            throw new BusinessException("类目不存在");
        }
        // 更新节点
        int result = categoryInfoMapper.updateByIdAndPlatform(po);
        //更新相关缓存
        handleCategoryTreeCache(category.getPlatform(), po, "UPDATE", oldPO.getParentId());

        return SupplyCategoryInfoConvert.INSTANCE.toPdInfoVO(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(String id, String platform) {
        // 获取当前节点信息
        CategoryPO po = categoryInfoMapper.selectByIdAndPlatform(id, platform);
        if (po == null) {
            return false;
        }

        // 检查是否有子节点
        int childCount = categoryInfoMapper.selectCount(
                new LambdaQueryWrapper<CategoryPO>()
                        .eq(CategoryPO::getParentId, id)
                        .eq(CategoryPO::getDeleteFlag, BooleanEnum.FALSE.getId())
        ).intValue();

        if (childCount > 0) {
            // 有子节点不允许删除
            log.warn("类目[{}]存在子节点，不能删除", id);
            return false;
        }

        // 保存父节点ID和平台信息，用于后续清除缓存
        String parentId = po.getParentId();

        // 删除节点
        int result = categoryInfoMapper.deleteByIdAndPlatform(id, platform);

        // 删除后检查原父节点下是否还有其他子节点，如果没有则将原父节点更新为叶子节点
        if (result > 0 && !TOP_CATEGORY_PARENT_ID.equals(parentId)) {
            int remainingChildCount = categoryInfoMapper.selectCount(
                    new LambdaQueryWrapper<CategoryPO>()
                            .eq(CategoryPO::getParentId, parentId)
                            .eq(CategoryPO::getPlatform, platform)
                            .eq(CategoryPO::getDeleteFlag, BooleanEnum.FALSE.getId())
            ).intValue();

            if (remainingChildCount == 0) {
                CategoryPO parentPO = categoryInfoMapper.selectByIdAndPlatform(parentId, platform);
                if (parentPO != null) {
                    CategoryPO updatePO = new CategoryPO();
                    updatePO.setId(parentPO.getId());
                    updatePO.setLeaf(BooleanEnum.TRUE.getId());
                    categoryInfoMapper.updateByIdAndPlatform(updatePO);
                    // 更新父节点缓存
                    //修改父节点的缓存
                    parentPO.setLeaf(BooleanEnum.TRUE.getId());
                    handleCategoryTreeCache(platform, parentPO, "UPDATE", parentId);
                }
            }

            // 清除相关缓存
            handleCategoryTreeCache(platform, po, "DELETE", parentId);
        }

        return result > 0;
    }


    @Override
    public CategoryMappingVO getCategoryMappingByPlatformId(String platform, String platformCategoryLeafId) {
        CategoryMappingPO categoryMappingPO = categoryMappingMapper.selectByPlatformLeafId(platform, platformCategoryLeafId);
        if (categoryMappingPO != null) {
            CategoryMappingVO categoryMappingVO = new CategoryMappingVO();
            categoryMappingVO.setCategoryId(categoryMappingPO.getCid());
            categoryMappingVO.setCategoryName(categoryMappingPO.getCname());
            categoryMappingVO.setCategoryLeafId(categoryMappingPO.getClid());
            categoryMappingVO.setPlatformCategoryId(categoryMappingPO.getPid());
            categoryMappingVO.setPlatformCategoryName(categoryMappingPO.getPname());
            categoryMappingVO.setPlatformCategoryLeafId(categoryMappingPO.getPlid());
            categoryMappingVO.setPlatform(categoryMappingPO.getPlatform());
            return categoryMappingVO;
        }
        return null;
    }

    /**
     * 更新分类树缓存（直接实现版）
     *
     * @param platform      平台
     * @param category      当前操作的分类
     * @param operationType 操作类型：DELETE/UPDATE/ADD/MOVE
     * @param oldParentId   旧的父ID（仅MOVE操作需要）
     */
    private void handleCategoryTreeCache(String platform,
                                         CategoryPO category,
                                         String operationType,
                                         String oldParentId) {
        doHandleCategoryTreeCache(platform, category, operationType, oldParentId, ShowStatusEnum.VISIBLE.getCode());
        doHandleCategoryTreeCache(platform, category, operationType, oldParentId, null);
    }

    /**
     * 更新分类树缓存（直接实现版）
     *
     * @param platform      平台
     * @param category      当前操作的分类
     * @param operationType 操作类型：DELETE/UPDATE/ADD/MOVE
     * @param oldParentId   旧的父ID（仅MOVE操作需要）
     */
    private void doHandleCategoryTreeCache(String platform, CategoryPO category, String operationType,
                                         String oldParentId,Integer showStatus) {
        // 1. 获取树形缓存key（如"YHT:0"）
        String treeCacheKey = ProductRedisKeyUtil.supplyCategoryInfoParentKey(platform, "0", showStatus);
        Object cachedTree = redisUtil.get(treeCacheKey);

        if (cachedTree == null) {
            return;
        }

        try {
            // 2. 反序列化树形数据
            List<SupplyCategoryTreeInfoVO> categoryTree = (List<SupplyCategoryTreeInfoVO>) cachedTree;

            // 3. 根据操作类型处理
            switch (operationType) {
                case "DELETE":
                    removeCategoryNode(categoryTree, category.getId());
                    break;
                case "UPDATE":
                    updateCategoryNode(categoryTree, category);
                    break;
                case "ADD":
                    addCategoryNode(categoryTree, category);
                    break;
                case "MOVE":
                    moveCategoryNode(categoryTree, category, oldParentId);
                    break;
            }

            // 4. 更新缓存
            redisUtil.set(treeCacheKey, categoryTree);

        } catch (ClassCastException e) {
            log.error("树形缓存反序列化失败，直接清除缓存。key: {}", treeCacheKey, e);
            redisUtil.del(treeCacheKey);
        }
    }


    private boolean removeCategoryNode(List<SupplyCategoryTreeInfoVO> nodes, String id) {
        Iterator<SupplyCategoryTreeInfoVO> iterator = nodes.iterator();
        while (iterator.hasNext()) {
            SupplyCategoryTreeInfoVO node = iterator.next();
            if (id.equals(node.getId())) {
                iterator.remove();
                return true;
            }
            if (node.getChildren() != null && removeCategoryNode(node.getChildren(), id)) {
                return true;
            }
        }
        return false;
    }

    private void updateCategoryNode(List<SupplyCategoryTreeInfoVO> nodes, CategoryPO category) {
        for (SupplyCategoryTreeInfoVO node : nodes) {
            if (category.getId().equals(node.getId())) {
                // 更新可修改字段（根据业务需求调整）
                node.setName(category.getName());
                node.setImageUrl(category.getImageUrl());
                node.setShowStatus(category.getShowStatus());
                node.setLeaf(category.getLeaf());
                node.setParentId(category.getParentId());
                node.setPlatform(category.getPlatform());
                return;
            }
            if (node.getChildren() != null) {
                updateCategoryNode(node.getChildren(), category);
            }
        }
    }

    private void addCategoryNode(List<SupplyCategoryTreeInfoVO> nodes, CategoryPO category) {
        for (SupplyCategoryTreeInfoVO node : nodes) {
            if (category.getParentId().equals(node.getId())) {
                if (node.getChildren() == null) {
                    node.setChildren(new ArrayList<>());
                }
                node.getChildren().add(convertToVO(category));
                node.setLeaf(BooleanEnum.FALSE.getId()); // 父节点不再是叶子节点
                return;
            }
            if (node.getChildren() != null) {
                addCategoryNode(node.getChildren(), category);
            }
        }
    }

    private void moveCategoryNode(List<SupplyCategoryTreeInfoVO> nodes,
                                  CategoryPO category,
                                  String oldParentId) {
        // 1. 从旧位置删除
        removeCategoryNode(nodes, category.getId());
        // 2. 添加到新位置
        addCategoryNode(nodes, category);
        // 3. 更新旧父节点的leaf状态
        updateParentLeafStatusInCache(nodes, oldParentId);
    }

    private void updateParentLeafStatusInCache(List<SupplyCategoryTreeInfoVO> nodes, String parentId) {
        for (SupplyCategoryTreeInfoVO node : nodes) {
            if (parentId.equals(node.getId())) {
                boolean hasChildren = node.getChildren() != null && !node.getChildren().isEmpty();
                node.setLeaf(hasChildren ? BooleanEnum.FALSE.getId() : BooleanEnum.TRUE.getId());
                return;
            }
            if (node.getChildren() != null) {
                updateParentLeafStatusInCache(node.getChildren(), parentId);
            }
        }
    }

    private SupplyCategoryTreeInfoVO convertToVO(CategoryPO po) {
        SupplyCategoryTreeInfoVO vo = new SupplyCategoryTreeInfoVO();
        vo.setId(po.getId());
        vo.setName(po.getName());
        vo.setPlatform(po.getPlatform());
        vo.setParentId(po.getParentId());
        vo.setLevel(po.getLevel());
        vo.setLeaf(po.getLeaf());
        vo.setShowStatus(po.getShowStatus());
        vo.setImageUrl(po.getImageUrl());
        return vo;
    }

    /**
     * 从树中移除指定ID的节点
     */
    private void removeCategoryById(List<SupplyCategoryTreeInfoVO> categories, String categoryId) {
        Iterator<SupplyCategoryTreeInfoVO> iterator = categories.iterator();
        while (iterator.hasNext()) {
            SupplyCategoryTreeInfoVO category = iterator.next();
            if (categoryId.equals(category.getId())) {
                iterator.remove();
                return;
            }
            if (category.getChildren() != null) {
                removeCategoryById(category.getChildren(), categoryId);
            }
        }
    }

    /**
     * 移除指定父节点下的所有子节点（保留父节点）
     */
    private void removeChildrenByParentId(List<SupplyCategoryTreeInfoVO> categories, String parentId) {
        for (SupplyCategoryTreeInfoVO category : categories) {
            if (parentId.equals(category.getId())) {
                category.setChildren(null); // 或 new ArrayList<>()
                return;
            }
            if (category.getChildren() != null) {
                removeChildrenByParentId(category.getChildren(), parentId);
            }
        }
    }

    private void collectLeafIds(List<SupplyCategoryTreeInfoVO> nodes, List<String> leafIds) {
        if (CollUtil.isEmpty(nodes)) {
            return;
        }
        for (SupplyCategoryTreeInfoVO node : nodes) {
            if (node.getLeaf() == BooleanEnum.TRUE.getId()) {
                leafIds.add(node.getId());
            }
            collectLeafIds(node.getChildren(), leafIds);
        }
    }

    private void loadChildren(List<SupplyCategoryTreeInfoVO> parentVoList, String platform, Integer showStatus) {

        if (CollUtil.isEmpty(parentVoList))
            return;

        List<String> parentIds = parentVoList.stream()
                .filter(dto -> dto.getLeaf() == 0)
                .map(SupplyCategoryTreeInfoVO::getId)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(parentIds))
            return;

        List<SupplyCategoryTreeInfoVO> childVoList = SupplyCategoryInfoConvert.INSTANCE.toTreeVOList(categoryInfoMapper.selectListByParentIdsAndPlatform(parentIds, platform, showStatus));
        Map<String, List<SupplyCategoryTreeInfoVO>> childrenMap = childVoList.stream()
                .collect(Collectors.groupingBy(
                        SupplyCategoryTreeInfoVO::getParentId,
                        Collectors.mapping(
                                Function.identity(),
                                Collectors.toList()
                        )
                ));
        parentVoList.forEach(dto -> dto.setChildren(childrenMap.get(dto.getId())));

        loadChildren(childVoList, platform, showStatus);
    }


    /**
     * 从参数对象中提取nameKeyword字段
     *
     * @param param 参数对象
     * @return 获取到的nameKeyword值，如果不存在则返回null
     */
    private String getNameKeywordFromParam(Object param) {
        if (param == null) {
            return null;
        }

        try {
            // 尝试从请求参数中获取nameKeyword
            java.lang.reflect.Field field = param.getClass().getDeclaredField("nameKeyword");
            field.setAccessible(true);
            Object value = field.get(param);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            // 忽略异常，表示请求参数中没有nameKeyword字段
            log.warn("Failed to extract nameKeyword from query param: {}", e.getMessage());
            return null;
        }
    }
}