package com.yaotown.ecommate.product.infrastructure.mapper.management;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yaotown.ecommate.common.core.enums.BooleanEnum;
import com.yaotown.ecommate.product.infrastructure.po.management.SupplierProductCategoryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商商品分类 Mapper
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Mapper
public interface SupplierProductCategoryMapper extends BaseMapper<SupplierProductCategoryPO> {
    
    /**
     * 根据企业ID查询分类列表
     *
     * @param enterpriseId 企业ID
     * @return 分类列表
     */
    default List<SupplierProductCategoryPO> selectByEnterpriseId(Long enterpriseId) {
        LambdaQueryWrapper<SupplierProductCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierProductCategoryPO::getEnterpriseId, enterpriseId);
        queryWrapper.orderByDesc(SupplierProductCategoryPO::getWeight);
        return selectList(queryWrapper);
    }
    
    /**
     * 根据企业ID查询显示状态的分类列表
     *
     * @param enterpriseId 企业ID
     * @return 分类列表
     */
    default List<SupplierProductCategoryPO> selectVisibleByEnterpriseId(Long enterpriseId) {
        LambdaQueryWrapper<SupplierProductCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierProductCategoryPO::getEnterpriseId, enterpriseId);
        queryWrapper.eq(SupplierProductCategoryPO::getStatus, BooleanEnum.TRUE.getId());
        queryWrapper.orderByDesc(SupplierProductCategoryPO::getWeight);
        return selectList(queryWrapper);
    }
    
    /**
     * 根据企业ID和父分类ID查询分类列表
     *
     * @param enterpriseId 企业ID
     * @param parentId 父分类ID
     * @return 分类列表
     */
    default List<SupplierProductCategoryPO> selectByParentId(Long enterpriseId, Long parentId) {
        LambdaQueryWrapper<SupplierProductCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierProductCategoryPO::getEnterpriseId, enterpriseId);
        queryWrapper.eq(SupplierProductCategoryPO::getParentId, parentId);
        queryWrapper.orderByDesc(SupplierProductCategoryPO::getWeight);
        return selectList(queryWrapper);
    }
    
    /**
     * 根据企业ID和父分类ID查询显示状态的分类列表
     *
     * @param enterpriseId 企业ID
     * @param parentId 父分类ID
     * @return 分类列表
     */
    default List<SupplierProductCategoryPO> selectVisibleByParentId(Long enterpriseId, Long parentId) {
        LambdaQueryWrapper<SupplierProductCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierProductCategoryPO::getEnterpriseId, enterpriseId);
        queryWrapper.eq(SupplierProductCategoryPO::getParentId, parentId);
        queryWrapper.eq(SupplierProductCategoryPO::getStatus, BooleanEnum.TRUE.getId());
        queryWrapper.orderByDesc(SupplierProductCategoryPO::getWeight);
        return selectList(queryWrapper);
    }


    /**
     * 根据企业ID和父分类ID查询分类列表
     *
     * @param enterpriseId 企业ID
     * @return 分类列表
     */
    default List<SupplierProductCategoryPO> selectVisibleByLevel(Long enterpriseId,Integer lever) {
        LambdaQueryWrapper<SupplierProductCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierProductCategoryPO::getEnterpriseId, enterpriseId);
        queryWrapper.eq(SupplierProductCategoryPO::getLevel, lever);
        queryWrapper.eq(SupplierProductCategoryPO::getStatus, BooleanEnum.TRUE.getId());
        queryWrapper.orderByDesc(SupplierProductCategoryPO::getWeight);
        return selectList(queryWrapper);
    }

    /**
     * 根据企业ID和父分类ID查询分类列表
     *
     * @param enterpriseId 企业ID
     * @return 分类列表
     */
    default List<SupplierProductCategoryPO> selectByLevel(Long enterpriseId,Integer lever) {
        LambdaQueryWrapper<SupplierProductCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierProductCategoryPO::getEnterpriseId, enterpriseId);
        queryWrapper.eq(SupplierProductCategoryPO::getLevel, lever);
        queryWrapper.orderByDesc(SupplierProductCategoryPO::getWeight);
        return selectList(queryWrapper);
    }
    
    /**
     * 根据分类名称查询分类
     *
     * @param enterpriseId 企业ID
     * @param categoryName 分类名称
     * @param parentId 父分类ID（可为null）
     * @return 分类
     */
    default SupplierProductCategoryPO selectByCategoryName(Long enterpriseId, String categoryName, Long parentId) {
        LambdaQueryWrapper<SupplierProductCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierProductCategoryPO::getEnterpriseId, enterpriseId);
        queryWrapper.eq(SupplierProductCategoryPO::getCategoryName, categoryName);
        // 如果parentId不为null，则增加父分类ID条件
        if (parentId != null) {
            queryWrapper.eq(SupplierProductCategoryPO::getParentId, parentId);
        }
        return selectOne(queryWrapper);
    }
    
    /**
     * 逻辑删除分类
     *
     * @return 影响行数
     */
    int deleteCategory(SupplierProductCategoryPO existingPO);
    
    /**
     * 更新商品数量
     *
     * @param id 分类ID
     * @param count 变化的数量，正数为增加，负数为减少
     * @return 影响行数
     */
    int updateProductCount(@Param("id") Long id, @Param("count") long count);
    
    /**
     * 更新分类状态
     *
     * @param id 分类ID
     * @param status 状态：1-显示，0-隐藏
     * @param modifierId 修改人ID
     * @param modifierName 修改人名称
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, 
                    @Param("status") Integer status, 
                    @Param("modifierId") Long modifierId, 
                    @Param("modifierName") String modifierName);
    
    /**
     * 根据父分类ID统计子分类数量
     *
     * @param parentId 父分类ID
     * @param enterpriseId 企业ID
     * @return 子分类数量
     */
    default long countByParentId(Long parentId, Long enterpriseId) {
        LambdaQueryWrapper<SupplierProductCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierProductCategoryPO::getParentId, parentId);
        queryWrapper.eq(SupplierProductCategoryPO::getEnterpriseId, enterpriseId);
        return selectCount(queryWrapper);
    }
    
    /**
     * 更新分类选择性字段
     *
     * @param categoryPO 分类PO
     * @return 影响行数
     */
    default int updateSelectiveById(SupplierProductCategoryPO categoryPO) {
        // 只更新非null字段
        LambdaUpdateWrapper<SupplierProductCategoryPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SupplierProductCategoryPO::getId, categoryPO.getId());
        
        if (categoryPO.getCategoryName() != null) {
            updateWrapper.set(SupplierProductCategoryPO::getCategoryName, categoryPO.getCategoryName());
        }
        if (categoryPO.getWeight() != null) {
            updateWrapper.set(SupplierProductCategoryPO::getWeight, categoryPO.getWeight());
        }
        if (categoryPO.getRate() != null) {
            updateWrapper.set(SupplierProductCategoryPO::getRate, categoryPO.getRate());
        }
        if (categoryPO.getStatus() != null) {
            updateWrapper.set(SupplierProductCategoryPO::getStatus, categoryPO.getStatus());
        }
        if (categoryPO.getModifierId() != null) {
            updateWrapper.set(SupplierProductCategoryPO::getModifierId, categoryPO.getModifierId());
        }
        if (categoryPO.getModifierName() != null) {
            updateWrapper.set(SupplierProductCategoryPO::getModifierName, categoryPO.getModifierName());
        }
        
        return update(null, updateWrapper);
    }
} 