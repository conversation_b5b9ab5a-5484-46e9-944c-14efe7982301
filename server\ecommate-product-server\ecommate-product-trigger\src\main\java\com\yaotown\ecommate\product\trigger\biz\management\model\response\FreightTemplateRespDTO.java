package com.yaotown.ecommate.product.trigger.biz.management.model.response;

import lombok.Data;

import java.util.Date;

/**
 * 运费模板响应DTO
 */
@Data
public class FreightTemplateRespDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 地区名称
     */
    private String regionName;

    /**
     * 地区编码，多个用逗号分隔
     */
    private String regionCodes;

    /**
     * 首件(个)
     */
    private Integer firstItem;

    /**
     * 首件运费(分)
     */
    private Long firstItemFee;

    /**
     * 首件运费(元)，用于前端显示
     */
    private Double firstItemFeeYuan;

    /**
     * 续件(个)
     */
    private Integer additionalItem;

    /**
     * 续件运费(分)
     */
    private Long additionalItemFee;

    /**
     * 续件运费(元)，用于前端显示
     */
    private Double additionalItemFeeYuan;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date updated;
} 