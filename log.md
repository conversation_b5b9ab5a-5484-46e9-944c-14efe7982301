# 开发日志

## 2025-07-29 FxgOrderAggregate 字段合并重构

### 修改内容
将 `FxgOrderAggregate` 类中的 `ShopOrderList` 和 `ShopOrderDetail` 对象的字段合并，提取到 `FxgOrderAggregate` 这一层。

### 具体变更

#### 1. 字段提取
- 将 `ShopOrderList` 内部类中的所有字段提取到 `FxgOrderAggregate` 主类中
- 将 `ShopOrderDetail` 内部类中的所有字段提取到 `FxgOrderAggregate` 主类中
- 去除重复字段，保留唯一字段定义

#### 2. 删除内部类
- 删除了 `ShopOrderList` 内部类定义
- 删除了 `ShopOrderDetail` 内部类定义
- 删除了大量注释掉的重复代码

#### 3. 字段分类整理
将提取的字段按功能进行了分类：

**基础订单信息：**
- 接单状态、应用ID、预约发货时间、达人成本金额等基础字段
- 订单ID、订单状态、订单类型、订单金额等核心订单信息
- 支付相关字段：支付金额、支付时间、支付类型等
- 物流相关字段：发货时间、收货时间、物流信息等
- 优惠相关字段：各种优惠金额、促销信息等

**复杂对象字段：**
- 地址标签UI信息 (AddressTagUi)
- 合并信息 (ConsolidateInfo)
- 懂车帝店铺业务数据 (DCarShopBizData)
- 物流信息 (LogisticsInfo)
- 用户相关信息 (UserCoordinate, UserIdInfo, UserTagUi)
- SKU订单列表 (SkuOrderList)
- 其他复杂对象

#### 4. 保留的内部类
保留了以下必要的内部类定义：
- AddressTagUi - 地址标签UI信息
- ConsolidateInfo - 合并信息
- DCarShopBizData - 懂车帝店铺业务数据
- CouponRight - 优惠券权益
- ExtraPromotionAmountDetail - 额外优惠金额详情
- ShareCost - 分摊成本
- FreeInterest - 免息信息
- FreeInterestDetail - 免息详情
- LogisticsInfo - 物流信息
- LogisticsTrack - 物流轨迹
- MaskPostAddr - 脱敏收货地址
- OrderOperateRecord - 订单操作记录
- OrderPhase - 订单阶段
- PostAddr - 收货地址
- PromiseDetail - 承诺详情
- PromotionDetail - 优惠详情
- PromotionList - 优惠列表
- ShopOrderTagUi - 店铺订单标签UI
- ShopPeriodPurchaseInfo - 店铺分期购买信息
- SkuOrderList - SKU订单列表
- SkuSpec - SKU规格
- UserCoordinate - 用户坐标
- UserIdInfo - 用户身份信息
- UserTagUi - 用户标签UI

### 重构原则遵循
1. **DRY原则** - 消除了 ShopOrderList 和 ShopOrderDetail 中的重复字段定义
2. **KISS原则** - 简化了类结构，将嵌套的对象字段扁平化到主类中
3. **SOLID原则** - 保持了单一职责，FxgOrderAggregate 专注于抖店订单聚合
4. **YAGNI原则** - 删除了注释掉的冗余代码

### 影响范围
- 文件：`server/ecommate-trade-server/ecommate-trade-domain/src/main/java/com/yaotown/ecommate/trade/domain/order/ecomlink/model/aggregate/ecomorder/FxgOrderAggregate.java`
- 行数变化：从 2424 行减少到 1382 行，减少了 1042 行代码
- 主要是删除了重复的字段定义和注释代码

### 注意事项
- 需要检查使用 `ShopOrderList` 和 `ShopOrderDetail` 的相关代码，确保兼容性
- JSON 序列化/反序列化可能需要相应调整
- 建议进行充分的单元测试验证功能正确性

## 2025-07-28 - 项目复制和重命名
### 项目概述
将 `ecommate-infra-server` 项目完整复制到 `ecommate-orderlink-server`，并将所有包路径、项目名称、模块名称从 'infra' 改为 'orderlink'。

### 完成的任务

#### 1. 分析源项目结构 ✅
- 分析了 `ecommate-infra-server` 项目的完整结构
- 确认了项目包含以下模块：
  - `ecommate-infra-module` (包含 api 和 biz 子模块)
  - `ecommate-infra-web`
- 确认了包路径结构：`com.yaotown.ecommate.infra`

#### 2. 创建目标项目根目录结构 ✅
- 复制并修改了根目录的 `pom.xml`
- 将 `artifactId` 从 `ecommate-infra-server` 改为 `ecommate-orderlink-server`
- 将模块引用从 `ecommate-infra-*` 改为 `ecommate-orderlink-*`
- 更新了项目描述为"源汇通订单链接服务"

#### 3. 复制并重命名 ecommate-infra-module ✅
- 创建了 `ecommate-orderlink-module` 目录结构
- 复制并修改了模块的 `pom.xml` 文件：
  - `ecommate-orderlink-module/pom.xml`
  - `ecommate-orderlink-module-api/pom.xml`
  - `ecommate-orderlink-module-biz/pom.xml`
- 复制了所有 Java 源码和资源文件
- 更新了模块间的依赖引用

#### 4. 复制并重命名 ecommate-infra-web ✅
- 创建了 `ecommate-orderlink-web` 目录
- 复制并修改了 `pom.xml` 文件
- 将依赖从 `ecommate-infra-module-biz` 改为 `ecommate-orderlink-module-biz`
- 复制了所有源码、配置文件和 Dockerfile

#### 5. 更新 Java 包路径 ✅
- 重命名了目录结构：
  - `com/yaotown/ecommate/infra` → `com/yaotown/ecommate/orderlink`
- 批量替换了所有 Java 文件中的包路径引用
- 批量替换了所有 XML 文件中的包路径引用
- 更新了主应用类名：`InfraApplication` → `OrderlinkApplication`

#### 6. 更新配置文件 ✅
- 更新了 `bootstrap.yml` 中的应用名称：
  - `ecommate-infra-server` → `ecommate-orderlink-server`
- 批量替换了所有配置文件中的路径：
  - `/infra/` → `/orderlink/`
- 更新了安全配置中的 URL 路径

#### 7. 更新 Dockerfile ✅
- 更新了 `Dockerfile` 中的 JAR 文件名：
  - `ecommate-infra-web.jar` → `ecommate-orderlink-web.jar`
- 更新了 `Dockerfile-arm` 中的 JAR 文件名

#### 8. 验证项目结构 ✅
- 确认了目录结构正确
- 验证了包路径更新正确
- 验证了配置文件更新正确
- 验证了依赖关系正确

### 关键变更汇总

#### 包路径变更
- `com.yaotown.ecommate.infra` → `com.yaotown.ecommate.orderlink`

#### 项目名称变更
- `ecommate-infra-server` → `ecommate-orderlink-server`
- `ecommate-infra-module` → `ecommate-orderlink-module`
- `ecommate-infra-module-api` → `ecommate-orderlink-module-api`
- `ecommate-infra-module-biz` → `ecommate-orderlink-module-biz`
- `ecommate-infra-web` → `ecommate-orderlink-web`

#### 应用类名变更
- `InfraApplication` → `OrderlinkApplication`

#### URL 路径变更
- `/infra/` → `/orderlink/`

### 注意事项
1. 所有的包路径引用已经更新
2. 所有的模块依赖关系已经更新
3. 配置文件中的路径已经更新
4. Dockerfile 中的 JAR 文件名已经更新
5. 项目可以独立编译和运行

### 后续建议
1. 建议运行 `mvn clean compile` 验证编译是否成功
2. 建议运行单元测试确保功能正常
3. 建议更新相关的部署脚本和配置
4. 建议更新项目文档和 README 文件

## 2025-07-28 通用 platform + extOrderId 更新功能实现 ✅
### 需求描述
实现通过 platform 和 extOrderId 更新表数据的功能，要求使用一条 SQL 语句直接实现新增或更新，而不需要先查询再更新。

### 实现方案
使用 MySQL 的 `ON DUPLICATE KEY UPDATE` 语法，基于 platform + ext_order_id 的唯一索引实现 upsert 操作。

### 具体实现
1. **Mapper 接口增强**：
   - 在 `OriginalPlatformOrderMapper` 中添加 `saveOrUpdate` 方法
   - 使用 `@Param` 注解支持参数传递

2. **SQL 实现**：
   ```sql
   INSERT INTO yt_original_platform_order (
     ext_order_id, platform, xyt_shop_id, content, delete_flag, created, updated
   ) VALUES (
     #{extOrderId}, #{platform}, #{xytShopId}, #{content},
     COALESCE(#{deleteFlag}, 0), COALESCE(#{created}, NOW()), COALESCE(#{updated}, NOW())
   )
   ON DUPLICATE KEY UPDATE
     xyt_shop_id = VALUES(xyt_shop_id),
     content = VALUES(content),
     updated = NOW()
   ```

3. **Service 层优化**：
   - 简化 `convertAndSaveOrder` 方法
   - 移除查询逻辑，直接调用 `saveOrUpdate`
   - 保持原有的时间解析逻辑

4. **数据库索引**：
   - 创建 `uk_platform_ext_order_id` 唯一索引
   - 基于 `(platform, ext_order_id)` 组合字段
   - 提供重复数据清理脚本

### 技术优势
- **性能提升**：一条 SQL 完成操作，减少数据库交互
- **原子性**：避免并发情况下的数据不一致
- **简化代码**：移除复杂的查询判断逻辑
- **通用性**：适用于所有平台的订单数据处理

### 使用方式
```java
// 直接调用，自动判断新增或更新
originalPlatformOrderMapper.saveOrUpdate(originalPlatformOrder);
```

### 编码问题修复 ✅
#### 问题描述
在项目复制过程中，部分包含中文注释的文件出现了编码问题，显示为乱码字符（如"枚�"、"服务�"等）。

#### 修复的文件
1. **ApiConstants.java** - 修复了中文注释编码问题，更新了服务名称和前缀路径
2. **ErrorCodeConstants.java** - 修复了"枚举类"显示为"枚举�"的问题，更新了注释描述
3. **RedisKeyConstants.java** - 修复了"枚举类"的编码问题
4. **SecurityConfiguration.java** - 修复了"安全配置"的编码问题
5. **EnterpriseAdPlaceController.java** - 修复了控制器注释的编码问题

#### 修复方法
- 使用UTF-8编码重新保存文件
- 手动替换乱码字符为正确的中文字符
- 确保所有Java文件都使用UTF-8编码

#### 验证结果
- 所有中文注释现在正确显示
- 文件编码统一为UTF-8
- IDE不再报告编码错误

## 2025-07-23 优化Apifox自动化测试脚本
- 优化了YHT平台类目属性处理逻辑
- 根据不同type类型(multi_select, text, multi_value_measure, select, measure)生成对应的CommonAttributeValueVO值
- 增强了属性值生成的准确性和完整性

## 2025-07-23 优化MongoDB查询日志输出
- 修改MongoQueryLogConfig，使其输出可直接在Navicat中执行的MongoDB查询语句
- 支持find、aggregate、count等常用操作的标准格式转换
- 提供更友好的查询语句格式，便于调试和性能分析

## 2025-07-23 修复MongoDB _id字段自动生成问题
- 移除AttributeAggregationMongoService中手动设置_id字段的代码
- 让MongoDB自动生成ObjectId，避免_id字段冲突
- 修复YhtOption、XhsOption、KuaiShopOption、SphxdOption等实体类的id设置逻辑
- 确保数据插入时使用MongoDB标准的ObjectId格式

## 2025-07-24 完善FxgProductMappingHandler默认值设置逻辑
- 实现FxgProductMappingHandler中existPlatformProduct不为null时的默认值设置
- 为空字段设置合理的默认值，包括：
  - 基础信息：outerProductId、productType、name、shortProductName
  - 支付库存：payType、reduceType、freightId、deliveryDelayDay等
  - 购买限制：maximumPerOrder、limitPerBuyer、minimumPerOrder、deliveryMethod
  - 重量信息：weight、weightUnit
- 遵循DRY原则，避免重复的默认值设置逻辑

## 2025-07-24 实现抖店类目属性数据编辑对比操作
- 实现productFormatNew属性数据的智能合并逻辑
- 新增mergeProductFormatNew方法，优先保留现有数据
- 新增mergePropertyItems方法，处理属性值列表的合并
- 新增辅助方法：
  - generatePropertyItemKey：生成属性项唯一键用于去重
  - copyPropertyItem：深拷贝属性项对象
  - copyMeasureInfo：深拷贝度量信息
  - updatePropertyItemIfNeeded：智能更新属性项字段
- 支持属性的增删改查操作，确保数据一致性和完整性

## 2025-07-24 优化抖店度量信息合并逻辑
- 增强度量信息(MeasureInfo)的合并处理
- 新增mergeMeasureInfo方法，实现度量信息的细粒度合并
- 新增mergeMeasureValues方法，处理度量值列表的合并
- 新增辅助方法：
  - generateMeasureValueKey：生成度量值唯一键
  - copyMeasureValues：复制度量值列表
  - copyMeasureValue：复制单个度量值
  - updateMeasureValueIfNeeded：智能更新度量值字段
- 支持moduleId、unitId、prefix、suffix等度量信息字段的精确合并

## 2025-07-24 重构快手商品映射处理器的基础信息映射逻辑
- 参考FxgProductMappingHandler的processBasicInfoMapping方法
- 实现existPlatformProduct不为null时的智能合并逻辑
- 优先保留现有数据，为空字段设置默认值：
  - 基础信息：title、relItemId、shortTitle、sellingPoint
  - 业务配置：payWay、purchaseLimit、stockPartner、multipleStock、saleTimeFlag
  - 服务规则：完整的ServiceRule对象处理
- 新增辅助方法：
  - createDefaultServiceRule：创建默认服务规则
  - updateServiceRuleIfNeeded：智能更新服务规则
  - updateServicePromiseIfNeeded：智能更新服务承诺
- 确保快手平台商品数据的一致性和完整性

## 2025-07-24 重构快手商品映射处理器的类目属性映射逻辑
- 参考FxgProductMappingHandler的processAttributeMapping方法
- 实现类目属性的智能合并逻辑，优先保留现有数据
- 支持类目ID一致性检查，避免不必要的属性覆盖
- 新增属性合并方法：
  - mergeItemPropValues：合并商品属性值数组
  - copyItemPropValues：复制商品属性值数组
  - copyItemPropValue：深拷贝单个商品属性值
  - updateItemPropValueIfNeeded：智能更新属性值字段
- 新增复杂对象处理方法：
  - copyCategoryPropValueParam：复制类目属性值参数
  - copyCategoryPropValueParamList：复制类目属性值参数列表
  - copyDateRangeParam：复制日期范围参数
- 支持快手平台特有的属性结构：单选值、多选值、文本值、日期值、图片值等
- 确保属性数据的完整性和一致性

## 2025-07-24 重构小红书商品映射处理器
- 参考FxgProductMappingHandler实现processBasicInfoMapping智能合并逻辑
- 优先保留现有数据，为空字段设置默认值：
  - 基础信息：name、articleNo
  - 物流信息：shippingTemplateId、shippingGrossWeight
  - 售后信息：freeReturn、deliveryMode、isChannel
- 重构processAttributeMapping方法，实现属性智能合并
- 新增属性合并方法：
  - mergeAttributes：合并属性列表
  - copyAttributes：复制属性列表
  - copyAttribute：深拷贝单个属性
  - copyValueList：复制属性值列表
  - updateAttributeIfNeeded：智能更新属性字段
- 支持小红书平台特有的属性结构：propertyId、name、value、valueId、valueList

## 2025-07-24 重构视频号小店商品映射处理器
- 参考FxgProductMappingHandler实现processBasicInfoMapping智能合并逻辑
- 优先保留现有数据，为空字段设置默认值：
  - 基础信息：outProductId、title、subTitle、spuCode
  - 发货配置：deliverMethod、listing
  - 复杂对象：expressInfo、extraService、sizeChart
- 新增默认对象创建方法：
  - createDefaultExpressInfo：创建默认运费模板信息
  - createDefaultExtraService：创建默认额外服务信息
  - createDefaultSizeChart：创建默认尺码表信息
- 新增对象更新方法：
  - updateExpressInfoIfNeeded：智能更新运费模板信息
  - updateExtraServiceIfNeeded：智能更新额外服务信息
  - updateSizeChartIfNeeded：智能更新尺码表信息
- 重构processAttributeMapping方法，实现属性智能合并
- 新增类目处理方法：
  - getCurrentCategoryId：获取当前类目ID（支持新旧类目结构）
  - setCategoryId：设置类目ID（同时设置新旧类目结构）
- 新增属性合并方法：
  - mergeAttrs：合并属性列表
  - copyAttrs：复制属性列表
  - copyAttr：深拷贝单个属性
  - updateAttrIfNeeded：智能更新属性字段
- 支持视频号小店特有的属性结构：attrKey、attrValue

## 2024-07-17 供应商产品仓储层实现

实现了 `SupplierProductRepository` 类中的 `addProduct` 方法，用于将供应商产品聚合根对象持久化到数据库中。该方法实现了以下功能：

1. 保存供应商产品基本信息
2. 保存供应商产品额外信息
3. 保存供应商产品图片信息
4. 保存供应商产品类目属性
5. 保存供应商产品SKU信息
6. 保存供应商平台产品关联信息
7. 保存供应商分销商关系

每个步骤都进行了非空检查，确保代码的健壮性。方法最终返回包含产品ID的值对象 `SupplierProductIdVO`。

## 2024-07-17 修复 SupplierProductAggregate 问题

1. 为 `SupplierProductAggregate` 类添加了 Lombok 的 `@Getter` 和 `@Setter` 注解，自动生成 getter 和 setter 方法
2. 修复了 `SupplierProductRepository.addProduct()` 方法中字段名不匹配的问题：
   - 将 `getSupplierProduct()` 改为 `getProductSpu()`
   - 将 `getSupplierProductImages()` 改为 `getImages()`
   - 将 `getSupplierProductExtra()` 改为 `getExtras()`（并修改了对应的集合处理逻辑）
   - 将 `getSupplierProductCategoryAttributes()` 改为 `getCategoryAttributes()`
   - 将 `getSupplierProductSkus()` 改为 `getProductSkus()`
   - 将 `getSupplierPlatformProducts()` 改为 `getPlatformProducts()`
   - 移除了之前版本中不存在的供应商分销商关系处理代码

## 2024-07-17 优化 SupplierProductRepository 实现

1. 使用 `SupplierProductConvert` 将 entity 对象和 VO 对象转换为 PO 对象
2. 使用 `CommonStreamUtil.transList` 进行批量转换处理
3. 对 SupplierProductConvert 接口增加了所需的转换方法：
   - `toProductExtraPO`: 将 VO 对象转换为 PO
   - `toProductImagePO`: 将 VO 对象转换为 PO
   - `toProductCategoryAttributePO`: 将 VO 对象转换为 PO
   - `toPlatformProductPO`: 将 Entity 对象转换为 PO
4. 优化了保存逻辑，先进行批量转换，再进行批量插入，提高了代码的可读性和性能

## 2024-07-17 修复 SupplierProductIdVO 构造函数问题

1. 为 `SupplierProductIdVO` 类添加了接收 Long 类型参数的构造函数，使得可以通过 `new SupplierProductIdVO(productId)` 来创建实例
2. 添加了 `@NoArgsConstructor` 注解，保留无参构造函数，确保兼容性

## 2024-07-17 完善 SupplierProductIdVO 返回信息

1. 修改了 `addProduct` 方法的返回处理，不再简单返回只包含ID的值对象
2. 完善 `SupplierProductIdVO` 对象的信息，包括：
   - productId: 产品ID
   - enterpriseId: 企业ID
   - origin: 来源
   - oriProductId: 原始产品ID
   - skuIdList: SKU ID列表
3. 增加了对 `SupplierProductSkuIdVO` 对象的完整填充：
   - skuId: SKU ID
   - productSkuOuterId: SKU自编码
   - productSkuBarCode: SKU条形码
4. 确保返回给上层应用的领域对象包含完整的业务信息 

## 2024-07-18 创建Cursor-Rules项目规范

1. 基于项目结构和DDD架构，创建了三层架构的cursor-rules规范：
   - 架构分层规范：
     - 应用层(Application)：处理用户请求、协调业务逻辑
     - 领域层(Domain)：核心业务逻辑、领域模型
     - 基础设施层(Infrastructure)：技术实现、持久化
   - 代码规范：
     - Java编码规范
     - Vue编码规范 
     - TypeScript编码规范
   - 项目结构规范：
     - 后端服务模块结构
     - 前端模块结构
     - 公共模块结构
     - 微服务命名规范
2. 规范包含了具体的命名约定、职责划分和最佳实践
3. 规范文件保存在项目根目录下的`.cursor/rules.json`文件中 

## 2025-07-17 实现基础的供应商产品管理功能

### 新增内容：
1. 创建供应商产品新增请求DTO：`AddSupplierProductRequest`
2. 创建供应商产品新增响应DTO：`AddSupplierProductResponse`
3. 实现`SupplierProductBizService`业务服务类，处理供应商产品新增功能
4. 实现`SupplierProductController`控制器，提供供应商产品新增接口

### 功能说明：
- 基于DDD架构设计，实现了供应商产品的新增功能
- 业务层完成对领域层的编排和调用，处理产品新增请求
- 控制层提供RESTful API接口，接收前端请求并返回处理结果
- 使用`@Validated`进行请求参数校验，确保数据完整性

### 待优化项：
- `buildSupplierProductAggregate`方法需要根据实际的`SupplierProductAggregate`结构完善
- 后续可添加产品编辑、查询、删除等功能 

## 2025年7月17日更新

创建了抖店商品创建API的请求对象:

- 新增 `DoudianProductAddRequest.java` 类，用于抖店API商品创建请求
- 该类基于抖店开放平台API文档 `/product/add` 接口
- 使用Jackson注解映射JSON字段名，同时保持Java代码中的驼峰命名
- 添加了内部类来表示商品规格、SKU和属性
- 按照API文档添加了必要的字段注释和验证注解 

## 2025年7月18日更新

完善抖店商品发布API请求对象：

- 完整实现了`DoudianProductAddRequest.java`类，用于抖店/product/addV2接口
- 基于抖店开放平台最新API文档，涵盖了所有请求参数
- 使用Jackson的@JsonProperty注解实现JSON属性名与Java驼峰命名的映射
- 创建了17个内部类来表示复杂的参数结构，如规格信息、SKU价格、售后服务等
- 为每个字段添加了详细的注释，包含字段含义和可选值
- 添加了支持新增的"虚拟商品设置是否订单展示核销去使用"(show_checkout_entry)参数
- 使用Lombok @Data注解简化代码，提高开发效率 

## 2025-07-17 实现供应商产品聚合查询

完成了 `SupplierProductRepository` 中的 `querySupplierProductAggregate` 方法实现，主要功能：
1. 根据供应商产品ID查询产品基本信息
2. 查询关联的产品额外信息、图片信息、类目属性、SKU信息和平台产品关联信息
3. 将所有信息组装成聚合对象返回

同时为以下Mapper接口添加了查询方法：
- SupplierProductMapper: 根据ID和状态查询产品基本信息
- SupplierProductExtraMapper: 根据产品ID查询产品额外信息
- SupplierProductImageMapper: 根据产品ID查询产品图片，并按顺序排列
- SupplierProductCategoryAttributeMapper: 根据产品ID查询产品类目属性
- SupplierProductSkuMapper: 根据产品ID查询产品SKU信息
- SupplierPlatformProductMapper: 根据产品ID查询平台产品关联信息

## 2025-07-17 优化供应商产品聚合查询

优化 `SupplierProductRepository` 中的 `querySupplierProductAggregate` 方法：
1. 添加布尔参数控制是否查询附属信息，包括：searchExtras、searchImages、searchCategoryAttributes、searchSkus
2. 更新接口定义，提供默认实现方法以兼容原有调用
3. 在实现中根据布尔参数决定是否查询相关表，提高查询效率
4. 移除了状态过滤条件，调整为仅根据产品ID进行查询
5. 移除了平台产品关联信息的查询，精简聚合对象内容 

## 2025-07-19 实现小红书商品映射处理器(XhsProductMappingHandler)

完成了 `XhsProductMappingHandler` 类的实现，根据小红书平台商品数据结构特点，实现了商品映射功能：

1. 完成 `processBasicInfoMapping` 方法：
   - 设置商品名称(name)
   - 设置类目ID(categoryId)
   - 设置商品货号(articleNo)
   - 配置物流相关参数，如运费模板、重量等
   - 设置售后和配送相关参数

2. 完成 `processSkuMapping` 方法：
   - 将供应商SKU信息转换为小红书平台SKU格式(XhsCreateSkuRequest)
   - 处理SKU价格，将元转换为分
   - 设置规格信息(variants)，支持多规格组合
   - 配置默认的发货时间信息
   - 收集并设置商品可选的规格类型(variantIds)

3. 完成 `processImageMapping` 方法：
   - 处理商品主图(images)
   - 处理详情图片(imageDescriptions)
   - 支持视频URL配置

4. 完成 `processAttributeMapping` 方法：
   - 处理品牌ID信息，支持直接设置和通过映射获取
   - 使用属性映射处理器转换平台特定属性
   - 支持多值属性的处理

5. 完成 `processExtrasMapping` 方法：
   - 处理商品描述文字
   - 处理常见问题(FAQ)信息
   - 支持英文名称等额外信息配置

此实现参考了FxgProductMappingHandler的逻辑结构，但根据XhsProductAggregate的特定字段进行了针对性处理，确保数据结构符合小红书平台API的要求。

## 2025-07-21 完成小红书属性映射处理器(XhsAttributeMappingHandler)

完成了 `XhsAttributeMappingHandler` 类中的 `transCommonValue2Platform` 方法实现，主要功能：

1. **属性映射查询**：
   - 根据类目ID和平台类目ID查询属性映射关系
   - 构建属性映射Map，便于快速查找映射关系

2. **属性转换逻辑**：
   - 遍历通用属性值列表，逐个转换为小红书平台属性格式
   - 支持属性映射，优先使用映射后的平台属性ID和名称
   - 如果没有映射关系，则直接使用原始属性ID

3. **属性值处理**：
   - 单个属性值：直接设置value和valueId字段
   - 多个属性值：使用valueList列表结构
   - 支持字符串值(name)和数字值(value)的转换

4. **辅助方法实现**：
   - `convertToXhsAttribute`: 将通用属性转换为小红书属性格式
   - `convertSingleAttributeValue`: 处理单个属性值的转换
   - `convertToValueListItem`: 将通用属性值转换为ValueList项

5. **设计原则遵循**：
   - 遵循DRY原则：避免重复代码，提取公共转换逻辑
   - 遵循KISS原则：保持方法简单明了，职责单一
   - 遵循SOLID原则：单一职责，每个方法只处理特定的转换逻辑
   - 空值安全：对所有可能为空的对象进行检查

此实现参考了FxgAttributeMappingHandler的转换模式，但针对小红书平台的XhsProductAggregate.Attribute结构进行了专门的适配处理。

## 2025-07-21 实现平台类目属性ID查询方法(getPlatformCategoryAttributeIds)

完成了 `CategoryAttributeRepository` 中的 `getPlatformCategoryAttributeIds` 方法实现，主要功能：

1. **缓存机制**：
   - 使用Redis缓存查询结果，提高查询性能
   - 缓存键格式：`categoryAttributes:attributeIds:{platform}:{platformCategoryLeafId}`
   - 设置缓存过期时间，避免数据过期问题

2. **多平台支持**：
   - YHT平台：查询 `attribute.property_id` 字段
   - FXG平台：查询 `attribute.property_id` 字段
   - KWAISHOP平台：查询 `attribute.propId` 字段
   - SPHXD平台：查询 `attribute.prop_id` 字段
   - XHS平台：查询 `attribute.id` 字段

3. **MongoDB聚合查询**：
   - 使用原生MongoDB聚合管道进行查询
   - 聚合步骤：匹配类目ID → 展开属性数组 → 投影属性ID → 分组去重 → 输出结果
   - 针对不同平台的属性ID字段名称差异进行适配

4. **聚合查询流程**：
   ```javascript
   // 以YHT平台为例的聚合管道
   [
     { $match: { "platformCategoryId": "categoryId" } },
     { $unwind: "$attribute" },
     { $project: { "attributeId": "$attribute.property_id" } },
     { $group: { "_id": null, "attributeIds": { $addToSet: "$attributeId" } } },
     { $project: { "attributeIds": 1 } }
   ]
   ```

5. **结果处理**：
   - 提取聚合查询结果中的属性ID列表
   - 过滤空值，确保返回有效的属性ID
   - 转换为字符串列表格式

6. **错误处理**：
   - 对不支持的平台类型进行警告日志记录
   - 空结果返回空列表，避免空指针异常
   - 完善的日志记录，便于问题排查

此实现遵循了DRY原则，将不同平台的查询逻辑抽取为独立方法，同时保持了KISS原则的简洁性。使用MongoDB原生聚合查询确保了高性能的数据检索。

## 2025-07-21 实现平台类目属性查询方法(不包含属性值数据)

完成了 `ICategoryAttributeRepository` 接口中的两个新方法实现，用于查询平台类目属性但不包含大量属性值数据：

1. **新增接口方法**：
   - `getPlatformAttributesByIds`: 根据属性ID列表获取平台类目属性
   - `getPlatformAttributesByCategory`: 根据平台类目ID获取平台类目属性

2. **多平台支持**：
   - YHT平台：排除 `options` 和 `measure_templates` 字段
   - FXG平台：排除 `options` 和 `measure_templates` 字段
   - KWAISHOP平台：排除 `prePropValues`、`unitProp` 和 `propInputConfig` 字段
   - SPHXD平台：排除 `value` 字段
   - XHS平台：排除 `attributeValue` 字段

3. **查询优化**：
   - 使用MongoDB的字段投影功能，只查询需要的字段
   - 实现Redis缓存机制，提高查询性能
   - 针对不同平台的ID字段类型进行适配处理

4. **使用示例**：
   ```java
   // 示例1：根据平台类目ID获取属性列表（不含属性值数据）
   List<XhsAttribute> xhsAttributes = categoryAttributeRepository.getPlatformAttributesByCategory(
       ErpPlatformTypeEnum.XHS.getValue(), platformCategoryLeafId);

   // 示例2：根据属性ID列表获取属性列表（不含属性值数据）
   List<String> platformCategoryAttributeIds = categoryAttributeRepository.getPlatformCategoryAttributeIds(
       getPlatform(), platformCategoryLeafId);
   List<XhsAttribute> xhsAttributes = categoryAttributeRepository.getPlatformAttributesByIds(
       ErpPlatformTypeEnum.XHS.getValue(), platformCategoryAttributeIds);

   // 示例3：结合属性映射使用
   List<String> platformCategoryAttributeIds = categoryAttributeRepository.getPlatformCategoryAttributeIds(
       getPlatform(), platformCategoryLeafId);
   // 去重过滤一次
   attributeMappings = CommonStreamUtil.filter(attributeMappings,
       attributeMapping -> platformCategoryAttributeIds.contains(attributeMapping.getPlatformAttributeId()));
   // 获取属性详情（不含属性值数据）
   List<XhsAttribute> xhsAttributes = categoryAttributeRepository.getPlatformAttributesByIds(
       ErpPlatformTypeEnum.XHS.getValue(), platformCategoryAttributeIds);
   ```

5. **性能优化**：
   - 通过排除大字段减少数据传输量和内存占用
   - 使用Redis缓存减少数据库查询次数
   - 针对快手平台的Long类型ID进行特殊处理

此实现遵循了SOLID原则中的单一职责原则和开闭原则，为不同平台提供了统一的接口，同时保持了代码的可扩展性。

## 2025-07-21 完善小红书属性映射处理器的选项值映射功能

完成了 `XhsAttributeMappingHandler` 中基于属性类型的选项值映射逻辑，主要功能：

1. **属性类型判断**：
   - YHT平台：通过 `type` 字段判断（select、multi_select为选项类型）
   - XHS平台：通过 `inputType` 字段判断（1-单选、2-多选为选项类型）
   - 只有选项类型的属性才进行选项值映射

2. **选项值映射逻辑**：
   - 查询 `AttributeOptionMappingEntity` 获取选项映射关系
   - 如果找到映射关系，使用映射后的选项值和选项ID
   - 如果没有找到映射关系，记录警告日志并跳过该选项值
   - 确保只有有效映射的选项值才会被设置到最终属性中

3. **新增接口方法**：
   - `findOptionMappingByAttributeId`: 根据属性ID查询所有选项映射
   - `findOptionMappingByOptionId`: 根据选项ID查询单个选项映射
   - 创建了 `AttributeOptionMappingMapper` 接口用于数据访问

4. **处理流程优化**：
   ```java
   // 判断属性类型
   boolean isOptionType = isOptionAttribute(yhtAttribute, xhsAttribute);

   if (isOptionType && mapping != null) {
       // 选项类型属性，进行选项值映射
       processOptionAttributeValues(attribute, attributeValues, mapping);
   } else {
       // 非选项类型属性，直接转换值
       processNonOptionAttributeValues(attribute, attributeValues);
   }
   ```

5. **选项映射查询**：
   ```java
   // 查询选项映射关系
   AttributeOptionMappingEntity optionMapping = categoryAttributeRepository.findOptionMappingByOptionId(
       getPlatform(),
       mapping.getAttributeId(),
       mapping.getPlatformAttributeId(),
       commonValue.getName() // 使用name作为选项ID
   );
   ```

6. **错误处理机制**：
   - 对于没有映射关系的选项值，记录警告日志
   - 如果所有选项值都没有映射关系，属性值保持为空
   - 确保数据的完整性和可追溯性

7. **数据结构适配**：
   - 单个选项值：设置到 `value` 和 `valueId` 字段
   - 多个选项值：设置到 `valueList` 列表
   - 兼容小红书平台的数据结构要求

此实现确保了属性值映射的准确性和可靠性，只有在存在明确映射关系的情况下才会进行选项值转换，避免了数据错误和不一致的问题。

## 2025-07-21 完成快手商品映射处理器基础信息映射(KuaiShopProductMappingHandler)

参考XhsProductMappingHandler的实现，完成了 `KuaiShopProductMappingHandler` 中的 `processBasicInfoMapping` 方法，主要功能：

1. **基础商品信息映射**：
   - 商品名称：映射到 `title` 字段
   - 外部商品ID：映射到 `relItemId` 字段（Long类型，支持数字转换和哈希码备用方案）
   - 类目ID：映射到 `categoryId` 字段（Long类型，包含错误处理）
   - 商品短标题：映射到 `shortTitle` 字段
   - 商品卖点：映射到 `sellingPoint` 字段
   - 商品备注：映射到 `itemRemark` 字段

2. **服务规则配置**：
   ```java
   KuaiShopProductAggregate.ServiceRule serviceRule = new KuaiShopProductAggregate.ServiceRule();
   serviceRule.setPromiseDeliveryTime(48L * 60 * 60 * 1000); // 48小时内发货
   serviceRule.setDeliveryMethod("1"); // 物流发货
   serviceRule.setRefundRule("1"); // 支持7天无理由退货
   ```

3. **服务承诺设置**：
   ```java
   KuaiShopProductAggregate.OpenApiServicePromise servicePromise = new KuaiShopProductAggregate.OpenApiServicePromise();
   servicePromise.setBrokenRefund(true); // 破损包赔
   serviceRule.setServicePromise(servicePromise);
   ```

4. **平台特定配置**：
   - 限购设置：默认不限购 (`purchaseLimit = false`)
   - 支付方式：默认在线支付 (`payWay = 1`)
   - 分仓模式：默认不分仓 (`stockPartner = false`)
   - 多仓库存：默认单仓模式 (`multipleStock = false`)
   - 定点开售：默认立即开售 (`saleTimeFlag = false`)

5. **数据类型处理**：
   - Long类型字段的安全转换（relItemId、categoryId）
   - 数字格式异常的备用处理方案
   - 完善的错误日志记录

6. **参考实现对比**：
   ```java
   // XHS平台实现
   xhsProductAggregate.setName(productSpu.getProductFullName());
   xhsProductAggregate.setCategoryId(categoryMapping.getPlatformCategoryLeafId());

   // 快手平台实现
   platformProduct.setTitle(productSpu.getProductFullName());
   platformProduct.setCategoryId(Long.parseLong(categoryMapping.getPlatformCategoryLeafId()));
   ```

7. **错误处理机制**：
   - 对于数字转换异常提供备用方案
   - 记录详细的错误日志便于问题排查
   - 确保程序的健壮性和稳定性

此实现遵循了DRY原则，复用了XhsProductMappingHandler的设计模式，同时针对快手平台的特定数据结构（如Long类型的ID字段）进行了适配处理。

## 2025-07-21 完成快手商品映射处理器SKU和图片映射(KuaiShopProductMappingHandler)

完成了 `KuaiShopProductMappingHandler` 中的 `processSkuMapping` 和 `processImageMapping` 方法实现，主要功能：

### 1. SKU映射处理 (processSkuMapping)

**基础SKU信息映射**：
- 外部SKU ID：`relSkuId` (Long类型，支持数字转换和哈希码备用方案)
- 库存数量：`skuStock` (默认为0)
- 销售价格：`skuSalePrice` (转换为分，乘以100)
- SKU别名：`skuNick`
- 条形码：`barcode`
- 重量：`weight` (转换为克，乘以1000，默认500克)

**SKU属性处理**：
```java
// 解析SKU属性字符串 "颜色:红色;尺寸:L"
String[] propPairs = sku.getProductSkuProps().split(";");
for (String propPair : propPairs) {
    String[] keyValue = propPair.split(":");
    if (keyValue.length == 2) {
        KuaiShopProductAggregate.OpenApiAddSkuPropDTO propDTO = new KuaiShopProductAggregate.OpenApiAddSkuPropDTO();
        propDTO.setPropName(keyValue[0]);      // 属性名
        propDTO.setPropValueName(keyValue[1]); // 属性值
        propDTO.setImageUrl(sku.getImageUrl()); // 规格图片
        propDTO.setIsMainProp(1);              // 主属性标识
    }
}
```

**数据类型安全转换**：
- Long类型字段的安全转换（relSkuId）
- BigDecimal到Integer的价格转换（分为单位）
- 重量单位转换（千克转克）
- 数字格式异常的备用处理方案

### 2. 图片映射处理 (processImageMapping)

**主图处理**：
```java
List<String> mainImages = images.stream()
    .filter(image -> StringUtils.isNotBlank(image.getUrl())
            && image.getImageType().equals(ProductImageTypeEnum.MAIN.getId()))
    .map(SupplierProductImageVO::getUrl)
    .collect(Collectors.toList());
platformProduct.setImageUrls(mainImages.toArray(new String[0]));
```

**详情图处理**：
```java
List<String> detailImages = images.stream()
    .filter(image -> StringUtils.isNotBlank(image.getUrl())
            && image.getImageType().equals(ProductImageTypeEnum.DESCRIPTION.getId()))
    .map(SupplierProductImageVO::getUrl)
    .collect(Collectors.toList());
platformProduct.setDetailImageUrls(detailImages);
```

**特殊图片处理**：
- 3:4主图 → 白底图 (`whiteBaseImageUrl`)
- 详情图 → HTML格式商品详情 (`details`)

**HTML详情生成**：
```java
StringBuilder detailsBuilder = new StringBuilder();
for (String imageUrl : detailImages) {
    detailsBuilder.append("<img src=\"").append(imageUrl).append("\" />");
}
platformProduct.setDetails(detailsBuilder.toString());
```

### 3. 平台差异适配

| 功能 | XHS平台 | 快手平台 | 处理方式 |
|------|---------|----------|----------|
| SKU ID | String | Long | 类型转换+备用方案 |
| 价格单位 | 分 | 分 | 统一处理 |
| 重量单位 | 克 | 克 | 单位转换 |
| 主图格式 | List<String> | String[] | 数组转换 |
| 详情处理 | 图片列表 | HTML字符串 | 格式转换 |

### 4. 错误处理机制

**数字转换异常**：
- 对于`relSkuId`：使用哈希码作为备用方案
- 记录警告日志，便于问题排查
- 确保程序继续执行

**空值安全**：
- 所有集合操作都进行非空检查
- 提供合理的默认值（重量500克、库存0等）
- 字符串字段进行非空验证

### 5. 设计模式遵循

**DRY原则**：
- 复用XhsProductMappingHandler的处理模式
- 提取公共的图片过滤和转换逻辑

**KISS原则**：
- 每个方法职责单一，逻辑清晰
- 避免过度复杂的嵌套处理

**SOLID原则**：
- 单一职责：SKU映射和图片映射分离
- 开闭原则：易于扩展新的图片类型处理

此实现确保了快手平台商品数据的完整性和准确性，同时保持了良好的代码可维护性和扩展性。

## 2025-07-21 完成快手属性映射处理器属性值转换(KuaiShopAttributeMappingHandler)

参考XhsAttributeMappingHandler的实现，完成了 `KuaiShopAttributeMappingHandler` 中的 `transCommonValue2Platform` 方法，主要功能：

### 1. 属性映射处理流程

**基础流程**：
```java
// 1. 获取属性映射关系
List<AttributeMappingEntity> attributeMappings = categoryAttributeRepository.findMappingByPlatformAndCategoryLeafId(getPlatform(), categoryLeafId, platformCategoryLeafId);

// 2. 获取平台类目属性ID列表
List<String> platformCategoryAttributeIds = categoryAttributeRepository.getPlatformCategoryAttributeIds(getPlatform(), platformCategoryLeafId);

// 3. 过滤有效的属性映射
attributeMappings = CommonStreamUtil.filter(attributeMappings, attributeMapping -> platformCategoryAttributeIds.contains(attributeMapping.getPlatformAttributeId()));

// 4. 构建映射Map
Map<String, AttributeMappingEntity> attributeMappingMap = CommonStreamUtil.toMap(attributeMappings, AttributeMappingEntity::getAttributeId, (e1, e2) -> e1);
```

**属性信息获取**：
```java
// 获取源汇通类目属性
List<YhtAttribute> yhtAttributes = categoryAttributeRepository.getPlatformAttributesByIds(ErpPlatformTypeEnum.YHT.getValue(), new ArrayList<>(attributeMappingMap.keySet()));

// 获取快手类目属性
List<KuaiShopAttribute> kuaiShopAttributes = categoryAttributeRepository.getPlatformAttributesByIds(getPlatform(), platformCategoryAttributeIds);
```

### 2. 属性类型判断

**选项类型识别**：
```java
private boolean isOptionAttribute(YhtAttribute yhtAttribute, KuaiShopAttribute kuaiShopAttribute) {
    // YHT平台：select、multi_select为选项类型
    if (yhtAttribute != null && StringUtils.isNotBlank(yhtAttribute.getType())) {
        String yhtType = yhtAttribute.getType();
        if ("select".equals(yhtType) || "multi_select".equals(yhtType)) {
            return true;
        }
    }

    // 快手平台：RADIO、CHECKBOX为选项类型
    if (kuaiShopAttribute != null && StringUtils.isNotBlank(kuaiShopAttribute.getPropInputType())) {
        String inputType = kuaiShopAttribute.getPropInputType();
        if ("RADIO".equals(inputType) || "CHECKBOX".equals(inputType)) {
            return true;
        }
    }

    return false;
}
```

### 3. 选项值映射处理

**选项映射查询**：
```java
AttributeOptionMappingEntity optionMapping = categoryAttributeRepository.findOptionMappingByOptionId(
    getPlatform(),
    mapping.getAttributeId(),
    mapping.getPlatformAttributeId(),
    commonValue.getName() // 使用name作为选项ID
);
```

**选项值转换**：
```java
if (optionMapping != null) {
    KuaiShopProductAggregate.CategoryPropValueParam valueParam = new KuaiShopProductAggregate.CategoryPropValueParam();
    valueParam.setPropValue(optionMapping.getPlatformOptionName());
    valueParam.setPropValueId(Long.parseLong(optionMapping.getPlatformAttributeOptionId()));
    mappedValues.add(valueParam);
}
```

**单选/多选处理**：
```java
String inputType = kuaiShopAttribute.getPropInputType();
if ("RADIO".equals(inputType) && mappedValues.size() == 1) {
    // 单选属性，设置到radioPropValue
    attribute.setRadioPropValue(mappedValues.get(0));
} else if ("CHECKBOX".equals(inputType)) {
    // 多选属性，设置到checkBoxPropValuesList
    attribute.setCheckBoxPropValuesList(mappedValues);
}
```

### 4. 非选项类型处理

**多种输入类型支持**：
```java
switch (inputType) {
    case "TEXT":
        attribute.setTextPropValue(firstValue.getName());
        break;
    case "DATETIME":
        Long timestamp = Long.parseLong(firstValue.getValue().toString());
        attribute.setDatetimeTimestamp(timestamp);
        break;
    case "IMAGE":
        List<String> imageUrls = new ArrayList<>();
        for (CommonAttributeValueVO value : attributeValues) {
            if (StringUtils.isNotBlank(value.getName())) {
                imageUrls.add(value.getName());
            }
        }
        attribute.setImagePropValues(imageUrls);
        break;
}
```

### 5. 平台差异适配

| 属性类型 | YHT平台 | 快手平台 | 处理方式 |
|----------|---------|----------|----------|
| 单选 | select | RADIO | 映射到radioPropValue |
| 多选 | multi_select | CHECKBOX | 映射到checkBoxPropValuesList |
| 文本 | text | TEXT | 映射到textPropValue |
| 图片 | - | IMAGE | 映射到imagePropValues |
| 日期 | measure | DATETIME | 映射到datetimeTimestamp |

### 6. 输入类型映射

```java
private Integer mapInputType(String propInputType) {
    switch (propInputType) {
        case "TEXT": return 1;     // 文本
        case "RADIO": return 2;    // 单选
        case "CHECKBOX": return 3; // 多选
        case "IMAGE": return 4;    // 图片
        case "DATETIME": return 5; // 日期时间
        default: return 1;         // 默认文本
    }
}
```

### 7. 错误处理机制

**映射缺失处理**：
- 对于没有属性映射关系的属性，记录警告并跳过
- 对于没有选项映射关系的选项值，记录警告并跳过
- 确保只有有效映射的数据才会被使用

**数据类型转换**：
- Long类型ID的安全转换
- 时间戳格式的容错处理
- 提供合理的默认值

**日志记录**：
- 详细的警告日志，便于问题排查
- 记录映射失败的具体信息

此实现遵循了DRY原则，复用了XhsAttributeMappingHandler的设计模式，同时针对快手平台的特定数据结构（如Long类型的ID字段、不同的输入类型枚举）进行了专门的适配处理。

## 2025-07-21 优化店铺访问令牌更新方法(ShopInfoMapper)

使用MyBatis-Plus的LambdaUpdateWrapper优化了店铺访问令牌的更新逻辑，主要改进：

### 1. 新增Mapper方法

**ShopInfoMapper.updateAccessTokenInfo**：
```java
default int updateAccessTokenInfo(Long shopId, String accessToken, String refreshToken, Date expiredDate) {
    LambdaUpdateWrapper<ShopInfoPO> updateWrapper = new LambdaUpdateWrapper<>();
    updateWrapper.eq(ShopInfoPO::getShopId, shopId)
            .set(ShopInfoPO::getAccessToken, accessToken)
            .set(ShopInfoPO::getRefreshToken, refreshToken)
            .set(ShopInfoPO::getExpiredDate, expiredDate);
    return update(null, updateWrapper);
}
```

### 2. 优化Repository实现

**ShopInfoRepository.updateAccessToken**：
```java
@Override
@Transactional
public void updateAccessToken(Long enterpriseId, Long shopId, String accessToken, String refreshToken, Integer expiresInSeconds) {
    Date expiredDate = DateUtil.offsetSecond(new Date(), expiresInSeconds);
    int updateCount = shopInfoMapper.updateAccessTokenInfo(shopId, accessToken, refreshToken, expiredDate);
    if (updateCount == 0) {
        log.warn("更新店铺访问令牌失败，店铺ID: {}", shopId);
    }
}
```

### 3. 性能优化对比

| 方面 | 原实现 | 优化后实现 |
|------|--------|------------|
| 数据库操作 | 2次（SELECT + UPDATE） | 1次（UPDATE） |
| 网络传输 | 查询完整记录 | 只更新指定字段 |
| 内存占用 | 需要加载完整对象 | 无需加载对象 |
| 并发安全 | 可能存在并发问题 | 原子性更新 |

### 4. 技术优势

**MyBatis-Plus LambdaUpdateWrapper优势**：
- **类型安全**：使用Lambda表达式避免字段名拼写错误
- **性能优化**：直接执行UPDATE语句，避免先查询再更新
- **原子性**：单次数据库操作，避免并发问题
- **精确更新**：只更新需要的字段，减少数据传输

**SQL生成示例**：
```sql
UPDATE yt_shop_info
SET access_token = ?, refresh_token = ?, expired_date = ?
WHERE shop_id = ?
```

### 5. 错误处理

**更新结果检查**：
- 检查更新影响的行数
- 记录更新失败的警告日志
- 便于问题排查和监控

### 6. 设计模式遵循

**SOLID原则**：
- 单一职责：方法只负责更新访问令牌相关字段
- 开闭原则：易于扩展其他字段的更新方法

**DRY原则**：
- 避免重复的查询-更新模式
- 提供可复用的更新方法

此优化显著提升了访问令牌更新的性能和安全性，同时保持了代码的简洁性和可维护性。

## 2025-07-21 完成视频号小店商品映射处理器基础信息映射(SphxdProductMappingHandler)

参考XhsProductMappingHandler的实现，完成了 `SphxdProductMappingHandler` 中的 `processBasicInfoMapping` 方法，主要功能：

### 1. 基础商品信息映射

**核心字段映射**：
```java
// 外部商品ID
sphxdProductAggregate.setOutProductId(productSpu.getProductOuterId());

// 商品标题和副标题
sphxdProductAggregate.setTitle(productSpu.getProductFullName());
sphxdProductAggregate.setSubTitle(productSpu.getProductName());

// 商家自定义商品编码
sphxdProductAggregate.setSpuCode(productSpu.getProductOuterId());
```

### 2. 类目信息设置

**新旧类目结构兼容**：
```java
if (Objects.nonNull(categoryMapping) && StringUtils.isNotBlank(categoryMapping.getPlatformCategoryLeafId())) {
    // 设置新类目结构（推荐使用）
    SphxdProductAggregate.CatV2 catV2 = new SphxdProductAggregate.CatV2();
    catV2.setCatId(categoryMapping.getPlatformCategoryLeafId());
    sphxdProductAggregate.setCatsV2(List.of(catV2));

    // 同时设置旧类目结构（兼容性）
    SphxdProductAggregate.Cat cat = new SphxdProductAggregate.Cat();
    cat.setCatId(categoryMapping.getPlatformCategoryLeafId());
    sphxdProductAggregate.setCats(List.of(cat));
}
```

### 3. 物流和发货配置

**发货方式设置**：
```java
// 设置发货方式（默认快递发货）
sphxdProductAggregate.setDeliverMethod(0); // 0-快递发货

// 设置运费模板信息
SphxdProductAggregate.ExpressInfo expressInfo = new SphxdProductAggregate.ExpressInfo();
expressInfo.setTemplateId(""); // 运费模板ID需要实际业务设置
expressInfo.setWeight(500.0); // 默认重量500克
sphxdProductAggregate.setExpressInfo(expressInfo);
```

### 4. 售后服务配置

**额外服务设置**：
```java
SphxdProductAggregate.ExtraService extraService = new SphxdProductAggregate.ExtraService();
extraService.setSevenDayReturn(1); // 支持7天无理由退货
extraService.setFreightInsurance(0); // 不支持运费险
extraService.setFakeOnePayThree(0); // 不支持假一赔三
extraService.setDamageGuarantee(1); // 支持坏损包退
sphxdProductAggregate.setExtraService(extraService);
```

**售后说明**：
```java
if (StringUtils.isNotBlank(productSpu.getProductDesc())) {
    sphxdProductAggregate.setAfterSaleDesc(productSpu.getProductDesc());
} else {
    sphxdProductAggregate.setAfterSaleDesc("支持7天无理由退货");
}
```

### 5. 平台特定配置

**上架和尺码表设置**：
```java
// 设置是否立即上架（默认不立即上架，需要审核）
sphxdProductAggregate.setListing(0); // 0-否，1-是

// 设置尺码表信息（默认不启用）
SphxdProductAggregate.SizeChart sizeChart = new SphxdProductAggregate.SizeChart();
sizeChart.setEnable(false);
sphxdProductAggregate.setSizeChart(sizeChart);
```

### 6. 平台差异适配

| 功能 | XHS平台 | 视频号小店平台 | 处理方式 |
|------|---------|----------------|----------|
| 商品名称 | name | title | 直接映射 |
| 商品货号 | articleNo | outProductId | 直接映射 |
| 类目ID | categoryId (String) | cats/catsV2 (List) | 构建类目对象列表 |
| 配送方式 | deliveryMode | deliverMethod | 枚举值映射 |
| 售后服务 | 简单字段 | 复杂嵌套对象 | 构建嵌套结构 |

### 7. 业务逻辑设计

**默认值策略**：
- 发货方式：默认快递发货（0）
- 商品重量：默认500克
- 售后服务：支持7天无理由退货和坏损包退
- 上架状态：默认不立即上架，需要审核
- 尺码表：默认不启用

**兼容性处理**：
- 同时设置新旧类目结构，确保API兼容性
- 提供合理的默认值，避免必填字段为空
- 支持可选字段的灵活配置

### 8. 错误处理

**空值安全**：
- 对所有可能为空的字段进行检查
- 提供合理的默认值和备用方案
- 确保必填字段都有有效值

此实现遵循了DRY原则，复用了XhsProductMappingHandler的设计模式，同时针对视频号小店平台的特定数据结构（如类目列表、复杂的服务配置）进行了专门的适配处理。

## 2025-07-21 完成视频号小店商品映射处理器SKU和图片映射(SphxdProductMappingHandler)

完成了 `SphxdProductMappingHandler` 中的 `processSkuMapping` 和 `processImageMapping` 方法实现，主要功能：

### 1. SKU映射处理 (processSkuMapping)

**基础SKU信息映射**：
```java
SphxdProductAggregate.Sku sphxdSku = new SphxdProductAggregate.Sku();
sphxdSku.setOutSkuId(sku.getProductSkuOuterId());     // 外部SKU ID
sphxdSku.setSkuCode(sku.getProductSkuBarCode());      // SKU编码
sphxdSku.setSalePrice(sku.getSalePrice().multiply(new BigDecimal("100")).intValue()); // 价格转分
sphxdSku.setStockNum(sku.getStock() != null ? sku.getStock() : 0); // 库存
sphxdSku.setThumbImg(sku.getImageUrl());              // SKU小图
```

**SKU属性处理**：
```java
// 解析SKU属性字符串 "颜色:红色;尺寸:L"
List<SphxdProductAggregate.SkuAttr> skuAttrs = new ArrayList<>();
if (StringUtils.isNotBlank(sku.getProductSkuProps())) {
    String[] propPairs = sku.getProductSkuProps().split(";");
    for (String propPair : propPairs) {
        String[] keyValue = propPair.split(":");
        if (keyValue.length == 2) {
            SphxdProductAggregate.SkuAttr skuAttr = new SphxdProductAggregate.SkuAttr();
            skuAttr.setAttrKey(keyValue[0]);    // 属性键
            skuAttr.setAttrValue(keyValue[1]);  // 属性值
            skuAttrs.add(skuAttr);
        }
    }
}
sphxdSku.setSkuAttrs(skuAttrs);
```

**SKU发货信息**：
```java
SphxdProductAggregate.SkuDeliverInfo deliverInfo = new SphxdProductAggregate.SkuDeliverInfo();
deliverInfo.setStockType(0); // 0:现货（默认）
sphxdSku.setSkuDeliverInfo(deliverInfo);
```

### 2. 图片映射处理 (processImageMapping)

**主图处理**：
```java
List<String> mainImages = images.stream()
    .filter(image -> StringUtils.isNotBlank(image.getUrl())
            && image.getImageType().equals(ProductImageTypeEnum.MAIN.getId()))
    .map(SupplierProductImageVO::getUrl)
    .collect(Collectors.toList());

if (!mainImages.isEmpty()) {
    platformProduct.setHeadImgs(mainImages);
}
```

**详情图处理**：
```java
List<String> detailImages = images.stream()
    .filter(image -> StringUtils.isNotBlank(image.getUrl())
            && image.getImageType().equals(ProductImageTypeEnum.DESCRIPTION.getId()))
    .map(SupplierProductImageVO::getUrl)
    .collect(Collectors.toList());

if (!detailImages.isEmpty()) {
    SphxdProductAggregate.DescInfo descInfo = new SphxdProductAggregate.DescInfo();
    descInfo.setImgs(detailImages);
    descInfo.setDesc(platformProduct.getAfterSaleDesc()); // 设置描述文本
    platformProduct.setDescInfo(descInfo);
}
```

**资质图片处理**：
```java
List<String> qualificationImages = images.stream()
    .filter(image -> StringUtils.isNotBlank(image.getUrl())
            && image.getImageType().equals(ProductImageTypeEnum.QUALIFICATION.getId()))
    .map(SupplierProductImageVO::getUrl)
    .collect(Collectors.toList());

if (!qualificationImages.isEmpty()) {
    List<SphxdProductAggregate.ProductQuaInfo> productQuaInfos = new ArrayList<>();
    SphxdProductAggregate.ProductQuaInfo quaInfo = new SphxdProductAggregate.ProductQuaInfo();
    quaInfo.setQuaId("default"); // 默认资质ID
    quaInfo.setQuaUrl(qualificationImages);
    productQuaInfos.add(quaInfo);
    platformProduct.setProductQuaInfos(productQuaInfos);
}
```

### 3. 平台差异适配

| 功能 | XHS平台 | 视频号小店平台 | 处理方式 |
|------|---------|----------------|----------|
| SKU ID | erpCode (String) | outSkuId (String) | 直接映射 |
| 价格单位 | 分 | 分 | 统一处理 |
| SKU属性 | Variant对象 | SkuAttr对象 | 结构转换 |
| 主图 | images (List<String>) | headImgs (List<String>) | 直接映射 |
| 详情图 | imageDescriptions (List<String>) | DescInfo.imgs (List<String>) | 嵌套对象 |
| 资质图 | 不支持 | ProductQuaInfo (List) | 新增支持 |

### 4. 数据结构对比

**SKU结构差异**：
```java
// XHS平台
XhsProductAggregate.XhsCreateSkuRequest skuRequest = new XhsProductAggregate.XhsCreateSkuRequest();
skuRequest.setVariants(variants); // 规格变体列表

// 视频号小店平台
SphxdProductAggregate.Sku sphxdSku = new SphxdProductAggregate.Sku();
sphxdSku.setSkuAttrs(skuAttrs); // SKU属性列表
```

**图片结构差异**：
```java
// XHS平台
platformProduct.setImages(mainImages);           // 主图
platformProduct.setImageDescriptions(detailImages); // 详情图

// 视频号小店平台
platformProduct.setHeadImgs(mainImages);         // 主图
SphxdProductAggregate.DescInfo descInfo = new SphxdProductAggregate.DescInfo();
descInfo.setImgs(detailImages);                  // 详情图（嵌套结构）
platformProduct.setDescInfo(descInfo);
```

### 5. 业务逻辑设计

**默认值策略**：
- SKU库存：默认为0
- 发货类型：默认现货（0）
- 资质ID：默认"default"
- 价格转换：元转分（乘以100）

**数据安全**：
- 完善的空值检查
- 集合非空验证
- 字符串分割的安全处理

### 6. 错误处理

**空值安全**：
- 对所有可能为空的字段进行检查
- 提供合理的默认值
- 确保必填字段都有有效值

**数据转换安全**：
- BigDecimal精确计算避免精度丢失
- 字符串分割的边界条件处理
- 集合操作的空值保护

此实现确保了视频号小店平台商品数据的完整性和准确性，同时保持了良好的代码可维护性和扩展性，完全适配了视频号小店平台的特定数据结构要求。

## 2025-07-21 完成视频号小店属性映射处理器属性值转换(SphxdAttributeMappingHandler)

参考KuaiShopAttributeMappingHandler的实现，完成了 `SphxdAttributeMappingHandler` 中的 `transCommonValue2Platform` 方法，主要功能：

### 1. 属性映射处理流程

**基础流程**：
```java
// 1. 获取属性映射关系
List<AttributeMappingEntity> attributeMappings = categoryAttributeRepository.findMappingByPlatformAndCategoryLeafId(getPlatform(), categoryLeafId, platformCategoryLeafId);

// 2. 获取平台类目属性ID列表
List<String> platformCategoryAttributeIds = categoryAttributeRepository.getPlatformCategoryAttributeIds(getPlatform(), platformCategoryLeafId);

// 3. 过滤有效的属性映射
attributeMappings = CommonStreamUtil.filter(attributeMappings, attributeMapping -> platformCategoryAttributeIds.contains(attributeMapping.getPlatformAttributeId()));

// 4. 构建映射Map
Map<String, AttributeMappingEntity> attributeMappingMap = CommonStreamUtil.toMap(attributeMappings, AttributeMappingEntity::getAttributeId, (e1, e2) -> e1);
```

**属性信息获取**：
```java
// 获取源汇通类目属性
List<YhtAttribute> yhtAttributes = categoryAttributeRepository.getPlatformAttributesByIds(ErpPlatformTypeEnum.YHT.getValue(), new ArrayList<>(attributeMappingMap.keySet()));

// 获取视频号小店类目属性
List<SphxdAttribute> sphxdAttributes = categoryAttributeRepository.getPlatformAttributesByIds(getPlatform(), platformCategoryAttributeIds);
```

### 2. 属性类型判断

**选项类型识别**：
```java
private boolean isOptionAttribute(YhtAttribute yhtAttribute, SphxdAttribute sphxdAttribute) {
    // YHT平台：select、multi_select为选项类型
    if (yhtAttribute != null && StringUtils.isNotBlank(yhtAttribute.getType())) {
        String yhtType = yhtAttribute.getType();
        if ("select".equals(yhtType) || "multi_select".equals(yhtType)) {
            return true;
        }
    }

    // 视频号小店平台：select_one、select_many为选项类型
    if (sphxdAttribute != null && StringUtils.isNotBlank(sphxdAttribute.getTypeV2())) {
        String typeV2 = sphxdAttribute.getTypeV2();
        if ("select_one".equals(typeV2) || "select_many".equals(typeV2)) {
            return true;
        }
    }

    return false;
}
```

### 3. 选项值映射处理

**选项映射查询**：
```java
AttributeOptionMappingEntity optionMapping = categoryAttributeRepository.findOptionMappingByOptionId(
    getPlatform(),
    mapping.getAttributeId(),
    mapping.getPlatformAttributeId(),
    commonValue.getName() // 使用name作为选项ID
);
```

**选项值转换**：
```java
if (optionMapping != null) {
    // 找到映射关系，使用映射后的值
    mappedValues.add(optionMapping.getPlatformOptionName());
}
```

**单选/多选处理**：
```java
String typeV2 = sphxdAttribute.getTypeV2();
if ("select_one".equals(typeV2) && mappedValues.size() == 1) {
    // 单选属性，返回第一个值
    return mappedValues.get(0);
} else if ("select_many".equals(typeV2)) {
    // 多选属性，用分号连接多个值
    return String.join(";", mappedValues);
}
```

### 4. 非选项类型处理

**多种数据类型支持**：
```java
switch (typeV2) {
    case "string":
        // 文本类型
        return firstValue.getName();

    case "integer":
        // 整数类型
        return String.valueOf(Integer.parseInt(firstValue.getValue().toString()));

    case "decimal4":
        // 小数类型（4位精度）
        double value = Double.parseDouble(firstValue.getValue().toString());
        return String.format("%.4f", value);

    case "integer_unit":
    case "decimal4_unit":
        // 带单位的数值类型
        return firstValue.getValue().toString();
}
```

### 5. 平台差异适配

| 属性类型 | YHT平台 | 视频号小店平台 | 处理方式 |
|----------|---------|----------------|----------|
| 单选 | select | select_one | 映射到单个值 |
| 多选 | multi_select | select_many | 分号连接多个值 |
| 文本 | text | string | 直接映射 |
| 整数 | measure | integer | 数值转换 |
| 小数 | measure | decimal4 | 保留4位精度 |
| 带单位 | multi_value_measure | integer_unit/decimal4_unit | 特殊处理 |

### 6. 数据结构适配

**视频号小店属性结构**：
```java
SphxdProductAggregate.Attr attribute = new SphxdProductAggregate.Attr();
attribute.setAttrKey(sphxdAttribute.getPropId());  // 属性键
attribute.setAttrValue(mappedValue);               // 属性值
```

**属性类型枚举**：
- `string`：文本
- `select_one`：单选，选项列表在value中
- `select_many`：多选，选项列表在value中
- `integer`：整数，数字必须为整数
- `decimal4`：小数（4位精度），小数部分最多4位
- `integer_unit`：整数+单位，单位的选项列表在value中
- `decimal4_unit`：小数（4位精度）+单位，单位的选项列表在value中

### 7. 错误处理机制

**映射缺失处理**：
- 对于没有属性映射关系的属性，记录警告并跳过
- 对于没有选项映射关系的选项值，记录警告并跳过
- 确保只有有效映射的数据才会被使用

**数据类型转换**：
- 整数类型的安全转换和异常处理
- 小数类型的精度控制
- 提供合理的默认值和备用方案

**日志记录**：
- 详细的警告日志，便于问题排查
- 记录映射失败的具体信息
- 数据转换异常的记录

### 8. 输出格式

**简化的属性结构**：
```java
// 视频号小店使用简单的键值对结构
{
    "attrKey": "yanse",      // 属性键（通过拼音生成）
    "attrValue": "红色"      // 属性值（单个值或分号分隔的多个值）
}
```

与快手平台的复杂嵌套结构相比，视频号小店采用了更简洁的属性表示方式，降低了数据处理的复杂度。

此实现遵循了DRY原则，复用了KuaiShopAttributeMappingHandler的设计模式，同时针对视频号小店平台的特定数据结构（如简化的属性格式、不同的类型枚举）进行了专门的适配处理。

## 2025-07-23 修改前端布局为顶部菜单模式

将 yaotown-supplier-admin-web 前端项目的默认布局从左侧菜单（classic）改为顶部菜单（top）模式：

### 修改内容：
1. **修改默认布局配置**：
   - 文件：`frontend/yaotown-supplier-admin-web/src/store/modules/app.ts`
   - 修改：`layout: wsCache.get(CACHE_KEY.LAYOUT) || 'top'`
   - 原值：`'classic'` → 新值：`'top'`

### 功能说明：
- 项目已内置支持多种布局模式：`classic`（左侧菜单）、`topLeft`（顶部+左侧）、`top`（顶部菜单）、`cutMenu`（分割菜单）
- 顶部菜单布局将菜单项显示在页面顶部，类似于您提供的示例图片效果
- 用户仍可通过右侧设置面板手动切换其他布局模式
- 布局配置会缓存在浏览器中，用户的个人选择会被保留

### 布局特点：
- **顶部菜单**：菜单项水平排列在页面顶部
- **响应式设计**：支持移动端适配
- **主题适配**：支持明暗主题切换
- **标签页支持**：可配合标签页功能使用

此修改确保新用户首次访问时默认看到顶部菜单布局，提供更现代化的用户界面体验。

## 2025-07-29 优化 ShardingSphere 数据源配置，统一 Druid 连接池配置

**问题描述：**
- 用户反馈在使用 ShardingSphere 时出现了两种连接池配置混合的情况
- application-local.yml 和 sharding-local.yaml 中都有 Druid 配置，导致配置冲突

**解决方案：**

1. **清理 application-local.yml 中的冗余配置**
   - 移除了注释掉的 Druid 配置块
   - 保留 ShardingSphere 驱动配置
   - 添加说明注释，指明连接池配置应在 sharding-local.yaml 中配置

2. **优化 sharding-local.yaml 中的 Druid 配置**
   - 将嵌套的 `druid:` 配置块改为直接属性配置
   - 使用标准的 Druid 属性名（如 `minIdle` 而不是 `min-idle`）
   - 添加了监控统计功能配置：
     - `filters: stat,wall,log4j2`
     - `connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000`

**修改的文件：**
- `server/ecommate-orderlink-server/ecommate-orderlink-web/src/main/resources/application-local.yml`
- `server/ecommate-orderlink-server/ecommate-orderlink-web/src/main/resources/sharding-local.yaml`

**技术要点：**
- 使用 ShardingSphere 时，连接池配置应统一在 sharding-local.yaml 中管理
- Druid 配置使用标准属性名，避免使用带连字符的配置方式
- 启用了 Druid 的监控统计功能，便于性能监控和 SQL 分析

**预期效果：**
- 消除连接池配置冲突
- 统一使用 Druid 连接池
- 启用监控功能，便于性能调优

## 2025-07-29 生成抖店订单聚合对象(FxgOrderAggregate)

**需求描述：**
根据抖店开放平台API文档，生成抖店订单列表和详情接口的返回值对应的Java对象。

**实现内容：**

1. **基础结构设计**
   - 继承 `ECommerceOrderModel` 基类
   - 使用 Jackson 注解实现 JSON 字段映射
   - 采用驼峰命名规范，通过 `@JsonProperty` 映射真实 JSON 结构
   - 使用 Lombok 简化代码

2. **主要属性定义**
   - `shopOrderList`: 订单列表信息（List<ShopOrderList>）
   - `shopOrderDetail`: 订单详情信息（ShopOrderDetail）

3. **ShopOrderList 类（订单列表）**
   包含完整的订单信息字段：
   - 基础信息：订单ID、订单状态、订单金额、支付金额等
   - 用户信息：买家留言、收货地址、用户昵称等
   - 物流信息：物流公司、物流单号、发货时间等
   - 优惠信息：优惠金额、平台优惠、店铺优惠等
   - SKU信息：商品列表、规格信息等
   - 业务字段：下单端、业务线、交易类型等

4. **ShopOrderDetail 类（订单详情）**
   与 ShopOrderList 结构相同，用于订单详情查询接口

5. **内部类定义**
   创建了30+个内部类来表示复杂的嵌套结构：
   - `AddressTagUi`: 地址标签UI信息
   - `ConsolidateInfo`: 合并信息
   - `LogisticsInfo`: 物流信息
   - `SkuOrderList`: SKU订单列表
   - `PostAddr`: 收货地址
   - `PromotionDetail`: 优惠详情
   - `UserCoordinate`: 用户坐标
   - 等等...

6. **技术特点**
   - **完整性**: 覆盖抖店API文档中的所有返回字段
   - **类型安全**: 使用强类型定义，避免运行时错误
   - **可维护性**: 清晰的类结构和详细的注释
   - **扩展性**: 易于添加新字段或修改现有结构
   - **标准化**: 遵循Java编码规范和DDD设计原则

7. **字段映射示例**
   ```java
   @JsonProperty("order_id")
   private String orderId;

   @JsonProperty("order_amount")
   private String orderAmount;

   @JsonProperty("sku_order_list")
   private List<SkuOrderList> skuOrderList;
   ```

8. **注释说明**
   - 每个字段都包含详细的中文注释
   - 枚举值字段包含可选值说明
   - 复杂字段包含业务含义解释

**文件位置：**
`server/ecommate-trade-server/ecommate-trade-domain/src/main/java/com/yaotown/ecommate/trade/domain/order/ecomlink/model/aggregate/ecomorder/FxgOrderAggregate.java`

**代码行数：** 约2400行

**设计原则遵循：**
- **DRY原则**: 避免重复定义相同结构
- **KISS原则**: 保持类结构简单明了
- **SOLID原则**: 单一职责，每个类只负责特定的数据结构
- **第一性原理**: 基于抖店API文档的真实数据结构进行建模

## 2025-07-31 实现视频号小店订单解析方法(SphxdChannelConnector)

**需求描述：**
实现 `SphxdChannelConnector` 类中的 `parsePlatformOrder` 方法，将视频号小店订单聚合对象转换为平台订单实体。

**实现内容：**

1. **方法签名**
   ```java
   @Override
   public EComLinkPlatformOrderEntity parsePlatformOrder(SphxdOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo)
   ```

2. **核心功能实现**
   - 解析订单基础信息：订单ID、创建时间、更新时间等
   - 解析支付信息：支付时间、支付交易号等
   - 解析物流信息：发货时间等
   - 解析价格信息：商品总价、实付金额、运费、优惠金额等
   - 解析买家信息：买家ID（openid）
   - 解析订单状态和售后状态

3. **订单状态解析方法 (parseOrderStatus)**
   ```java
   private PlatformOrderStatusEnum parseOrderStatus(SphxdOrderAggregate ecommerceOrder) {
       switch (ecommerceOrder.getStatus()) {
           case 20: // 待发货（包括部分发货）
           case 21: // 部分发货
               return PlatformOrderStatusEnum.TOBESHIPPED;
           case 30: // 待收货（包括部分发货）
               return PlatformOrderStatusEnum.DELIVERY;
           case 100: // 完成
               return PlatformOrderStatusEnum.SIGNED;
           case 250: // 订单取消
               return PlatformOrderStatusEnum.CANCEL;
           default:
               return PlatformOrderStatusEnum.PENDING;
       }
   }
   ```

4. **售后状态解析方法 (parseItemWarrantyStatus)**
   ```java
   private PlatformOrderWarrantyStatusEnum parseItemWarrantyStatus(SphxdOrderAggregate.ProductInfo productInfo) {
       Integer skuCnt = productInfo.getSkuCnt();
       Integer onAftersaleSkuCnt = productInfo.getOnAftersaleSkuCnt();
       Integer finishAftersaleSkuCnt = productInfo.getFinishAftersaleSkuCnt();

       if (onAftersaleSkuCnt == 0 && finishAftersaleSkuCnt == 0) {
           return PlatformOrderWarrantyStatusEnum.NONE;
       }
       if (onAftersaleSkuCnt > 0) {
           return PlatformOrderWarrantyStatusEnum.REFUNDING;
       }
       if (finishAftersaleSkuCnt >= skuCnt) {
           return PlatformOrderWarrantyStatusEnum.REFUND_SUCCESS;
       }
       return PlatformOrderWarrantyStatusEnum.REFUNDING;
   }
   ```

5. **售后状态聚合逻辑**
   - 遍历所有商品项，获取每个商品的售后状态
   - 如果所有商品售后状态一致，使用该状态
   - 如果包含退款关闭状态，优先使用退款关闭
   - 否则使用部分退款状态

6. **特殊业务逻辑**
   ```java
   // 如果订单退款成功且订单状态为待付款，则设置订单状态为已取消
   if (orderEntity.getWarrantyStatus() == PlatformOrderWarrantyStatusEnum.REFUND_SUCCESS.getValue()
           && orderEntity.getOrderStatus() == PlatformOrderStatusEnum.PENDING.getValue()) {
       orderEntity.setOrderStatus(PlatformOrderStatusEnum.CANCEL.getValue());
   }
   ```

7. **数据转换处理**
   - 时间戳转换：秒级时间戳转换为 Date 对象
   - 价格转换：分为单位的价格转换为 Long 类型
   - 空值安全：对所有可能为空的字段进行检查

8. **导入的依赖**
   ```java
   import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderStatusEnum;
   import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderWarrantyStatusEnum;
   import java.util.Date;
   import java.util.HashSet;
   import java.util.Set;
   ```

**技术特点：**
- **类型安全**: 使用枚举类型确保状态值的正确性
- **空值安全**: 完善的空值检查，避免 NullPointerException
- **业务逻辑**: 实现了复杂的售后状态聚合逻辑
- **数据转换**: 正确处理时间戳和价格的单位转换
- **代码复用**: 参考其他平台连接器的实现模式

**设计原则遵循：**
- **DRY原则**: 复用了其他平台连接器的设计模式
- **KISS原则**: 保持方法简单明了，职责单一
- **SOLID原则**: 单一职责，每个方法只处理特定的解析逻辑
- **第一性原理**: 基于视频号小店API的真实数据结构进行解析

**文件位置：**
`server/ecommate-trade-server/ecommate-trade-domain/src/main/java/com/yaotown/ecommate/trade/domain/order/ecomlink/service/connector/impl/SphxdChannelConnector.java`

## 2025-08-01 - 添加 showStatus 参数到类目树接口

### 修改内容

#### 1. API 接口修改
- **文件**: `frontend/yaotown-ecommate-admin-web/src/api/product/categoryMapping.ts`
- **函数**: `getCategoryTree`
- **修改**: 添加了可选的 `showStatus` 参数
- **参数说明**:
  - `showStatus?: number` - 显示状态(0:不显示 1:显示)，可选参数
  - 当传入 `showStatus` 参数时，会作为查询参数添加到请求中

#### 2. 前端调用修改
- **文件**: `frontend/yaotown-ecommate-admin-web/src/views/product/category-mapping/index.vue`
- **修改位置**:
  1. `loadSourceNode` 函数 - 第497行：添加 `showStatus: 1` 参数
  2. `loadTargetNode` 函数 - 第523行：添加 `showStatus: 1` 参数
  3. `selectSourceCategoryById` 函数 - 第720行：添加 `showStatus: 1` 参数
  4. `selectSourceCategoryById` 函数 - 第767行：添加 `showStatus: 1` 参数

### API 文档对应
根据提供的 OpenAPI 文档：
```
/v1/platform/product/category/tree/{platform}/{parentId}:
  parameters:
    - name: showStatus
      in: query
      description: 显示状态(0:不显示 1:显示)
      required: false
      schema:
        type: integer
```

### 修改原理
1. **向后兼容**: `showStatus` 参数设为可选，不会影响现有的调用
2. **参数传递**: 使用 `params` 对象传递查询参数，符合 RESTful API 规范
3. **统一调用**: 所有调用都传入 `showStatus: 1` 以显示状态信息

### 影响范围
- 仅影响类目树的加载功能
- 不影响其他业务逻辑
- 保持向后兼容性

## 2025-07-31 实现视频号小店订单商品解析方法(SphxdChannelConnector)

**需求描述：**
实现 `SphxdChannelConnector` 类中的 `parsePlatformOrderItems` 方法，将视频号小店订单中的商品信息转换为平台订单商品实体列表。

**实现内容：**

1. **主方法实现 (parsePlatformOrderItems)**
   ```java
   @Override
   public List<EComLinkPlatformOrderItemEntity> parsePlatformOrderItems(SphxdOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo)
   ```

2. **核心功能**
   - 解析订单中的商品列表 (`productInfos`)
   - 构建发货商品数量映射，用于判断商品状态
   - 逐个解析商品项，生成订单商品实体

3. **发货数量映射构建 (buildDeliveryProductNums)**
   ```java
   private Map<String, Integer> buildDeliveryProductNums(SphxdOrderAggregate.OrderDetail orderDetail)
   ```
   - 遍历发货信息 (`deliveryProductInfo`)
   - 统计每个SKU的已发货数量
   - 用于后续判断商品的发货状态

4. **单个商品解析 (parseItem)**
   ```java
   private EComLinkPlatformOrderItemEntity parseItem(SphxdOrderAggregate.ProductInfo productInfo,
                                                     Map<String, Integer> deliveryProductNums,
                                                     int index)
   ```

5. **商品字段映射**
   - `extNumIid`: 商品ID (`product_id`)
   - `extSkuId`: SKU ID (`sku_id`)
   - `extSkuTitle`: 商品标题 (`title`)
   - `extOuterId`: 商品编码 (`sku_code`)
   - `imageUrl`: 商品图片 (`thumb_img`)
   - `num`: 商品数量 (`sku_cnt`)
   - `price`: 售价 (`sale_price`)
   - `totalFee`: 实付价格 (`real_price`)

6. **商品状态解析 (parseItemStatus)**
   ```java
   private PlatformOrderStatusEnum parseItemStatus(SphxdOrderAggregate.ProductInfo productInfo,
                                                  Map<String, Integer> deliveryProductNums)
   ```
   - 比较商品总数量与已发货数量
   - 如果 `skuCnt > deliveryNum` → `TOBESHIPPED` (待发货)
   - 否则 → `DELIVERY` (配送中)

7. **SKU规格解析**
   ```java
   // 解析SKU属性信息，格式：属性键:属性值;属性键:属性值
   String skuSpecChars = productInfo.getSkuAttrs()
           .stream()
           .map(attr -> attr.getAttrKey() + ":" + attr.getAttrValue())
           .collect(Collectors.joining(";"));
   ```

8. **扩展商品ID生成**
   ```java
   // 格式：productId@skuId@index，确保唯一性
   orderItemEntity.setExtItemId(String.format("%s@%s@%d",
           orderItemEntity.getExtNumIid(),
           orderItemEntity.getExtSkuId(),
           index));
   ```

9. **数据类型处理**
   - 价格字段：Integer 转 Long
   - 数量字段：空值安全处理，默认为0
   - 集合字段：空值检查，避免 NullPointerException

10. **业务逻辑特点**
    - 支持部分发货场景的状态判断
    - 复用已实现的 `parseItemWarrantyStatus` 方法
    - 与参考代码保持一致的数据结构和处理逻辑

**技术特点：**
- **空值安全**: 完善的空值检查和默认值处理
- **类型转换**: 正确处理 Integer 到 Long 的转换
- **集合处理**: 使用 Stream API 进行高效的数据转换
- **业务逻辑**: 准确实现商品状态的判断逻辑
- **代码复用**: 复用现有的售后状态解析方法

**设计原则遵循：**
- **DRY原则**: 复用现有的解析方法和工具类
- **KISS原则**: 保持方法简单明了，职责单一
- **SOLID原则**: 单一职责，每个方法只处理特定的解析逻辑
- **第一性原理**: 基于视频号小店API的真实数据结构进行解析

**导入的依赖：**
```java
import com.yaotown.common.base.core.util.CommonStreamUtil;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
```

## 2025-07-31 修正订单商品解析方法(SphxdChannelConnector)

**修正内容：**

1. **移除 totalFee 字段设置**
   - 根据最新参考代码，移除了 `totalFee` 字段的设置
   - 参考代码中没有设置此字段，保持与参考代码一致

2. **移除 getProductSkuCondition 字段**
   - 确认项目中不存在 `GetProductSkuCondition` 枚举类
   - 移除了相关字段设置，避免编译错误

3. **保持核心逻辑不变**
   - `buildDeliveryProductNums` 方法逻辑与参考代码完全一致
   - `parseItemStatus` 方法逻辑与参考代码完全一致
   - 商品字段映射与参考代码保持一致

**最终实现与参考代码的对应关系：**

| 参考代码字段 | 实现代码字段 | 说明 |
|-------------|-------------|------|
| `extNumIid` | `extNumIid` | 商品ID (`product_id`) |
| `extSkuId` | `extSkuId` | SKU ID (`sku_id`) |
| `extSkuTitle` | `extSkuTitle` | 商品标题 (`title`) |
| `extOuterId` | `extOuterId` | 商品编码 (`sku_code`) |
| `imageUrl` | `imageUrl` | 商品图片 (`thumb_img`) |
| `num` | `num` | 商品数量 (`sku_cnt`) |
| `price` | `price` | 售价 (`sale_price`) |
| `itemStatus` | `itemStatus` | 商品状态 |
| `warrantyStatus` | `warrantyStatus` | 售后状态 |
| ~~`getProductSkuCondition`~~ | ~~移除~~ | 项目中不存在此枚举 |

**修正后的代码完全符合参考代码的实现逻辑和字段映射要求。**

## 2025-07-31 修正 CommonStreamUtil.transList 使用方式

**问题描述：**
`CommonStreamUtil.transList` 方法只接受 `Function<T, V>` 参数，不支持带索引的 lambda 表达式。

**修正前代码：**
```java
return CommonStreamUtil.transList(productInfos, (productInfo, index) -> {
    return parseItem(productInfo, deliveryProductNums, index);
});
```

**修正后代码：**
```java
List<EComLinkPlatformOrderItemEntity> orderItemEntities = new ArrayList<>();
for (int i = 0; i < productInfos.size(); i++) {
    orderItemEntities.add(parseItem(productInfos.get(i), deliveryProductNums, i));
}
return orderItemEntities;
```

**修正说明：**
- 使用传统的 for 循环替代 `CommonStreamUtil.transList`
- 保持了索引参数的传递，确保 `extItemId` 的唯一性
- 代码逻辑和功能完全不变，只是改变了实现方式