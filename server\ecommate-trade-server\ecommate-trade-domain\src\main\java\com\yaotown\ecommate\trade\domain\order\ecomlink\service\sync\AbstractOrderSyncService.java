package com.yaotown.ecommate.trade.domain.order.ecomlink.service.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.yaotown.common.base.core.entity.CommonDateRange;
import com.yaotown.ecommate.common.core.entity.KeyValue;
import com.yaotown.ecommate.common.core.util.json.JsonUtils;
import com.yaotown.ecommate.common.lock.template.DistributedLockCallback;
import com.yaotown.ecommate.common.lock.template.DistributedLockTemplate;
import com.yaotown.ecommate.common.redis.util.RedisUtil;
import com.yaotown.ecommate.trade.domain.common.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.adapter.OrderSyncConfigProperties;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.EComLinkPlatformOrderAggregate;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.ECommerceOrderModel;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.EComLinkPlatformOrderEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.CursorTypeVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.IOrderSyncService;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.ChannelConnectorHolder;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.IChannelConnector;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.handle.OrderConvertHandler;
import com.yaotown.ecommate.trade.types.constant.FlowConstant;
import com.yaotown.ecommate.trade.types.constant.MqConstant;
import com.yaotown.ecommate.trade.types.util.TradeRedisKeyUtil;
import com.yaotown.sdk.mq.bo.ObjectMQMsgBase;
import com.yaotown.sdk.mq.utils.RocketMQEnhanceTemplate;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@Service
@Slf4j
public abstract class AbstractOrderSyncService implements IOrderSyncService {

    @Resource
    protected ChannelConnectorHolder channelConnectorHolder;
    @Resource
    protected RedisUtil redisUtil;
    @Resource
    protected OrderSyncConfigProperties orderSyncConfigProperties;
    @Resource
    protected FlowExecutor flowExecutor;
    @Resource
    protected RocketMQEnhanceTemplate rocketMQEnhanceTemplate;
    @Resource
    protected DistributedLockTemplate distributedLockTemplate;

    private final static String DEFAULT_TAG = "default";

    @Override
    public void syncOrderByJob(ShopInfoEntity shopInfo) {
        // 获取平台连接器
        IChannelConnector channelConnector = channelConnectorHolder.route(shopInfo.getPlatformType());
        if (channelConnector == null)
            return;
        // 获取拉取时间范围
        CommonDateRange dateRange = this.getSyncOrderDateRange(shopInfo, channelConnector);
        log.info("[JOB][订单同步任务] 开始抓取订单 店铺ID:{} 开始时间:{} 结束时间:{}", shopInfo.getShopId(),
                DateUtil.formatDateTime(dateRange.getStartDate()), DateUtil.formatDateTime(dateRange.getEndDate()));

        try {
            // 抓取订单
            int result = this.syncOrderByFetchTime(dateRange.getStartDate(), dateRange.getEndDate(), shopInfo);
            // 更新redis抓取时间
            this.updateSyncOrderFetchTime(shopInfo, dateRange.getEndDate());
            log.info("[JOB][订单同步任务] 抓取订单结束 店铺ID:{} 抓取数量:{}", shopInfo.getShopId(), result);
        } catch (Exception e) {
            log.error("[JOB][订单同步任务] 抓取订单异常 店铺ID:{}", shopInfo.getShopId(), e);
        }
    }

    @Override
    public int syncOrderByFetchTime(Date beginDate, Date endDate, ShopInfoEntity shopInfo) {
        IChannelConnector channelConnector = channelConnectorHolder.route(shopInfo.getPlatformType());
        if (channelConnector == null)
            return 0;

        Set<String> extOrderIdSet = new HashSet<>();
        // 按照不同类型同步订单
        IChannelConnector.GetType[] getTypes = channelConnector.getPlatformParam().getGetTypes();
        for (IChannelConnector.GetType getType : getTypes) {
            switch (channelConnector.getPlatformParam().getOrderSyncWay()) {
                case CURSOR:
                    // TODO 暂不做实现
                    extOrderIdSet.addAll(this.syncOrderByCursor(beginDate, endDate, shopInfo, getType));
                    break;
                case TRAVERSE:
                    // TODO 暂不做实现
//                    extOrderIdSet.addAll(this.syncOrderByTraverse(beginDate, endDate, shopInfo, getType));
                    break;
                case PAGE:
                    extOrderIdSet.addAll(this.syncOrderByPage(beginDate, endDate, shopInfo, getType));
                    break;
                default:
                    log.error("[订单同步] 通过拉取时间同步不支持orderIds同步模式");
            }
        }

        return extOrderIdSet.size();
    }

    @Override
    public Set<String> syncOrderByCursor(Date startDate, Date endDate, ShopInfoEntity shopInfo, IChannelConnector.GetType getType) {
        // 获取平台连接器
        IChannelConnector<ECommerceOrderModel> channelConnector = channelConnectorHolder.route(shopInfo.getPlatformType());
        if (channelConnector == null)
            return Set.of();

        String beginDateStr = DateUtil.formatDateTime(startDate);
        String endDateStr = DateUtil.formatDateTime(endDate);
        log.info("[店铺订单抓取] 开始店铺[{}][{}][{}][{}]抓取订单{}---{}", shopInfo.getShopName(), shopInfo.getShopId(), shopInfo.getPlatformType(),
                getType.getName(), beginDateStr, endDateStr);

        Set<String> extOrderIdSet = new HashSet<>();
        KeyValue<String, List<ECommerceOrderModel>> orderPagByCursor = channelConnector.getOrderPageByCursor(CursorTypeVO.FIRST.getValue(), startDate, endDate, shopInfo, getType);
        List<ECommerceOrderModel> commerceOrders = orderPagByCursor.getValue();

        while (CollUtil.isNotEmpty(commerceOrders)) {
            syncSendECommerceOrder(commerceOrders, shopInfo, extOrderIdSet);
            if (Objects.equals(orderPagByCursor.getKey(), CursorTypeVO.NOMORE.getValue()))
                break;

            orderPagByCursor = channelConnector.getOrderPageByCursor(
                    orderPagByCursor.getKey(),
                    startDate,
                    endDate,
                    shopInfo,
                    getType
            );
            commerceOrders = orderPagByCursor.getValue();
        }

        log.info("[店铺订单抓取] 结束店铺[{}][{}][{}][{}]抓取订单{}---{}共同步{}条订单", shopInfo.getShopName(), shopInfo.getShopId(), shopInfo.getPlatformType(),
                getType.getName(), beginDateStr, endDateStr, extOrderIdSet.size());
        return extOrderIdSet;
    }

    @Override
    public Set<String> syncOrderByPage(Date startDate, Date endDate, ShopInfoEntity shopInfo, IChannelConnector.GetType getType) {
        // 获取平台连接器
        IChannelConnector channelConnector = channelConnectorHolder.route(shopInfo.getPlatformType());
        if (channelConnector == null)
            return Set.of();

        String beginDateStr = DateUtil.formatDateTime(startDate);
        String endDateStr = DateUtil.formatDateTime(endDate);
        log.info("[店铺订单抓取] 开始店铺[{}][{}][{}][{}]抓取订单{}---{}", shopInfo.getShopName(), shopInfo.getShopId(), shopInfo.getPlatformType(),
                getType.getName(), beginDateStr, endDateStr);

        int count = channelConnector.getOrderCount(startDate, endDate, shopInfo, getType);
        log.info("[店铺订单抓取] 店铺[{}][{}][{}][{}]在{}---{}之间共有{}条订单", shopInfo.getShopName(), shopInfo.getShopId(), shopInfo.getPlatformType(),
                getType.getName(), beginDateStr, endDateStr, count);

        Set<String> extOrderIdSet = new HashSet<>();
        if (count == 0)
            return extOrderIdSet;

        // 时间区间内总数超过了平台所设置上限 需要进行分割时间段抓取
        if (count > channelConnector.getPlatformParam().getMaxFetchCount()) {
            List<FetchTime> fetchTimes = this.splitFetchTime(startDate, endDate, shopInfo, getType);
            log.info("[店铺订单抓取] 店铺[{}][{}][{}][{}]拆分时间段抓取:{}", shopInfo.getShopName(), shopInfo.getShopId(), shopInfo.getPlatformType(),
                    getType.getName(), JsonUtils.toJsonString(fetchTimes));
            // 按照时间段同步
            for (FetchTime fetchTime : fetchTimes)
                this.doSyncOrderByPage(extOrderIdSet, fetchTime, shopInfo, getType);
        } else {
            FetchTime fetchTime = FetchTime.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .count(count)
                    .build();
            this.doSyncOrderByPage(extOrderIdSet, fetchTime, shopInfo, getType);
        }

        log.info("[店铺订单抓取] 结束店铺[{}][{}][{}][{}]抓取订单{}---{}共同步{}条订单", shopInfo.getShopName(), shopInfo.getShopId(), shopInfo.getPlatformType(),
                getType.getName(), beginDateStr, endDateStr, extOrderIdSet.size());
        return extOrderIdSet;
    }

    private void doSyncOrderByPage(Set<String> extOrderIdSet, FetchTime fetchTime, ShopInfoEntity shopInfo, IChannelConnector.GetType getType) {
        IChannelConnector channelConnector = channelConnectorHolder.route(shopInfo.getPlatformType());
        int totalPage = this.calcTotalPageCount(fetchTime.getCount(), channelConnector.getPlatformParam().getDefaultPageSize());
        for (int pageIndex = totalPage; pageIndex >= 1; --pageIndex) {
            List<ECommerceOrderModel> ecommerceOrders = channelConnector.getOrderPage(
                    pageIndex,
                    channelConnector.getPlatformParam().getDefaultPageSize(),
                    fetchTime.getStartDate(),
                    fetchTime.getEndDate(),
                    shopInfo,
                    getType
            );

            if (CollUtil.isEmpty(ecommerceOrders))
                continue;

            syncSendECommerceOrder(ecommerceOrders, shopInfo, extOrderIdSet);
        }
    }

    private void syncSendECommerceOrder(List<ECommerceOrderModel> ecommerceOrders, ShopInfoEntity shopInfo, Set<String> extOrderIdSet) {
        List<ObjectMQMsgBase<String>> msgList = new ArrayList<>();
        for (ECommerceOrderModel ecommerceOrder : ecommerceOrders) {
            if (!extOrderIdSet.add(ecommerceOrder.getEcommerceOrderId()))
                continue;
            ObjectMQMsgBase<String> msgBase = new ObjectMQMsgBase<>(
                    DEFAULT_TAG,
                    DEFAULT_TAG,
                    JsonUtils.toJsonString(ecommerceOrder)
            );
            msgBase.setMqMsgObjectClassName(ecommerceOrder.getClass().getName());
            msgList.add(msgBase);
        }
        if (CollUtil.isNotEmpty(msgList)) {
            // 根据店铺顺序消费，防止并发太高
            rocketMQEnhanceTemplate.multiSendOrderly(MqConstant.ORDER_SYNC_HANDLE_TOPIC, DEFAULT_TAG, msgList, shopInfo.getShopId().toString());
        }
    }

    private int calcTotalPageCount(int totalCount, int pageSize) {
        return (0 == (totalCount % pageSize)) ? (totalCount / pageSize) : ((totalCount / pageSize) + 1);
    }

    @Override
    public EComLinkPlatformOrderAggregate parseOrder(ECommerceOrderModel ecommerceOrder, ShopInfoEntity shopInfo) {
        // 获取平台连接器
        IChannelConnector channelConnector = channelConnectorHolder.route(ecommerceOrder.getPlatform());
        if (channelConnector == null)
            return null;

        EComLinkPlatformOrderAggregate platformOrderAggregate = EComLinkPlatformOrderAggregate.builder()
                .platformOrder(channelConnector.parsePlatformOrder(ecommerceOrder, shopInfo))
                .platformOrderItems(channelConnector.parsePlatformOrderItems(ecommerceOrder, shopInfo))
                .platformOrderLogistics(channelConnector.parsePlatformOrderLogistics(ecommerceOrder, shopInfo))
                .platformOrderConsignee(channelConnector.parsePlatformOrderConsignee(ecommerceOrder, shopInfo))
                .platformOrderMemo(channelConnector.parsePlatformOrderMemo(ecommerceOrder, shopInfo))
                .build();

        // 初始化基础数据
        this.initPlatformOrderDefaultData(platformOrderAggregate, shopInfo);

        return platformOrderAggregate;
    }

    @Override
    public List<Long> convertOrder(EComLinkPlatformOrderAggregate platformOrderAggregate, ShopInfoEntity shopInfo) {
        EComLinkPlatformOrderEntity platformOrder = platformOrderAggregate.getPlatformOrder();
        Long enterpriseId = platformOrder.getEnterpriseId();
        String extOrderId = platformOrder.getExtOrderId();
        return distributedLockTemplate.tryLock(new DistributedLockCallback<>() {
            @Override
            public List<Long> process() {
                OrderConvertHandler.ProcessContext processContext = new OrderConvertHandler.ProcessContext();
                processContext.setPlatformOrderAggregates(List.of(platformOrderAggregate));
                processContext.setShopInfo(shopInfo);
                LiteflowResponse response = flowExecutor.execute2Resp(FlowConstant.CONVERT_ORDER_FLOW, null, processContext);
                if (response.isSuccess() && CollUtil.isNotEmpty(processContext.getPlatformOrderIds())) {
                    return processContext.getPlatformOrderIds();
                } else {
                    log.error("[店铺订单转换处理] 转换流执行失败 店铺[{}][{}][{}][{}] 订单{}", shopInfo.getShopName(), shopInfo.getShopId(),
                            shopInfo.getPlatformType(), enterpriseId, extOrderId, response.getCause());
                }
                return List.of();
            }

            @Override
            public String getLockName() {
                return TradeRedisKeyUtil.orderConvertLockKey(extOrderId);
            }

            @Override
            public String getErrorDesc() {
                return String.format("[店铺订单转换处理] 已有订单转换中 店铺[%s][%s][%s][%s] 订单%s", shopInfo.getShopName(), shopInfo.getShopId(), shopInfo.getPlatformType(), enterpriseId, extOrderId);
            }
        }, 60, 60, TimeUnit.SECONDS, false);
    }

    protected abstract List<FetchTime> splitFetchTime(Date startDate, Date endDate, ShopInfoEntity shopInfo, IChannelConnector.GetType getType);

    protected abstract CommonDateRange getSyncOrderDateRange(ShopInfoEntity shopInfo, IChannelConnector channelConnector);

    protected abstract void updateSyncOrderFetchTime(ShopInfoEntity shopInfo, Date endDate);

    protected abstract void initPlatformOrderDefaultData(EComLinkPlatformOrderAggregate platformOrderAggregate, ShopInfoEntity shopInfo);

    @Data
    @Builder
    protected static class FetchTime {
        private Date startDate;
        private Date endDate;
        private int count;
    }
}
