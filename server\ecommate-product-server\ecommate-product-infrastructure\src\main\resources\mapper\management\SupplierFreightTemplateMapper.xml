<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaotown.ecommate.product.infrastructure.mapper.management.SupplierFreightTemplateMapper">
  <resultMap id="BaseResultMap" type="com.yaotown.ecommate.product.infrastructure.po.management.SupplierFreightTemplatePO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="region_codes" jdbcType="VARCHAR" property="regionCodes" />
    <result column="first_item" jdbcType="INTEGER" property="firstItem" />
    <result column="first_item_fee" jdbcType="BIGINT" property="firstItemFee" />
    <result column="additional_item" jdbcType="INTEGER" property="additionalItem" />
    <result column="additional_item_fee" jdbcType="BIGINT" property="additionalItemFee" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="modifier_name" jdbcType="VARCHAR" property="modifierName" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, enterprise_id, template_name, region_name, region_codes, first_item, 
    first_item_fee, additional_item, additional_item_fee, status, delete_flag, 
    creator_id, creator_name, modifier_id, modifier_name, created, updated
  </sql>

  <!-- 批量插入 -->
  <insert id="insertBatchSomeColumn">
    INSERT INTO yt_freight_template (
      enterprise_id, template_name, region_name, region_codes, 
      first_item, first_item_fee, additional_item, additional_item_fee, 
      status, delete_flag, creator_id, creator_name, modifier_id, 
      modifier_name, created, updated
    ) VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (
        #{item.enterpriseId,jdbcType=BIGINT}, 
        #{item.templateName,jdbcType=VARCHAR}, 
        #{item.regionName,jdbcType=VARCHAR}, 
        #{item.regionCodes,jdbcType=VARCHAR}, 
        #{item.firstItem,jdbcType=INTEGER}, 
        #{item.firstItemFee,jdbcType=BIGINT}, 
        #{item.additionalItem,jdbcType=INTEGER}, 
        #{item.additionalItemFee,jdbcType=BIGINT}, 
        #{item.status,jdbcType=INTEGER}, 
        #{item.deleteFlag,jdbcType=INTEGER}, 
        #{item.creatorId,jdbcType=BIGINT}, 
        #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.modifierId,jdbcType=BIGINT}, 
        #{item.modifierName,jdbcType=VARCHAR}, 
        #{item.created,jdbcType=TIMESTAMP}, 
        #{item.updated,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <update id="removeById">
      update yt_freight_template
      set delete_flag   = 1,
          modifier_id   = #{modifierId},
          modifier_name = #{modifierName}
      WHERE id = #{id}
  </update>
</mapper>