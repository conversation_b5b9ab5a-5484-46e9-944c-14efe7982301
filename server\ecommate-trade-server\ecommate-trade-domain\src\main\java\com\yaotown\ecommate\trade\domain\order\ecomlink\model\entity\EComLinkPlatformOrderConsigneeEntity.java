package com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class EComLinkPlatformOrderConsigneeEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 平台订单id
     */
    private Long platformOrderId;

    /**
     * 外部订单ID
     */
    private String extOrderId;

    /**
     * 订单来源平台
     */
    private String platform;

    /**
     * 订单所属企业id
     */
    private Long enterpriseId;

    /**
     * 订单供应商企业id
     */
    private Long supplierEnterpriseId;

    /**
     * 订单当前企业id(分销拆单时，当前企业id为拆分后的企业id)
     */
    private Long currentEnterpriseId;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人国家
     */
    private String receiverCountry;

    /**
     * 收件人州/省
     */
    private String receiverState;

    /**
     * 收件人市
     */
    private String receiverCity;

    /**
     * 收件人区，县
     */
    private String receiverDistrict;

    /**
     * 收件人街道，乡镇
     */
    private String receiverTown;

    /**
     * 收件人详细地址
     */
    private String receiverAddress;

    /**
     * 收件人邮政编码
     */
    private String receiverZip;

    /**
     * 收件人手机号
     */
    private String receiverMobile;

    /**
     * 收件人手机号
     */
    private String receiverPhone;

    /**
     * 完成时间
     */
    private Date doneTime;

    /**
     * 操作版本(1:默认 4:记录换货操作 5:组合商品原记录)
     */
    private Long doneVersion;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date updated;
}
