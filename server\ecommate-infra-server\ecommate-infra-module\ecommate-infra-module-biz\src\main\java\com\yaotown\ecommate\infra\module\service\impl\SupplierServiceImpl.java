package com.yaotown.ecommate.infra.module.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.crypto.SecureUtil;
import com.yaotown.ecommate.infra.module.enums.AccountStatusEnum;
import com.yaotown.ecommate.infra.module.enums.EnterpriseAccountTypeEnum;
import com.yaotown.ecommate.infra.module.mapper.EnterpriseMapper;
import com.yaotown.ecommate.infra.module.mapper.account.AccountEnterpriseMapper;
import com.yaotown.ecommate.infra.module.mapper.account.AccountMapper;
import com.yaotown.ecommate.infra.module.pojo.dto.enterprise.account.LoginRespDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.supplier.SupplierLoginDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.supplier.SupplierRegisterDTO;
import com.yaotown.ecommate.infra.module.pojo.entity.EnterprisePO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.AccountEnterprisePO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.AccountPO;
import com.yaotown.ecommate.infra.module.service.CaptchaService;
import com.yaotown.ecommate.infra.module.service.SupplierService;
import com.yaotown.ecommate.infra.module.service.oauth2.OAuth2TokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 供应商服务实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SupplierServiceImpl implements SupplierService {

    private final AccountMapper accountMapper;
    private final EnterpriseMapper enterpriseMapper;
    private final AccountEnterpriseMapper accountEnterpriseMapper;
    private final CaptchaService captchaService;
    private final OAuth2TokenService oAuth2TokenService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registerSupplier(SupplierRegisterDTO registerDTO) {
        
        // 2. 创建企业信息
        EnterprisePO enterprisePO = new EnterprisePO();
        enterprisePO.setEnterpriseName(registerDTO.getEnterpriseName());
        enterprisePO.setContact(registerDTO.getContactPerson());
        enterprisePO.setPhone(registerDTO.getContactPhone());
        enterprisePO.setEnterpriseType(2); // 2表示供应商企业类型
        // 设置版本ID（需要先修改企业表，添加版本ID字段）
        // enterprisePO.setVersionId(registerDTO.getVersionId());
        enterprisePO.setStatus(1); // 1表示正常状态
        enterprisePO.setDeleteFlag(0); // 0表示未删除
        
        // 3. 保存企业信息
        enterpriseMapper.insert(enterprisePO);
        
        // 4. 创建账号信息
        AccountPO accountPO = new AccountPO();
        accountPO.setUsername(registerDTO.getUsername());
        accountPO.setNickname(registerDTO.getUsername());
        accountPO.setMobile(registerDTO.getContactPhone());
//        accountPO.setAccountType(AccountTypeEnum.SUPPLIER_ACCOUNT.getId()); // 供应商账号类型
        
        // 5. 设置密码（加盐处理）
        accountPO.setHashSalt(UUID.randomUUID().toString(true));
        accountPO.setLoginPassword(SecureUtil.md5(registerDTO.getPassword() + accountPO.getHashSalt()));
        
        // 6. 设置其他账号属性
        accountPO.setStatus(AccountStatusEnum.NORMAL.getId()); // 正常状态
        accountPO.setExpiredAt(DateUtil.offset(DateUtil.date(), DateField.YEAR, 10)); // 设置10年后过期
        
        // 7. 保存账号信息
        accountMapper.insert(accountPO);
        
        // 8. 创建账号与企业的关联
        AccountEnterprisePO accountEnterprisePO = new AccountEnterprisePO();
        accountEnterprisePO.setAccountId(accountPO.getId());
        accountEnterprisePO.setEnterpriseId(enterprisePO.getId());
        accountEnterprisePO.setEnterpriseAccountType(EnterpriseAccountTypeEnum.ENTERPRISE_MAIN_ACCOUNT.getCode()); // 主账号
        
        // 9. 保存关联信息
        accountEnterpriseMapper.insert(accountEnterprisePO);
    }

    @Override
    public LoginRespDTO login(SupplierLoginDTO loginDTO) {
     return new LoginRespDTO();
    }
} 