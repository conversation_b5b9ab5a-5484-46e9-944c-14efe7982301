package com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector;

import com.yaotown.ecommate.common.core.entity.KeyValue;
import com.yaotown.ecommate.trade.domain.common.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.ECommerceOrderModel;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.*;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.PlatformParamVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 平台渠道连接器
 *
 * <AUTHOR>
 * @date 2025/6/11
 */
public interface IChannelConnector<T extends ECommerceOrderModel> {

    /**
     * 获取当前平台
     *
     * @return
     */
    String getPlatform();

    /**
     * 获取平台配置参数
     * 每个平台个性话参数请根据需求配置在此处 勿在业务代码中编写平台个性化判断
     *
     * @return
     */
    PlatformParamVO getPlatformParam();

    /**
     * 获取指定时间范围的订单数量
     *
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @param shopInfo
     * @param getType   抓取订单类型
     * @return int 总记录数量
     */
    Integer getOrderCount(Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType);

    /**
     * 返回指定页码和指定记录的订单
     * 注意返回列表json中一定要设置iboss_ext_order_id参数
     *
     * @param pageIndex 页码，从1开始
     * @param pageSize  每页大小
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @param shopInfo  店铺信息
     * @param getType   抓取订单类型
     * @return List<T> 返回每条订单从平台上获取下来的JSON字符串
     */
    List<T> getOrderPage(int pageIndex, int pageSize, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType);

    /**
     * 返回指定页码和指定记录的订单
     * 注意返回列表json中一定要设置iboss_ext_order_id参数
     *
     * @param cursor    游标内容，第一次传空 CursorType.FIRST.getValue()
     * @param beginDate
     * @param endDate
     * @param shopInfo
     * @param getType   抓取订单类型
     * @return Pair<String, List <T>> 返回 cursor & 每条订单从平台上获取下来的JSON字符串
     */
    KeyValue<String, List<T>> getOrderPageByCursor(String cursor, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType);

    /**
     * 解析平台订单
     *
     * @param ecommerceOrder 平台订单
     * @param shopInfo       店铺信息
     * @return 平台订单实体对象
     */
    EComLinkPlatformOrderEntity parsePlatformOrder(T ecommerceOrder, ShopInfoEntity shopInfo);

    /**
     * 解析平台订单项
     *
     * @param ecommerceOrder 平台订单
     * @param shopInfo       店铺信息
     * @return 平台订单项实体对象
     */
    List<EComLinkPlatformOrderItemEntity> parsePlatformOrderItems(T ecommerceOrder, ShopInfoEntity shopInfo);

    /**
     * 解析平台订单物流
     *
     * @param ecommerceOrder 平台订单
     * @param shopInfo       店铺信息
     * @return 平台订单物流实体对象
     */
    List<EComLinkPlatformOrderLogisticsEntity> parsePlatformOrderLogistics(T ecommerceOrder, ShopInfoEntity shopInfo);

    /**
     * 解析平台订单备注
     *
     * @param ecommerceOrder 平台订单
     * @param shopInfo       店铺信息
     * @return 平台订单备注实体对象
     */
    EComLinkPlatformOrderMemoEntity parsePlatformOrderMemo(T ecommerceOrder, ShopInfoEntity shopInfo);

    /**
     * 解析平台订单收货人
     *
     * @param ecommerceOrder 平台订单
     * @param shopInfo       店铺信息
     * @return 平台订单收货人实体对象
     */
    EComLinkPlatformOrderConsigneeEntity parsePlatformOrderConsignee(T ecommerceOrder, ShopInfoEntity shopInfo);


    @AllArgsConstructor
    @Data
    @Builder
    @NoArgsConstructor
    class GetType {
        private int value;
        private String name;
    }
}
