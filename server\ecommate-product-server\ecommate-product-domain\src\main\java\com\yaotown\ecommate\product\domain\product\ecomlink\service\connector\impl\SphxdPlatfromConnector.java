package com.yaotown.ecommate.product.domain.product.ecomlink.service.connector.impl;

import com.yaotown.ecommate.common.core.enums.BooleanEnum;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.core.util.json.JsonUtils;
import com.yaotown.ecommate.product.domain.product.ecomlink.adapter.port.IECommerceLinkSupplyProductPort;
import com.yaotown.ecommate.product.domain.product.ecomlink.adapter.port.ISphxdLinkPort;
import com.yaotown.ecommate.product.domain.product.ecomlink.model.aggregate.ecomproduct.ECommerceProduct;
import com.yaotown.ecommate.product.domain.product.ecomlink.model.aggregate.ecomproduct.ECommerceProductModel;
import com.yaotown.ecommate.product.domain.product.ecomlink.model.aggregate.sphxd.SphxdProductAggregate;
import com.yaotown.ecommate.product.domain.product.ecomlink.model.entity.ProductListingTaskEntity;
import com.yaotown.ecommate.product.domain.product.ecomlink.model.valobj.*;
import com.yaotown.ecommate.product.domain.product.ecomlink.service.connector.AbstractPlatformConnector;
import com.yaotown.ecommate.product.domain.product.ecomlink.service.handle.Callback;
import com.yaotown.ecommate.product.domain.product.erplink.model.valobj.ErpResponseVO;
import com.yaotown.ecommate.product.domain.product.listing.model.aggregate.ProductListingDetailAggregate;
import com.yaotown.ecommate.product.domain.product.listing.model.entity.ProductListingEntity;
import com.yaotown.ecommate.product.domain.product.listing.model.entity.ProductSkuEntity;
import com.yaotown.ecommate.product.domain.product.listing.model.entity.ProductSpuEntity;
import com.yaotown.ecommate.product.domain.product.listing.model.valobj.ProductImageVO;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.valobj.PlatformProductVO;
import com.yaotown.ecommate.product.types.enums.erp.ErpPlatformTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SphxdPlatfromConnector extends AbstractPlatformConnector<SphxdProductAggregate> {

    @Resource
    private ISphxdLinkPort sphxdLinkPort;
    @Resource
    private IECommerceLinkSupplyProductPort eCommerceLinkSupplyProductPort;

    public SphxdPlatfromConnector() {
        platform = ErpPlatformTypeEnum.SPHXD.getValue();
    }

    /**
     * 获取运费模板ID
     * 
     * @param accessToken 访问令牌
     * @return 运费模板ID，如果获取失败则返回默认值
     */
    private String getFreightTemplateId(String accessToken) {
        try {
            // 默认获取10个模板，取第一个
            SphxdFreightTemplateResponse response = sphxdLinkPort.getFreightTemplateList(accessToken, 0, 1000);
            if (response != null && response.getErrcode() != null && response.getErrcode() == 0 
                    && response.getTemplate_id_list() != null && !response.getTemplate_id_list().isEmpty()) {
                return response.getTemplate_id_list().get(0);
            }
            log.warn("获取运费模板失败，将使用默认值");
            return "47428464001";  // 默认值
        } catch (Exception e) {
            log.error("获取运费模板异常", e);
            return "47428464001";  // 默认值
        }
    }

    /**
     * 获取售后地址ID
     * 
     * @param accessToken 访问令牌
     * @return 售后地址ID，如果获取失败则返回默认值
     */
    private Long getAfterSaleAddressId(String accessToken) {
        try {
            // 默认获取售后地址列表，取第一个
            SphxdAddressResponse response = sphxdLinkPort.getAddressList(accessToken, 0, 10);
            if (response != null && response.getErrcode() != null && response.getErrcode() == 0 
                    && response.getAddress_id_list() != null && !response.getAddress_id_list().isEmpty()) {
                String addressId = response.getAddress_id_list().get(0);
                return Long.parseLong(addressId);
            }
            log.warn("获取售后地址失败，将使用默认值");
            return 1L;  // 默认值
        } catch (Exception e) {
            log.error("获取售后地址异常", e);
            return 1L;  // 默认值
        }
    }

    @Override
    public ECommerceProduct<SphxdProductAggregate> trans2ECommerceProduct(ProductListingDetailAggregate productListingDetailAggregate, ECommerceShopVO ecommerceShop, ECommerceProduct<SphxdProductAggregate> eCommerceProduct) {
        log.info("转换产品到视频号小店格式, productId: {}", productListingDetailAggregate.getProductListing().getProductListingId());

        SphxdProductAggregate sphxdProduct = new SphxdProductAggregate();
        ProductListingEntity productListing = productListingDetailAggregate.getProductListing();
        ProductSpuEntity productSpu = productListingDetailAggregate.getProductSpu();
        
        // 尝试从选品中心获取平台数据
        try {
            // 获取平台产品数据
            Long enterpriseId = productListing.getEnterpriseId();
            Long supplierOriProductId = Long.valueOf(productSpu.getOriProductId());
            PlatformProductVO platformProductVO = eCommerceLinkSupplyProductPort.getCategoryAttributeByProductExtend(
                    enterpriseId, supplierOriProductId, platform);
            
            // 如果找到了平台数据，优先使用平台数据填充
            if (platformProductVO != null && StringUtils.hasLength(platformProductVO.getContent())) {
                log.info("找到SPHXD平台产品数据，使用平台数据进行填充");
                try {
                    // 解析JSON内容
                    SphxdProductAggregate platformData = JsonUtils.parseObject(
                            platformProductVO.getContent(), SphxdProductAggregate.class);
                    
                    if (platformData != null) {
                        // 复制平台数据到当前对象
                        sphxdProduct = platformData;
                        
                        // 确保覆盖一些关键字段为本地数据
                        sphxdProduct.setOutProductId(String.valueOf(productListing.getProductId()));
                        sphxdProduct.setTitle(productListing.getProductFullName());
                        sphxdProduct.setShortTitle(productListing.getProductName());
                        
//                        // 设置SKU信息
//                        updateSkusFromLocalData(sphxdProduct, productListingDetailAggregate);
                        
                        log.info("成功使用平台数据填充视频号小店商品");
                    }
                } catch (Exception e) {
                    log.error("解析SPHXD平台产品数据失败，将使用本地数据进行填充", e);
                    // 如果解析失败，回退到手动填充
                    fillProductDataManually(sphxdProduct, productListingDetailAggregate, ecommerceShop);
                }
            } else {
                log.info("未找到SPHXD平台产品数据，使用本地数据进行填充");
                fillProductDataManually(sphxdProduct, productListingDetailAggregate, ecommerceShop);
            }
        } catch (Exception e) {
            log.error("获取SPHXD平台产品数据失败，使用本地数据进行填充", e);
            fillProductDataManually(sphxdProduct, productListingDetailAggregate, ecommerceShop);
        }
        
        // 设置ECommerceProductModel基本信息
        sphxdProduct.setProductId(productListing.getProductId());
        sphxdProduct.setProductListingId(productListing.getProductListingId());
        sphxdProduct.setEnterpriseId(productListing.getEnterpriseId());

        // 创建响应产品
        ECommerceProduct<SphxdProductAggregate> result = new ECommerceProduct<>();
        result.setProductModel(sphxdProduct);
        if (eCommerceProduct != null) {
            result.setPlatform(eCommerceProduct.getPlatform());
            result.setShopId(eCommerceProduct.getShopId());
        }

        return result;
    }

    /**
     * 从本地数据填充产品信息
     * 
     * @param sphxdProduct 视频号小店产品
     * @param productListingDetailAggregate 商品详情
     * @param ecommerceShop 店铺信息
     */
    private void fillProductDataManually(SphxdProductAggregate sphxdProduct, ProductListingDetailAggregate productListingDetailAggregate, ECommerceShopVO ecommerceShop) {
        ProductListingEntity productListing = productListingDetailAggregate.getProductListing();
        ProductSpuEntity productSpu = productListingDetailAggregate.getProductSpu();
        
        // 设置基本信息
        sphxdProduct.setOutProductId(String.valueOf(productListing.getProductId()));
        sphxdProduct.setTitle(productListing.getProductFullName());
        sphxdProduct.setShortTitle(productListing.getProductName());

        // 设置发货方式，默认快递发货
        sphxdProduct.setDeliverMethod(0);

        // 设置商品类目 - TODO: 需要调整类目字段
        List<SphxdProductAggregate.CategoryInfo> catsV2 = new ArrayList<>();
        SphxdProductAggregate.CategoryInfo catInfo = new SphxdProductAggregate.CategoryInfo();
        catInfo.setCatId("10000111"); // 暂时使用固定值，后续根据实际情况调整
        catsV2.add(catInfo);
        sphxdProduct.setCatsV2(catsV2);

        // 设置主图
        List<ProductImageVO> images = productListingDetailAggregate.getProductImage();
        if (!CollectionUtils.isEmpty(images)) {
            List<String> headImgs = images.stream()
                    .filter(img -> img != null && StringUtils.hasLength(img.getUrl()))
                    .map(ProductImageVO::getUrl)
                    .collect(Collectors.toList());
            sphxdProduct.setHeadImgs(headImgs);
        }

        // 设置商品详情
        SphxdProductAggregate.DescInfo descInfo = new SphxdProductAggregate.DescInfo();

        //商品详情文本
        descInfo.setDesc(productSpu.getDescription());

        // 设置详情图片
        List<ProductImageVO> detailImages = productListingDetailAggregate.getProductListingImage();
        if (!CollectionUtils.isEmpty(detailImages)) {
            List<String> detailImgUrls = detailImages.stream()
                    .filter(img -> img != null && StringUtils.hasLength(img.getUrl()))
                    .map(ProductImageVO::getUrl)
                    .collect(Collectors.toList());
            descInfo.setImgs(detailImgUrls);
        }

        sphxdProduct.setDescInfo(descInfo);

        // 设置商品属性 - TODO: 获取商品属性
        List<SphxdProductAggregate.Attribute> attrs = new ArrayList<>();
        SphxdProductAggregate.Attribute attr = new SphxdProductAggregate.Attribute();
        attr.setAttrKey("产地");
        attr.setAttrValue("中国");
        attrs.add(attr);
        sphxdProduct.setAttrs(attrs);

        // 设置运费信息 - 从API获取运费模板ID
        SphxdProductAggregate.ExpressInfo expressInfo = new SphxdProductAggregate.ExpressInfo();
        // 如果有访问令牌，则尝试获取运费模板ID
        if (ecommerceShop != null && StringUtils.hasLength(ecommerceShop.getAccessToken())) {
            String templateId = getFreightTemplateId(ecommerceShop.getAccessToken());
            expressInfo.setTemplateId(templateId);
        } else {
            expressInfo.setTemplateId("47428464001"); // 使用默认值
        }
        expressInfo.setWeight(100); // 默认重量
        sphxdProduct.setExpressInfo(expressInfo);

        // 设置售后说明
        sphxdProduct.setAftersaleDesc("支持7天无理由退货");

        // 设置额外服务
        SphxdProductAggregate.ExtraService extraService = new SphxdProductAggregate.ExtraService();
        extraService.setSevenDayReturn(1); // 支持7天无理由退货
        extraService.setFreightInsurance(0); // 不支持运费险
        sphxdProduct.setExtraService(extraService);

        // 设置SKU信息
        updateSkusFromLocalData(sphxdProduct, productListingDetailAggregate);

        // 设置售后地址
        SphxdProductAggregate.AfterSaleInfo afterSaleInfo = new SphxdProductAggregate.AfterSaleInfo();
        if (ecommerceShop != null && StringUtils.hasLength(ecommerceShop.getAccessToken())) {
            Long addressId = getAfterSaleAddressId(ecommerceShop.getAccessToken());
            afterSaleInfo.setAfterSaleAddressId(addressId);
        } else {
            afterSaleInfo.setAfterSaleAddressId(1L); // 默认地址ID
        }
        sphxdProduct.setAfterSaleInfo(afterSaleInfo);

        // 默认不上架，等待审核通过后再上架
        sphxdProduct.setListing(BooleanEnum.FALSE.getId());

        // 默认品牌
        sphxdProduct.setBrandId("2100000000");

        // 设置商家自定义编码
        sphxdProduct.setSpuCode(productListing.getProductListingId().toString());
    }

    /**
     * 从本地数据更新SKU信息
     * 
     * @param sphxdProduct 视频号小店产品
     * @param productListingDetailAggregate 商品详情
     */
    private void updateSkusFromLocalData(SphxdProductAggregate sphxdProduct, ProductListingDetailAggregate productListingDetailAggregate) {
        List<ProductSkuEntity> skus = productListingDetailAggregate.getProductSku();
        if (!CollectionUtils.isEmpty(skus)) {
            List<SphxdProductAggregate.Sku> sphxdSkus = new ArrayList<>();

            for (ProductSkuEntity sku : skus) {
                SphxdProductAggregate.Sku sphxdSku = new SphxdProductAggregate.Sku();
                sphxdSku.setOutSkuId(sku.getSkuId().toString()); // 使用内部SKU ID作为外部ID
                sphxdSku.setSalePrice(sku.getSalePrice() != null ? sku.getSalePrice() : 0); // 售卖价格已经是分为单位
                sphxdSku.setStockNum(sku.getStock() != null ? sku.getStock() : 0);
                sphxdSku.setSkuCode(sku.getProductSkuOuterId());

                // 设置SKU属性 获取SKU属性
                List<SphxdProductAggregate.SkuAttribute> skuAttrs = new ArrayList<>();
                for (String spec : sku.getProductSkuProps().split(";")) {
                    if (org.apache.commons.lang3.StringUtils.isBlank(spec)) {
                        continue;
                    }
                    SphxdProductAggregate.SkuAttribute skuAttribute = new SphxdProductAggregate.SkuAttribute();
                    // 规格信息，适配【绿色清新香2瓶】、【颜色:白色】
                    String[] attrInfo = spec.split(":");
                    if (attrInfo.length == 1) {
                        skuAttribute.setAttrKey("规格");
                        skuAttribute.setAttrValue(attrInfo[0].trim());
                    } else {
                        skuAttribute.setAttrKey(attrInfo[0].trim());
                        skuAttribute.setAttrValue(attrInfo[1].trim());
                    }
                    skuAttrs.add(skuAttribute);
                }
                sphxdSku.setSkuAttrs(skuAttrs);

                // 设置发货信息，默认现货
                SphxdProductAggregate.SkuDeliverInfo skuDeliverInfo = new SphxdProductAggregate.SkuDeliverInfo();
                skuDeliverInfo.setStockType(0); // 现货
                sphxdSku.setSkuDeliverInfo(skuDeliverInfo);

                sphxdSkus.add(sphxdSku);
            }

            sphxdProduct.setSkus(sphxdSkus);
        }
    }

    @Override
    public void transRelationIdAfterAddPlatformProductSuccess(ECommerceProduct eCommerceProduct, AddPlatformProductResponseVO response) {
        // 直接反射方式可能存在兼容性问题，这里尝试使用强制类型转换
        if (eCommerceProduct != null && eCommerceProduct.getProductModel() != null && response != null) {
            ECommerceProductModel model = eCommerceProduct.getProductModel();
            if (model instanceof SphxdProductAggregate) {
                SphxdProductAggregate productAggregate = (SphxdProductAggregate) model;
                productAggregate.setEcommerceProductId(response.getPlatformProductId());
            }
        }
    }

    @Override
    public ErpResponseVO updateShelfStatus(ECommerceShopVO ecommerceShop, ProductListingDetailAggregate aggregate, ProductListingTaskEntity task, boolean isOnShelf, Callback callback) {
        // 这个方法在当前需求中不需要实现
        return null;
    }

    @Override
    public Boolean updateProductStock(ProductListingDetailAggregate productListingDetailAggregate, ECommerceShopVO ecommerceShop, ProductListingTaskEntity productListingTaskEntity, Callback setProcessData) {
        // 这个方法在当前需求中不需要实现
        return null;
    }

    @Override
    public AddPlatformProductResponseVO updateProduct(ECommerceProduct eCommerceProduct, Long accountId, Callback setProcessData) {
        // 这个方法在当前需求中不需要实现
        return null;
    }

    @Override
    public AddPlatformProductResponseVO addProduct(ECommerceProduct<?> eCommerceProduct, Long accountId, Long productListingId, Callback setProcessData, String accessToken) {
        log.info("添加商品到视频号小店, productListingId: {}, accountId: {}", productListingId, accountId);
        
        // 参数校验
        if (eCommerceProduct == null) {
            throw new BusinessException("商品信息为空");
        }
        
        // 获取商品数据
        SphxdProductAggregate sphxdProduct = null;
        if (eCommerceProduct.getProductModel() instanceof SphxdProductAggregate) {
            sphxdProduct = (SphxdProductAggregate) eCommerceProduct.getProductModel();
        } else {
            throw new BusinessException("商品格式不正确，应为SphxdProductAggregate类型");
        }
        
        // 准备请求体
        String requestBody = JsonUtils.toJsonString(sphxdProduct);
        
        try {
            // 调用视频号小店API添加商品
            SphxdResponseVO response = sphxdLinkPort.addProduct(accessToken, requestBody);
            
            // 设置处理数据，便于记录日志和追踪
            if (response.isSuccess()) {
                // 成功处理
                setProcessData.accept(requestBody, JsonUtils.toJsonString(response.getResult()), null);
                
                // 提取商品ID和构建响应对象
                SphxdProductAggregate.Response sphxdResponse = (SphxdProductAggregate.Response) response.getResult().getData();
                
                // 构造响应
                AddPlatformProductResponseVO addResponse = new AddPlatformProductResponseVO();
                if (sphxdResponse != null && sphxdResponse.getData() != null) {
                    addResponse.setPlatformProductId(sphxdResponse.getData().getProductId());
                    
                    // 处理SKU映射关系
                    // 这里假设SKU关系有特定逻辑，实际需要根据业务需求调整
                    Map<Long, String> skuIdMap = new HashMap<>();
                    for (SphxdProductAggregate.Sku sku : sphxdProduct.getSkus()) {
                        try {
                            Long skuId = Long.valueOf(sku.getOutSkuId());
                            // 这里需要实际的SKU ID映射逻辑
                            // 简化处理，使用相同的ID
                            skuIdMap.put(skuId, sku.getOutSkuId());
                        } catch (NumberFormatException e) {
                            log.warn("SKU ID格式错误: {}", sku.getOutSkuId());
                        }
                    }
                    addResponse.setPlatformProductSkuIdMap(skuIdMap);
                }
                
                return addResponse;
            } else {
                // 失败处理
                SphxdRestResult<?> result = response.getResult();
                String errorMsg = String.format("添加商品失败: 错误码=%s, 错误信息=%s",
                        result.getErrcode(), result.getErrmsg());
                setProcessData.accept(requestBody, JsonUtils.toJsonString(result), errorMsg);
                throw new BusinessException(errorMsg);
            }
        } catch (BusinessException e) {
            // 直接抛出已封装的业务异常
            throw e;
        } catch (Exception e) {
            // 处理其他异常
            String errorMsg = "添加商品异常: " + e.getMessage();
            setProcessData.accept(requestBody, null, errorMsg);
            throw new BusinessException(errorMsg);
        }
    }
}
