2025-08-01 14:34:13.238 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848
2025-08-01 14:34:13.240 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000
2025-08-01 14:34:13.242 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0
2025-08-01 14:34:13.245 [Thread-2] INFO  [ecommate-infra-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-08-01 14:34:13.263 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels.....
2025-08-01 14:34:13.263 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-08-01 14:34:13.263 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null
2025-08-01 14:34:13.263 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect properties labels: {}
2025-08-01 14:34:13.264 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null
2025-08-01 14:34:13.264 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect jvm labels: {}
2025-08-01 14:34:13.264 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect env raw labels: null
2025-08-01 14:34:13.264 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect env labels: {}
2025-08-01 14:34:13.264 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-08-01 14:34:13.275 [main] INFO  [ecommate-infra-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 14:34:13.276 [main] INFO  [ecommate-infra-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 14:34:13.312 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2025-08-01 14:34:13.430 [main] INFO  [ecommate-infra-server] c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-08-01 14:34:13.436 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of df33018d-ba8b-4f10-a0a3-ecab67782306_config-0
2025-08-01 14:34:13.453 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$636/0x0000025d543bccb0
2025-08-01 14:34:13.453 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$637/0x0000025d543bd0d0
2025-08-01 14:34:13.453 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-08-01 14:34:13.454 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-08-01 14:34:13.459 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-08-01 14:34:13.486 [main] INFO  [ecommate-infra-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-08-01 14:34:14.337 [main] INFO  [ecommate-infra-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to get current node abilities...
2025-08-01 14:34:14.339 [main] INFO  [ecommate-infra-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-08-01 14:34:14.339 [main] INFO  [ecommate-infra-server] c.a.n.common.ability.AbstractAbilityControlManager - Initialize current abilities finish...
2025-08-01 14:34:14.340 [main] INFO  [ecommate-infra-server] c.a.n.c.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-08-01 14:34:14.361 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1754030043935_192.168.48.1_55508
2025-08-01 14:34:14.362 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] Notify connected event to listeners.
2025-08-01 14:34:14.362 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.ClientWorker - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] Connected,notify listen context...
2025-08-01 14:34:14.362 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-08-01 14:34:14.362 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [df33018d-ba8b-4f10-a0a3-ecab67782306_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$657/0x0000025d54536a20
2025-08-01 14:34:14.413 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.utils.JvmUtil - isMultiInstance:false
2025-08-01 14:34:14.609 [main] INFO  [ecommate-infra-server] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-ecommate-infra-server-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-infra-server.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-infra-server,DEFAULT_GROUP'}]
2025-08-01 14:34:14.636 [main] INFO  [ecommate-infra-server] com.yaotown.ecommate.infra.web.InfraApplication - The following 1 profile is active: "local"
2025-08-01 14:34:16.609 [main] INFO  [ecommate-infra-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:34:16.615 [main] INFO  [ecommate-infra-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:34:16.649 [main] INFO  [ecommate-infra-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-08-01 14:34:17.090 [main] INFO  [ecommate-infra-server] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=ca283a31-9e70-36c0-b596-5876f5ad1d25
2025-08-01 14:34:18.888 [main] INFO  [ecommate-infra-server] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:34:18.888 [main] INFO  [ecommate-infra-server] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4241 ms
2025-08-01 14:34:21.449 [main] INFO  [ecommate-infra-server] org.redisson.Version - Redisson 3.32.0
2025-08-01 14:34:21.810 [redisson-netty-1-4] INFO  [ecommate-infra-server] org.redisson.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6379
2025-08-01 14:34:22.088 [redisson-netty-1-19] INFO  [ecommate-infra-server] org.redisson.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6379
2025-08-01 14:34:23.906 [main] INFO  [ecommate-infra-server] o.a.r.s.autoconfigure.RocketMQAutoConfiguration - a producer (ecommate-infra-server) init on namesrv **************:9876
2025-08-01 14:34:26.445 [main] INFO  [ecommate-infra-server] c.a.c.config.AjCaptchaServiceAutoConfiguration - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='源汇通', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-08-01 14:34:26.449 [main] INFO  [ecommate-infra-server] c.anji.captcha.service.impl.CaptchaServiceFactory - supported-captchaCache-service:[local]
2025-08-01 14:34:26.456 [main] INFO  [ecommate-infra-server] c.anji.captcha.service.impl.CaptchaServiceFactory - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-08-01 14:34:26.495 [main] INFO  [ecommate-infra-server] com.anji.captcha.util.ImageUtils - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@7668b4ff, ROTATE=[Ljava.lang.String;@2ef86335, ORIGINAL=[Ljava.lang.String;@2fa7a4d3, PIC_CLICK=[Ljava.lang.String;@65f913fd, ROTATE_BLOCK=[Ljava.lang.String;@56059cf1]
2025-08-01 14:34:26.496 [main] INFO  [ecommate-infra-server] c.a.c.service.impl.BlockPuzzleCaptchaServiceImpl - --->>>初始化验证码底图<<<---blockPuzzle
2025-08-01 14:34:26.679 [main] INFO  [ecommate-infra-server] c.a.c.service.impl.BlockPuzzleCaptchaServiceImpl - 初始化local缓存...
2025-08-01 14:34:27.397 [main] INFO  [ecommate-infra-server] c.y.s.m.module.handler.handler.impl.EmailHandler - EmailHandler#init Rate Limit Strategy: requestRateLimit Value: 5.0
2025-08-01 14:34:27.497 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.im.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.501 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.im.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.501 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.im.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.501 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.push.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.501 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.push.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.push.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.sms.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.sms.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.sms.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.email.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.email.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.email.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.official_accounts.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.official_accounts.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.official_accounts.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.502 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.mini_program.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.503 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.mini_program.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.503 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.mini_program.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.503 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.503 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.503 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.503 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_robot.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.503 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_robot.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.503 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_robot.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.503 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_work_notice.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_work_notice.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_work_notice.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat_robot.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat_robot.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat_robot.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_robot.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_robot.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_robot.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.alipay_mini_program.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.alipay_mini_program.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.alipay_mini_program.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_application_robot.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.504 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_application_robot.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.505 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_application_robot.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-08-01 14:34:27.824 [main] INFO  [ecommate-infra-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:rocketMqBizReceiver, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-01 14:34:28.076 [main] INFO  [ecommate-infra-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:rocketMqRecallReceiver, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-01 14:34:30.735 [main] INFO  [ecommate-infra-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-01 14:34:32.308 [main] INFO  [ecommate-infra-server] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 14:34:32.563 [main] INFO  [ecommate-infra-server] c.y.ecommate.common.id.config.IdAutoConfiguration - 构建ID生成器时使用随机workId，它的值为: 25
2025-08-01 14:34:34.483 [main] INFO  [ecommate-infra-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-01 14:34:34.734 [main] INFO  [ecommate-infra-server] io.undertow - starting server: Undertow - 2.3.13.Final
2025-08-01 14:34:34.747 [main] INFO  [ecommate-infra-server] org.xnio - XNIO version 3.8.8.Final
2025-08-01 14:34:34.759 [main] INFO  [ecommate-infra-server] org.xnio.nio - XNIO NIO Implementation Version 3.8.8.Final
2025-08-01 14:34:34.787 [main] INFO  [ecommate-infra-server] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:34:34.832 [main] INFO  [ecommate-infra-server] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 38785 (http) with context path '/'
2025-08-01 14:34:34.836 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - initializer namespace from ans.namespace attribute : null
2025-08-01 14:34:34.836 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-08-01 14:34:34.837 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - initializer namespace from namespace attribute :null
2025-08-01 14:34:34.841 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-08-01 14:34:34.844 [main] INFO  [ecommate-infra-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 14:34:34.844 [main] INFO  [ecommate-infra-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 14:34:34.930 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 963fabb4-51fa-4106-bd96-ab20f176168a
2025-08-01 14:34:34.931 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - Create naming rpc client for uuid->963fabb4-51fa-4106-bd96-ab20f176168a
2025-08-01 14:34:34.931 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [963fabb4-51fa-4106-bd96-ab20f176168a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-08-01 14:34:34.931 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [963fabb4-51fa-4106-bd96-ab20f176168a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-08-01 14:34:34.931 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [963fabb4-51fa-4106-bd96-ab20f176168a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-08-01 14:34:34.932 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [963fabb4-51fa-4106-bd96-ab20f176168a] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-08-01 14:34:34.932 [main] INFO  [ecommate-infra-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-08-01 14:34:34.948 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [963fabb4-51fa-4106-bd96-ab20f176168a] Success to connect to server [**************:8848] on start up, connectionId = 1754030064627_192.168.48.1_55573
2025-08-01 14:34:34.948 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [963fabb4-51fa-4106-bd96-ab20f176168a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-08-01 14:34:34.948 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [963fabb4-51fa-4106-bd96-ab20f176168a] Notify connected event to listeners.
2025-08-01 14:34:34.948 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [963fabb4-51fa-4106-bd96-ab20f176168a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$657/0x0000025d54536a20
2025-08-01 14:34:34.948 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - Grpc connection connect
2025-08-01 14:34:34.949 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - [REGISTER-SERVICE] ecommate-cfx registering service ecommate-infra-server with instance Instance{instanceId='null', ip='*************', port=38785, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={preserved.heart.beat.timeout=3000, preserved.ip.delete.timeout=3000, preserved.register.source=SPRING_CLOUD, version=1.0.0, IPv6=[2408:8459:860:1569:4709:cc1d:2f0b:53b4], preserved.heart.beat.interval=1000}}
2025-08-01 14:34:34.958 [main] INFO  [ecommate-infra-server] c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ecommate-infra-server *************:38785 register finished
2025-08-01 14:34:36.077 [main] INFO  [ecommate-infra-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-01 14:34:36.859 [main] INFO  [ecommate-infra-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='unique-send-consumer-group_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='message-send_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='com.yaotown.sdk.message.module.ctsmd', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 14:34:37.505 [main] INFO  [ecommate-infra-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='message-recall_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='message-recall_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='com.yaotown.sdk.message.module.ctsmd', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 14:34:37.522 [main] INFO  [ecommate-infra-server] com.yaotown.ecommate.infra.web.InfraApplication - Started InfraApplication in 28.194 seconds (process running for 29.024)
2025-08-01 14:34:37.526 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - config listener notify warn timeout millis use default 60000 millis 
2025-08-01 14:34:37.527 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snapshot = true 
2025-08-01 14:34:37.527 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-infra-server+DEFAULT_GROUP+ecommate-cfx
2025-08-01 14:34:37.531 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-infra-server, group=DEFAULT_GROUP, cnt=1
2025-08-01 14:34:37.531 [main] INFO  [ecommate-infra-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-infra-server, group=DEFAULT_GROUP
2025-08-01 14:34:37.532 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-infra-server-local.yaml+DEFAULT_GROUP+ecommate-cfx
2025-08-01 14:34:37.532 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-infra-server-local.yaml, group=DEFAULT_GROUP, cnt=1
2025-08-01 14:34:37.532 [main] INFO  [ecommate-infra-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-infra-server-local.yaml, group=DEFAULT_GROUP
2025-08-01 14:34:37.533 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-infra-server.yaml+DEFAULT_GROUP+ecommate-cfx
2025-08-01 14:34:37.533 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-infra-server.yaml, group=DEFAULT_GROUP, cnt=1
2025-08-01 14:34:37.533 [main] INFO  [ecommate-infra-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-infra-server.yaml, group=DEFAULT_GROUP
2025-08-01 14:34:37.988 [RMI TCP Connection(2)-*************] INFO  [ecommate-infra-server] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:34:37.988 [RMI TCP Connection(2)-*************] INFO  [ecommate-infra-server] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:34:37.991 [RMI TCP Connection(2)-*************] INFO  [ecommate-infra-server] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-01 14:34:38.067 [RMI TCP Connection(4)-*************] INFO  [ecommate-infra-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-01 14:36:14.323 [XNIO-1 task-2] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/captcha/get) 参数({"captchaType":"blockPuzzle"})]
2025-08-01 14:36:14.322 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/captcha/get) 参数({"captchaType":"blockPuzzle"})]
2025-08-01 14:36:14.322 [XNIO-1 task-3] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/captcha/get) 参数({"captchaType":"blockPuzzle"})]
2025-08-01 14:36:14.529 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/captcha/get) 耗时(205 ms)]
2025-08-01 14:36:14.529 [XNIO-1 task-3] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/captcha/get) 耗时(205 ms)]
2025-08-01 14:36:14.530 [XNIO-1 task-2] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/captcha/get) 耗时(206 ms)]
2025-08-01 14:36:24.440 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/captcha/check) 参数({"captchaType":"blockPuzzle","pointJson":"w3uigfGPd8JpCWjNAJaCtMH0NI+ULRz5HMqeYGdD02I=","token":"fb9fd8e7ea4f4f59a6a191a20f586a1c"})]
2025-08-01 14:36:24.443 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/captcha/check) 耗时(4 ms)]
2025-08-01 14:36:25.476 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/account/login) 参数({"username":"admin","loginPassword":"abc.123456"})]
2025-08-01 14:36:25.793 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/account/login) 耗时(317 ms)]
2025-08-01 14:36:25.883 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/dict-data/simple-list) 无参数]
2025-08-01 14:36:25.966 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/dict-data/simple-list) 耗时(83 ms)]
2025-08-01 14:36:25.968 [XNIO-1 task-3] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/captcha/get) 参数({"captchaType":"blockPuzzle"})]
2025-08-01 14:36:25.996 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/account/get-permission-info) 无参数]
2025-08-01 14:36:26.003 [XNIO-1 task-3] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/captcha/get) 耗时(35 ms)]
2025-08-01 14:36:26.223 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/account/get-permission-info) 耗时(227 ms)]
2025-08-01 14:40:00.020 [scheduling-1] INFO  [ecommate-infra-server] c.y.e.infra.module.task.MessagePushScheduledTask - 开始扫描消息推送任务
2025-08-01 14:40:00.021 [scheduling-1] INFO  [ecommate-infra-server] c.y.e.infra.module.task.MessagePushScheduledTask - 扫描消息推送任务结束
2025-08-01 14:50:58.919 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/rpc-api/infra/oauth2/token/check) 参数({accessToken=8978da2c-d133-410c-8166-6ed3801383ff})]
2025-08-01 14:50:58.959 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/rpc-api/infra/oauth2/token/check) 耗时(39 ms)]
2025-08-01 14:55:47.004 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/dict-data/simple-list) 无参数]
2025-08-01 14:55:47.054 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/dict-data/simple-list) 耗时(49 ms)]
2025-08-01 14:55:47.069 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/account/get-permission-info) 无参数]
2025-08-01 14:55:47.258 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/account/get-permission-info) 耗时(189 ms)]
2025-08-01 14:57:06.449 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/dict-data/simple-list) 无参数]
2025-08-01 14:57:06.497 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/dict-data/simple-list) 耗时(47 ms)]
2025-08-01 14:57:06.516 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/infra/account/get-permission-info) 无参数]
2025-08-01 14:57:06.682 [XNIO-1 task-4] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/infra/account/get-permission-info) 耗时(166 ms)]
2025-08-01 15:00:00.000 [scheduling-1] INFO  [ecommate-infra-server] c.y.e.infra.module.task.MessagePushScheduledTask - 开始扫描消息推送任务
2025-08-01 15:00:00.000 [scheduling-1] INFO  [ecommate-infra-server] c.y.e.infra.module.task.MessagePushScheduledTask - 扫描消息推送任务结束
