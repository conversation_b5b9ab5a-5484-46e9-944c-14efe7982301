2025-08-01 14:34:30.359 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848
2025-08-01 14:34:30.360 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000
2025-08-01 14:34:30.363 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0
2025-08-01 14:34:30.366 [Thread-2] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-08-01 14:34:30.404 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels.....
2025-08-01 14:34:30.440 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-08-01 14:34:30.440 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null
2025-08-01 14:34:30.443 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect properties labels: {}
2025-08-01 14:34:30.443 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null
2025-08-01 14:34:30.443 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect jvm labels: {}
2025-08-01 14:34:30.443 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect env raw labels: null
2025-08-01 14:34:30.443 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect env labels: {}
2025-08-01 14:34:30.444 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-08-01 14:34:30.457 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 14:34:30.457 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 14:34:30.522 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2025-08-01 14:34:30.677 [main] INFO  [ecommate-product-server] c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-08-01 14:34:30.683 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 35b85447-b71c-4472-897f-b8a181254c47_config-0
2025-08-01 14:34:30.718 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$639/0x000001415542f7b8
2025-08-01 14:34:30.718 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$640/0x000001415542fbd8
2025-08-01 14:34:30.719 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-08-01 14:34:30.719 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-08-01 14:34:30.729 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 9848}
2025-08-01 14:34:30.767 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:10848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-08-01 14:34:34.472 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-08-01 14:34:34.472 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-08-01 14:34:34.606 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to get current node abilities...
2025-08-01 14:34:34.611 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-08-01 14:34:34.612 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Initialize current abilities finish...
2025-08-01 14:34:34.612 [main] INFO  [ecommate-product-server] c.a.n.c.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-08-01 14:34:34.635 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1754030064171_192.168.48.1_55571
2025-08-01 14:34:34.636 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Notify connected event to listeners.
2025-08-01 14:34:34.637 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Connected,notify listen context...
2025-08-01 14:34:34.637 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-08-01 14:34:34.638 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [35b85447-b71c-4472-897f-b8a181254c47_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$660/0x000001415558ee68
2025-08-01 14:34:34.749 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.utils.JvmUtil - isMultiInstance:false
2025-08-01 14:34:34.948 [main] INFO  [ecommate-product-server] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server,DEFAULT_GROUP'}]
2025-08-01 14:34:34.982 [main] INFO  [ecommate-product-server] com.yaotown.ecommate.product.ProductApplication - The following 1 profile is active: "local"
2025-08-01 14:34:37.084 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:34:37.086 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-01 14:34:37.622 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 530 ms. Found 3 MongoDB repository interfaces.
2025-08-01 14:34:37.638 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:34:37.640 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:34:37.685 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.domain.product.compute.repository.AttributeDefinitionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-08-01 14:34:37.685 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.infrastructure.mapper.compute.AttributeMappingMapper; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-08-01 14:34:37.685 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.infrastructure.mapper.compute.AttributeOptionMappingMapper; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-08-01 14:34:37.685 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-08-01 14:34:38.367 [main] INFO  [ecommate-product-server] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=6fb7e30e-2a45-3446-8cd4-82ddce71f28e
2025-08-01 14:34:39.743 [main] INFO  [ecommate-product-server] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:34:39.743 [main] INFO  [ecommate-product-server] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4743 ms
2025-08-01 14:34:40.348 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:34:40.441 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:34:41.985 [main] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2025-08-01 14:34:42.694 [main] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2,db_center} inited
2025-08-01 14:34:42.695 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [db_center] success
2025-08-01 14:34:42.695 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 14:34:42.696 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 14:34:44.280 [main] INFO  [ecommate-product-server] org.redisson.Version - Redisson 3.32.0
2025-08-01 14:34:44.713 [redisson-netty-1-4] INFO  [ecommate-product-server] org.redisson.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6379
2025-08-01 14:34:44.982 [redisson-netty-1-19] INFO  [ecommate-product-server] org.redisson.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6379
2025-08-01 14:34:45.535 [main] INFO  [ecommate-product-server] org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.0.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Alibaba/17.0.14+7"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='yaotown_product', source='yaotown_product', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[com.yaotown.ecommate.product.config.MongoQueryLogConfig$MongoQueryCommandListener@4473bbc1, io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@3b4aeacd], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@433f8efa, com.mongodb.Jep395RecordCodecProvider@bb4fc7d, com.mongodb.KotlinCodecProvider@55eb9b2c]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@fbd9b1f], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null}
2025-08-01 14:34:45.570 [cluster-ClusterId{value='688c6005aa94d44add557bb9', description='null'}-**************:27017] INFO  [ecommate-product-server] org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=**************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=37475300}
2025-08-01 14:34:46.213 [main] INFO  [ecommate-product-server] c.y.e.p.i.repository.CategoryAttributeRepository - 初始化MongoDB索引
2025-08-01 14:34:46.479 [main] INFO  [ecommate-product-server] c.y.e.p.i.repository.CategoryAttributeRepository - MongoDB索引创建完成
2025-08-01 14:34:46.857 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:34:47.650 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:34:47.657 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:34:47.663 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:34:48.287 [main] INFO  [ecommate-product-server] o.a.r.s.autoconfigure.RocketMQAutoConfiguration - a producer (ecommate-product-server) init on namesrv **************:9876
2025-08-01 14:34:49.377 [main] INFO  [ecommate-product-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:accountRegisterMessageConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-01 14:34:49.618 [main] INFO  [ecommate-product-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:listingTaskHandleMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-01 14:34:49.635 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:34:51.997 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-01 14:34:53.550 [main] INFO  [ecommate-product-server] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 14:34:53.844 [main] INFO  [ecommate-product-server] c.y.ecommate.common.id.config.IdAutoConfiguration - 构建ID生成器时使用随机workId，它的值为: 24
2025-08-01 14:34:55.627 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-01 14:34:55.827 [main] INFO  [ecommate-product-server] io.undertow - starting server: Undertow - 2.3.13.Final
2025-08-01 14:34:55.847 [main] INFO  [ecommate-product-server] org.xnio - XNIO version 3.8.8.Final
2025-08-01 14:34:55.866 [main] INFO  [ecommate-product-server] org.xnio.nio - XNIO NIO Implementation Version 3.8.8.Final
2025-08-01 14:34:55.916 [main] INFO  [ecommate-product-server] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:34:55.988 [main] INFO  [ecommate-product-server] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 38786 (http) with context path '/'
2025-08-01 14:34:55.996 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from ans.namespace attribute : null
2025-08-01 14:34:55.996 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-08-01 14:34:55.997 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from namespace attribute :null
2025-08-01 14:34:56.009 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-08-01 14:34:56.016 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 14:34:56.017 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 14:34:56.108 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 998e56fb-8636-4869-b8ae-5808488d8742
2025-08-01 14:34:56.115 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Create naming rpc client for uuid->998e56fb-8636-4869-b8ae-5808488d8742
2025-08-01 14:34:56.115 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [998e56fb-8636-4869-b8ae-5808488d8742] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-08-01 14:34:56.115 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [998e56fb-8636-4869-b8ae-5808488d8742] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-08-01 14:34:56.116 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [998e56fb-8636-4869-b8ae-5808488d8742] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-08-01 14:34:56.116 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [998e56fb-8636-4869-b8ae-5808488d8742] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-08-01 14:34:56.117 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-08-01 14:34:56.136 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [998e56fb-8636-4869-b8ae-5808488d8742] Success to connect to server [**************:8848] on start up, connectionId = 1754030085819_192.168.48.1_55609
2025-08-01 14:34:56.137 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [998e56fb-8636-4869-b8ae-5808488d8742] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-08-01 14:34:56.137 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [998e56fb-8636-4869-b8ae-5808488d8742] Notify connected event to listeners.
2025-08-01 14:34:56.137 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [998e56fb-8636-4869-b8ae-5808488d8742] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$660/0x000001415558ee68
2025-08-01 14:34:56.137 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Grpc connection connect
2025-08-01 14:34:56.139 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [REGISTER-SERVICE] ecommate-cfx registering service ecommate-product-server with instance Instance{instanceId='null', ip='*************', port=38786, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={preserved.heart.beat.timeout=3000, preserved.ip.delete.timeout=3000, preserved.register.source=SPRING_CLOUD, version=1.0.0, IPv6=[2408:8459:860:1569:4709:cc1d:2f0b:53b4], preserved.heart.beat.interval=1000}}
2025-08-01 14:34:56.152 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ecommate-product-server *************:38786 register finished
2025-08-01 14:34:57.276 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-01 14:34:58.202 [main] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-enterprise-create-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-enterprise-create_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 14:34:58.973 [main] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-task-handle-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-task-handle_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 14:34:58.989 [main] INFO  [ecommate-product-server] com.yaotown.ecommate.product.ProductApplication - Started ProductApplication in 32.777 seconds (process running for 34.475)
2025-08-01 14:34:58.997 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - config listener notify warn timeout millis use default 60000 millis 
2025-08-01 14:34:58.997 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snapshot = true 
2025-08-01 14:34:58.997 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server+DEFAULT_GROUP+ecommate-cfx
2025-08-01 14:34:59.014 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server, group=DEFAULT_GROUP, cnt=1
2025-08-01 14:34:59.014 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server, group=DEFAULT_GROUP
2025-08-01 14:34:59.015 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server-local.yaml+DEFAULT_GROUP+ecommate-cfx
2025-08-01 14:34:59.015 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server-local.yaml, group=DEFAULT_GROUP, cnt=1
2025-08-01 14:34:59.015 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server-local.yaml, group=DEFAULT_GROUP
2025-08-01 14:34:59.017 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server.yaml+DEFAULT_GROUP+ecommate-cfx
2025-08-01 14:34:59.017 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server.yaml, group=DEFAULT_GROUP, cnt=1
2025-08-01 14:34:59.017 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server.yaml, group=DEFAULT_GROUP
2025-08-01 14:34:59.139 [RMI TCP Connection(6)-*************] INFO  [ecommate-product-server] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:34:59.139 [RMI TCP Connection(6)-*************] INFO  [ecommate-product-server] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:34:59.143 [RMI TCP Connection(6)-*************] INFO  [ecommate-product-server] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-08-01 14:36:34.899 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/YHT/0) 无参数]
2025-08-01 14:36:36.316 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/YHT/0) 耗时(1416 ms)]
2025-08-01 14:40:17.772 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/details) 参数({"categoryId":"20019","platform":"YHT"})]
2025-08-01 14:40:18.043 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/details) 耗时(270 ms)]
2025-08-01 14:40:21.113 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/update) 参数({"id":"20019","platform":"YHT","name":"模玩/动漫/周边/娃圈/三坑/桌游","imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/45415/20250514/1c705e47ee524581b46ca203571a7db3.png","parentId":"0","level":0,"leaf":0,"showStatus":1})]
2025-08-01 14:40:21.275 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/update) 耗时(161 ms)]
2025-08-01 14:43:41.953 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] io.undertow - stopping server: Undertow - 2.3.13.Final
2025-08-01 14:43:41.957 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01 14:43:41.963 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-01 14:43:41.963 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] ecommate-cfx deregistering service ecommate-product-server with instance: Instance{instanceId='null', ip='*************', port=38786, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
2025-08-01 14:43:41.969 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->998e56fb-8636-4869-b8ae-5808488d8742
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@72d1a37d[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 175]
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7ec085b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-08-01 14:43:41.971 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - Close current connection 1754030085819_192.168.48.1_55609
2025-08-01 14:43:41.971 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2c3606ec[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 109]
2025-08-01 14:43:41.971 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->998e56fb-8636-4869-b8ae-5808488d8742
2025-08-01 14:43:41.971 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-08-01 14:43:41.971 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialService - [null] CredentialService is freed
2025-08-01 14:43:41.971 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
2025-08-01 14:43:41.975 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-task-handle-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-task-handle_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 14:43:41.975 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-enterprise-create-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-enterprise-create_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 14:43:45.292 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-01 14:43:45.297 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-08-01 14:43:45.336 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-08-01 14:43:45.336 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.b.d.d.destroyer.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [db_center] success,
2025-08-01 14:43:45.336 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-01 14:43:45.336 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-01 14:43:45.336 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.b.d.d.destroyer.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-01 14:43:45.336 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-01 14:43:53.306 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848
2025-08-01 14:43:53.306 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000
2025-08-01 14:43:53.308 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0
2025-08-01 14:43:53.314 [Thread-2] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-08-01 14:43:53.347 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels.....
2025-08-01 14:43:53.347 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-08-01 14:43:53.347 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null
2025-08-01 14:43:53.348 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect properties labels: {}
2025-08-01 14:43:53.348 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null
2025-08-01 14:43:53.348 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect jvm labels: {}
2025-08-01 14:43:53.348 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect env raw labels: null
2025-08-01 14:43:53.348 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect env labels: {}
2025-08-01 14:43:53.349 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-08-01 14:43:53.367 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 14:43:53.367 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 14:43:53.439 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2025-08-01 14:43:53.596 [main] INFO  [ecommate-product-server] c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-08-01 14:43:53.603 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of e2acc357-e459-4a45-b860-5345156ad95d_config-0
2025-08-01 14:43:53.642 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$639/0x00000216da42f288
2025-08-01 14:43:53.642 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$640/0x00000216da42f6a8
2025-08-01 14:43:53.643 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-08-01 14:43:53.644 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-08-01 14:43:53.653 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-08-01 14:43:53.690 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-08-01 14:43:55.038 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to get current node abilities...
2025-08-01 14:43:55.041 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-08-01 14:43:55.043 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Initialize current abilities finish...
2025-08-01 14:43:55.043 [main] INFO  [ecommate-product-server] c.a.n.c.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-08-01 14:43:55.065 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1754030624586_192.168.48.1_55865
2025-08-01 14:43:55.066 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] Notify connected event to listeners.
2025-08-01 14:43:55.066 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] Connected,notify listen context...
2025-08-01 14:43:55.067 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-08-01 14:43:55.067 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e2acc357-e459-4a45-b860-5345156ad95d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$660/0x00000216da588d88
2025-08-01 14:43:55.159 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.utils.JvmUtil - isMultiInstance:false
2025-08-01 14:43:55.354 [main] INFO  [ecommate-product-server] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server,DEFAULT_GROUP'}]
2025-08-01 14:43:55.391 [main] INFO  [ecommate-product-server] com.yaotown.ecommate.product.ProductApplication - The following 1 profile is active: "local"
2025-08-01 14:43:57.652 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:43:57.654 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-01 14:43:58.265 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 605 ms. Found 3 MongoDB repository interfaces.
2025-08-01 14:43:58.280 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 14:43:58.282 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 14:43:58.321 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.domain.product.compute.repository.AttributeDefinitionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-08-01 14:43:58.322 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.infrastructure.mapper.compute.AttributeMappingMapper; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-08-01 14:43:58.322 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.infrastructure.mapper.compute.AttributeOptionMappingMapper; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-08-01 14:43:58.322 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-08-01 14:43:58.883 [main] INFO  [ecommate-product-server] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=6fb7e30e-2a45-3446-8cd4-82ddce71f28e
2025-08-01 14:44:00.243 [main] INFO  [ecommate-product-server] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-01 14:44:00.243 [main] INFO  [ecommate-product-server] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4837 ms
2025-08-01 14:44:00.796 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:44:00.892 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:44:02.480 [main] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2025-08-01 14:44:03.183 [main] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2,db_center} inited
2025-08-01 14:44:03.184 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [db_center] success
2025-08-01 14:44:03.184 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-01 14:44:03.184 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-01 14:44:04.877 [main] INFO  [ecommate-product-server] org.redisson.Version - Redisson 3.32.0
2025-08-01 14:44:05.359 [redisson-netty-1-6] INFO  [ecommate-product-server] org.redisson.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6379
2025-08-01 14:44:05.486 [redisson-netty-1-19] INFO  [ecommate-product-server] org.redisson.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6379
2025-08-01 14:44:05.999 [main] INFO  [ecommate-product-server] org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.0.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Alibaba/17.0.14+7"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='yaotown_product', source='yaotown_product', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[com.yaotown.ecommate.product.config.MongoQueryLogConfig$MongoQueryCommandListener@5f18f8a1, io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@7921eb37], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@26d63c94, com.mongodb.Jep395RecordCodecProvider@4e42beba, com.mongodb.KotlinCodecProvider@73021987]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@5c501f62], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null}
2025-08-01 14:44:06.046 [cluster-ClusterId{value='688c623576c54b04c6e8f505', description='null'}-**************:27017] INFO  [ecommate-product-server] org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=**************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=39343400}
2025-08-01 14:44:06.736 [main] INFO  [ecommate-product-server] c.y.e.p.i.repository.CategoryAttributeRepository - 初始化MongoDB索引
2025-08-01 14:44:06.980 [main] INFO  [ecommate-product-server] c.y.e.p.i.repository.CategoryAttributeRepository - MongoDB索引创建完成
2025-08-01 14:44:07.383 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:44:08.241 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:44:08.248 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:44:08.256 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:44:08.885 [main] INFO  [ecommate-product-server] o.a.r.s.autoconfigure.RocketMQAutoConfiguration - a producer (ecommate-product-server) init on namesrv **************:9876
2025-08-01 14:44:10.007 [main] INFO  [ecommate-product-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:accountRegisterMessageConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-01 14:44:10.236 [main] INFO  [ecommate-product-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:listingTaskHandleMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-01 14:44:10.291 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-08-01 14:44:12.633 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-01 14:44:14.211 [main] INFO  [ecommate-product-server] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-01 14:44:14.485 [main] INFO  [ecommate-product-server] c.y.ecommate.common.id.config.IdAutoConfiguration - 构建ID生成器时使用随机workId，它的值为: 15
2025-08-01 14:44:16.203 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-01 14:44:16.400 [main] INFO  [ecommate-product-server] io.undertow - starting server: Undertow - 2.3.13.Final
2025-08-01 14:44:16.416 [main] INFO  [ecommate-product-server] org.xnio - XNIO version 3.8.8.Final
2025-08-01 14:44:16.432 [main] INFO  [ecommate-product-server] org.xnio.nio - XNIO NIO Implementation Version 3.8.8.Final
2025-08-01 14:44:16.490 [main] INFO  [ecommate-product-server] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-01 14:44:16.564 [main] INFO  [ecommate-product-server] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 38786 (http) with context path '/'
2025-08-01 14:44:16.572 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from ans.namespace attribute : null
2025-08-01 14:44:16.572 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-08-01 14:44:16.572 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from namespace attribute :null
2025-08-01 14:44:16.581 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-08-01 14:44:16.587 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 14:44:16.587 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 14:44:16.661 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 270b1ba9-cf03-4175-8a2e-b2e4a6468f03
2025-08-01 14:44:16.665 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Create naming rpc client for uuid->270b1ba9-cf03-4175-8a2e-b2e4a6468f03
2025-08-01 14:44:16.665 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-08-01 14:44:16.665 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-08-01 14:44:16.666 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-08-01 14:44:16.666 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-08-01 14:44:16.667 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-08-01 14:44:16.686 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] Success to connect to server [**************:8848] on start up, connectionId = 1754030646365_192.168.48.1_55899
2025-08-01 14:44:16.686 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-08-01 14:44:16.686 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] Notify connected event to listeners.
2025-08-01 14:44:16.686 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$660/0x00000216da588d88
2025-08-01 14:44:16.686 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Grpc connection connect
2025-08-01 14:44:16.688 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [REGISTER-SERVICE] ecommate-cfx registering service ecommate-product-server with instance Instance{instanceId='null', ip='*************', port=38786, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={preserved.heart.beat.timeout=3000, preserved.ip.delete.timeout=3000, preserved.register.source=SPRING_CLOUD, version=1.0.0, IPv6=[2408:8459:860:1569:4709:cc1d:2f0b:53b4], preserved.heart.beat.interval=1000}}
2025-08-01 14:44:16.699 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ecommate-product-server *************:38786 register finished
2025-08-01 14:44:17.821 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-01 14:44:18.553 [main] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-enterprise-create-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-enterprise-create_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 14:44:19.216 [main] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-task-handle-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-task-handle_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-08-01 14:44:19.231 [main] INFO  [ecommate-product-server] com.yaotown.ecommate.product.ProductApplication - Started ProductApplication in 29.866 seconds (process running for 30.863)
2025-08-01 14:44:19.274 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - config listener notify warn timeout millis use default 60000 millis 
2025-08-01 14:44:19.275 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snapshot = true 
2025-08-01 14:44:19.276 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server+DEFAULT_GROUP+ecommate-cfx
2025-08-01 14:44:19.290 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server, group=DEFAULT_GROUP, cnt=1
2025-08-01 14:44:19.290 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server, group=DEFAULT_GROUP
2025-08-01 14:44:19.291 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server-local.yaml+DEFAULT_GROUP+ecommate-cfx
2025-08-01 14:44:19.291 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server-local.yaml, group=DEFAULT_GROUP, cnt=1
2025-08-01 14:44:19.291 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server-local.yaml, group=DEFAULT_GROUP
2025-08-01 14:44:19.291 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server.yaml+DEFAULT_GROUP+ecommate-cfx
2025-08-01 14:44:19.291 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server.yaml, group=DEFAULT_GROUP, cnt=1
2025-08-01 14:44:19.291 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server.yaml, group=DEFAULT_GROUP
2025-08-01 14:44:19.612 [RMI TCP Connection(2)-*************] INFO  [ecommate-product-server] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 14:44:19.612 [RMI TCP Connection(2)-*************] INFO  [ecommate-product-server] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-01 14:44:19.615 [RMI TCP Connection(2)-*************] INFO  [ecommate-product-server] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-01 14:45:19.989 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/update) 参数({"id":"20019","platform":"YHT","name":"模玩/动漫/周边/娃圈/三坑/桌游","imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/45415/20250514/1c705e47ee524581b46ca203571a7db3.png","parentId":"0","level":0,"leaf":0,"showStatus":1})]
2025-08-01 14:45:20.669 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/update) 耗时(678 ms)]
2025-08-01 14:45:44.202 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 无参数]
2025-08-01 14:45:44.204 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 无参数]
2025-08-01 14:45:44.297 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(94 ms)]
2025-08-01 14:45:44.374 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(169 ms)]
2025-08-01 14:45:46.356 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/FXG) 无参数]
2025-08-01 14:45:46.444 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/FXG) 耗时(87 ms)]
2025-08-01 14:46:53.840 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 无参数]
2025-08-01 14:46:53.841 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/FXG) 无参数]
2025-08-01 14:46:54.125 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(284 ms)]
2025-08-01 14:46:54.189 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/FXG) 耗时(348 ms)]
2025-08-01 14:48:54.536 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 无参数]
2025-08-01 14:48:54.796 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(260 ms)]
2025-08-01 14:49:00.949 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/XHS) 无参数]
2025-08-01 14:49:01.026 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/XHS) 耗时(78 ms)]
2025-08-01 14:49:04.539 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/SPHXD) 无参数]
2025-08-01 14:49:04.618 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/SPHXD) 耗时(79 ms)]
2025-08-01 14:49:07.002 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/SPHXD/1001) 参数({showStatus=1})]
2025-08-01 14:49:07.169 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/SPHXD/1001) 耗时(166 ms)]
2025-08-01 14:49:10.027 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/FXG) 无参数]
2025-08-01 14:49:10.043 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/FXG) 耗时(16 ms)]
2025-08-01 14:50:58.884 [XNIO-1 task-3] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:ecommate-infra-server, group:DEFAULT_GROUP, clusters: 
2025-08-01 14:50:58.884 [XNIO-1 task-3] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:ecommate-infra-server, group:DEFAULT_GROUP, cluster: 
2025-08-01 14:50:58.893 [XNIO-1 task-3] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@ecommate-infra-server -> [{"instanceId":"*************#38785##DEFAULT_GROUP@@ecommate-infra-server","ip":"*************","port":38785,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-infra-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8459:860:1569:4709:cc1d:2f0b:53b4]","version":"1.0.0","preserved.heart.beat.interval":"1000"},"instanceHeartBeatInterval":1000,"instanceHeartBeatTimeOut":3000,"ipDeleteTimeout":3000}]
2025-08-01 14:50:58.893 [XNIO-1 task-3] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@ecommate-infra-server -> [{"instanceId":"*************#38785##DEFAULT_GROUP@@ecommate-infra-server","ip":"*************","port":38785,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-infra-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8459:860:1569:4709:cc1d:2f0b:53b4]","version":"1.0.0","preserved.heart.beat.interval":"1000"},"instanceHeartBeatInterval":1000,"instanceHeartBeatTimeOut":3000,"ipDeleteTimeout":3000}]
2025-08-01 14:50:59.426 [nacos-grpc-client-executor-**************-85] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] Receive server push request, request = NotifySubscriberRequest, requestId = 752
2025-08-01 14:50:59.426 [nacos-grpc-client-executor-**************-85] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [270b1ba9-cf03-4175-8a2e-b2e4a6468f03] Ack server push request, request = NotifySubscriberRequest, requestId = 752
2025-08-01 14:51:51.240 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 参数({showStatus=1})]
2025-08-01 14:51:51.523 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(282 ms)]
2025-08-01 14:52:32.348 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 无参数]
2025-08-01 14:52:32.356 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(8 ms)]
2025-08-01 14:52:33.798 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/FXG) 无参数]
2025-08-01 14:52:33.810 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/FXG) 耗时(10 ms)]
2025-08-01 14:54:17.749 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 无参数]
2025-08-01 14:54:17.750 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 无参数]
2025-08-01 14:54:17.760 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(10 ms)]
2025-08-01 14:54:17.762 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(11 ms)]
2025-08-01 14:54:33.017 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 无参数]
2025-08-01 14:54:33.017 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 无参数]
2025-08-01 14:54:33.026 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(8 ms)]
2025-08-01 14:54:33.026 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(8 ms)]
2025-08-01 14:55:48.304 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 无参数]
2025-08-01 14:55:48.305 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 无参数]
2025-08-01 14:55:48.314 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(9 ms)]
2025-08-01 14:55:48.315 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(9 ms)]
2025-08-01 14:57:07.024 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 无参数]
2025-08-01 14:57:07.024 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 无参数]
2025-08-01 14:57:07.033 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(9 ms)]
2025-08-01 14:57:07.037 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(12 ms)]
2025-08-01 14:57:54.167 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 无参数]
2025-08-01 14:57:54.172 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 无参数]
2025-08-01 14:57:54.175 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(6 ms)]
2025-08-01 14:57:54.177 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(6 ms)]
2025-08-01 14:58:24.521 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 无参数]
2025-08-01 14:58:24.522 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 参数({showStatus=1})]
2025-08-01 14:58:24.527 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(5 ms)]
2025-08-01 14:58:24.527 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(4 ms)]
2025-08-01 14:58:37.208 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 参数({showStatus=1})]
2025-08-01 14:58:37.221 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(12 ms)]
2025-08-01 14:58:37.223 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 参数({showStatus=1})]
2025-08-01 14:58:37.487 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(263 ms)]
2025-08-01 14:59:13.905 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/FXG) 参数({showStatus=1})]
2025-08-01 14:59:14.017 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/FXG) 耗时(112 ms)]
2025-08-01 14:59:16.105 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 参数({showStatus=1})]
2025-08-01 14:59:16.111 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(5 ms)]
2025-08-01 14:59:17.094 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/XHS) 参数({showStatus=1})]
2025-08-01 14:59:17.169 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/XHS) 耗时(74 ms)]
2025-08-01 14:59:18.687 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/SPHXD) 参数({showStatus=1})]
2025-08-01 14:59:18.890 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/SPHXD) 耗时(202 ms)]
2025-08-01 14:59:23.047 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/YHT/0) 无参数]
2025-08-01 14:59:24.166 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/YHT/0) 耗时(1118 ms)]
2025-08-01 15:00:12.128 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/YHT) 参数({showStatus=1})]
2025-08-01 15:00:12.128 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/top/list/KWAISHOP) 参数({showStatus=1})]
2025-08-01 15:00:12.132 [XNIO-1 task-3] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/KWAISHOP) 耗时(4 ms)]
2025-08-01 15:00:12.135 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/top/list/YHT) 耗时(7 ms)]
2025-08-01 15:02:20.663 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/YHT/0) 无参数]
2025-08-01 15:02:21.056 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/YHT/0) 耗时(392 ms)]
2025-08-01 15:05:47.258 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/YHT/0) 无参数]
2025-08-01 15:05:47.690 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/YHT/0) 耗时(430 ms)]
2025-08-01 15:06:10.212 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/FXG/0) 无参数]
2025-08-01 15:06:11.639 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/FXG/0) 耗时(1426 ms)]
2025-08-01 15:06:28.139 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/FXG/0) 无参数]
2025-08-01 15:06:28.525 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/FXG/0) 耗时(385 ms)]
2025-08-01 15:06:40.435 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/FXG/0) 无参数]
2025-08-01 15:06:40.839 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/FXG/0) 耗时(403 ms)]
2025-08-01 15:09:32.751 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/KWAISHOP/0) 无参数]
2025-08-01 15:09:33.940 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/KWAISHOP/0) 耗时(1189 ms)]
2025-08-01 15:09:41.455 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/XHS/0) 无参数]
2025-08-01 15:09:41.826 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/XHS/0) 耗时(370 ms)]
2025-08-01 15:09:44.292 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/SPHXD/0) 无参数]
2025-08-01 15:09:45.013 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/SPHXD/0) 耗时(721 ms)]
2025-08-01 15:09:48.470 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/platform/product/category/tree/FXG/0) 无参数]
2025-08-01 15:09:48.904 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/platform/product/category/tree/FXG/0) 耗时(433 ms)]
