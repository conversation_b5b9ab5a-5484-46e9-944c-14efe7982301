server:
  port: 8080
  forward-headers-strategy: native
  error:
    include-exception: true
    include-message: always
  max-http-request-header-size: 1048576
spring:
  profiles:
    active: local
  application:
    name: ecommate-infra-server
  main:
    allow-bean-definition-overriding: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  cache:
    type: redis
    redis:
      key-prefix: "ecommate:"
  cloud:
    nacos:
      server-addr: ${nacos.server-addr}
      discovery:
        server-addr: ${spring.cloud.nacos.server-addr}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}
        heart-beat-interval: 1000
        heart-beat-timeout: 3000
        ip-delete-timeout: 3000
        metadata:
          preserved.heart.beat.interval: 1000
          preserved.heart.beat.timeout: 3000
          preserved.ip.delete.timeout: 3000
      config:
        server-addr: ${spring.cloud.nacos.server-addr}
        namespace: ${nacos.namespace} # 命名空间。这里使用 dev 开发环境
        username: ${nacos.username}
        password: ${nacos.password}
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        file-extension: yaml
        extension-configs:
          - data-id: ${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: DEFAULT_GROUP
            refresh: true
feign:
  client:
    config:
      default:
        readTimeout: 120000
  httpclient:
    enabled: true
    disable-ssl-validation: true
    max-connections: 1000
    max-connections-per-route: 100
    connection-timeout: 5000

mybatis-plus:
  global-config:
    db-config:
      #logic-delete-field 必须为实体字段名
      logic-delete-field: deleteFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 全局关闭一级缓存，后续改为人工针对查询配置
    local-cache-scope: STATEMENT

pagehelper:
  helper-dialect: mysql
  reasonable: false
  support-methods-arguments: false
  params: count=countSql

yaotown:
  web:
    platform-api:
      prefix: "/platform"
      controller: "**.controller.platform.**"
    enterprise-api:
      prefix: "/enterprise"
      controller: "**.controller.enterprise.**"
    open-api:
      prefix: "/open-api"
      controller: "**.controller.open.**"
    supplier-api:
      prefix: "/supplier"
      controller: "**.controller.supplier.**"
  file:
    download-uri: /v1/%s/infra/download
  security:
    permit-all-urls:
      - /actuator/prometheus
      - /enterprise/infra/account/exists
      - /enterprise/infra/account/register
      - /enterprise/infra/account/login
      - /enterprise/infra/account/loginByMobile
      - /enterprise/infra/account/forgetThePassword
      - /enterprise/infra/captcha/generate
      - /enterprise/infra/invitation/generateCode
      - /enterprise/infra/sms/send
      - /enterprise/infra/sms/code_verify
      - /enterprise/infra/banner/list
      - /enterprise/infra/advertising/list-active
      - /enterprise/product/supply-enterprise/product
      - /enterprise/product/supply-enterprise/detail/**
      - /enterprise/product/supply-product/detail/**
      - /platform/infra/account/login
      - /platform/infra/captcha/get
      - /platform/infra/captcha/check
      - /supplier/infra/supplier/register
      - /supplier/infra/supplier/login
      - /supplier/infra/supplier/send
      - /supplier/infra/supplier/code_verify

aj:
  captcha:
    #    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    #    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    #    cache-type: redis # 缓存 local/redis...
    #    cache-number: 1000 # local 缓存的阈值,达到这个值，清除缓存
    #    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    type: blockPuzzle # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: 源汇通 # 右下角水印文字(我的水印)
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败 5 次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制
