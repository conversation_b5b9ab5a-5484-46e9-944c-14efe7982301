<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaotown.ecommate.trade.infrastructure.mapper.ErpOrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.yaotown.ecommate.trade.infrastructure.po.ErpOrderInfoPO">
    <!--@mbg.generated-->
    <!--@Table ods_xyt_order_info_realtime-->
    <id column="order_id" jdbcType="BIGINT" property="orderId" />
    <id column="current_tenant_id" jdbcType="BIGINT" property="currentTenantId" />
    <result column="ext_order_id" jdbcType="VARCHAR" property="extOrderId" />
    <result column="wms_order_id" jdbcType="VARCHAR" property="wmsOrderId" />
    <result column="order_kind" jdbcType="SMALLINT" property="orderKind" />
    <result column="ext_app_id" jdbcType="VARCHAR" property="extAppId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="wms_create_time" jdbcType="TIMESTAMP" property="wmsCreateTime" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="total_fee" jdbcType="BIGINT" property="totalFee" />
    <result column="discount_fee" jdbcType="BIGINT" property="discountFee" />
    <result column="post_fee" jdbcType="BIGINT" property="postFee" />
    <result column="paid_fee" jdbcType="BIGINT" property="paidFee" />
    <result column="paid_points" jdbcType="INTEGER" property="paidPoints" />
    <result column="payment_trade_id" jdbcType="VARCHAR" property="paymentTradeId" />
    <result column="adjust_fee" jdbcType="BIGINT" property="adjustFee" />
    <result column="order_status" jdbcType="SMALLINT" property="orderStatus" />
    <result column="warranty_status" jdbcType="SMALLINT" property="warrantyStatus" />
    <result column="revoke_status" jdbcType="SMALLINT" property="revokeStatus" />
    <result column="order_status_reason" jdbcType="SMALLINT" property="orderStatusReason" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="referee_id" jdbcType="VARCHAR" property="refereeId" />
    <result column="referee_nick" jdbcType="VARCHAR" property="refereeNick" />
    <result column="shipping_type" jdbcType="VARCHAR" property="platformOrderShippingTypeEnum" />
    <result column="sku_num" jdbcType="INTEGER" property="skuNum" />
    <result column="sku_brief" jdbcType="VARCHAR" property="skuBrief" />
    <result column="wms_id" jdbcType="BIGINT" property="wmsId" />
    <result column="create_op" jdbcType="BIGINT" property="createOp" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
    <result column="done_time" jdbcType="TIMESTAMP" property="doneTime" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="state" jdbcType="SMALLINT" property="state" />
    <result column="done_version" jdbcType="BIGINT" property="doneVersion" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="supplier_tenant_id" jdbcType="BIGINT" property="supplierTenantId" />
    <result column="reserve_stock_id" jdbcType="BIGINT" property="reserveStockId" />
    <result column="brand_tenant_id" jdbcType="BIGINT" property="brandTenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    order_id, current_tenant_id, ext_order_id, wms_order_id, order_kind, ext_app_id, 
    create_time, update_time, pay_time, wms_create_time, pay_type, total_fee, discount_fee, 
    post_fee, paid_fee, paid_points, payment_trade_id, adjust_fee, order_status, warranty_status, 
    revoke_status, order_status_reason, platform_id, shop_id, buyer_nick, buyer_id, referee_id, 
    referee_nick, shipping_type, sku_num, sku_brief, wms_id, create_op, sync_time, done_time, 
    delivery_time, `state`, done_version, tenant_id, supplier_tenant_id, reserve_stock_id, 
    brand_tenant_id
  </sql>
</mapper>