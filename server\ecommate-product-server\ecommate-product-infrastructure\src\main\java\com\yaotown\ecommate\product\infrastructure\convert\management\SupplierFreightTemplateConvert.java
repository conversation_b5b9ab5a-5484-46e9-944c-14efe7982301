package com.yaotown.ecommate.product.infrastructure.convert.management;

import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierFreightTemplateEntity;
import com.yaotown.ecommate.product.infrastructure.po.management.SupplierFreightTemplatePO;
import com.yaotown.ecommate.product.types.util.MapStructConvertUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 供应商运费模板转换器
 */
@Mapper(uses = {MapStructConvertUtil.class})
public interface SupplierFreightTemplateConvert {

    SupplierFreightTemplateConvert INSTANCE = Mappers.getMapper(SupplierFreightTemplateConvert.class);

    /**
     * Entity转PO
     */
    SupplierFreightTemplatePO convertToPO(SupplierFreightTemplateEntity entity);

    /**
     * PO转Entity
     */
    SupplierFreightTemplateEntity convertToEntity(SupplierFreightTemplatePO po);


    List<SupplierFreightTemplateEntity> convertToEntityList(List<SupplierFreightTemplatePO> po);
} 