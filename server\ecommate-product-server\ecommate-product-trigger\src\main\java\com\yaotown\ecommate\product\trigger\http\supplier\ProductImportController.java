package com.yaotown.ecommate.product.trigger.http.supplier;

import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.common.base.core.entity.R;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.security.core.util.SecurityUtils;
import com.yaotown.ecommate.product.trigger.biz.management.model.request.SupplierProductPageQueryReqDTO;
import com.yaotown.ecommate.product.trigger.biz.management.model.response.SupplierProductImportRespDTO;
import com.yaotown.ecommate.product.trigger.biz.management.service.SupplierProductImportBizService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @apiFolder 供应商端/产品中心服务/商品导入
 * @classPrefixPath /v1/supplier/
 */
@RestController
@RequestMapping("/product/import")
@Slf4j
@AllArgsConstructor
@Validated
public class ProductImportController {

    // Excel文件最大大小，10MB
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    private final SupplierProductImportBizService supplierProductImportBizService;

    /**
     * Excel商品导入
     *
     * @param file 导入的Excel文件
     * @return 导入结果
     */
    @PostMapping(value = "/excel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SupplierProductImportRespDTO> importProductExcel(@RequestParam("file") MultipartFile file) {
        // 校验文件是否为空
        if (file == null || file.isEmpty()) {
            return R.error("导入文件不能为空");
        }

        // 校验文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return R.error("导入文件大小不能超过10MB");
        }

        // 校验文件类型
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename) ||
                (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return R.error("文件格式不正确，请上传Excel文件");
        }

        try {
            // 调用业务服务处理导入逻辑，传入当前登录企业ID
            Long enterpriseId = SecurityUtils.getLoginEnterpriseId();
            SupplierProductImportRespDTO result = supplierProductImportBizService.importProducts(file, enterpriseId);

            // 记录导入结果
            log.info("Excel商品导入成功，总数：{}，成功：{}，失败：{}",
                    result.getTotalCount(), result.getSuccessCount(), result.getErrorRecords());

            return R.success(result);
        } catch (BusinessException e) {
            // 业务参数验证异常
            log.warn("导入Excel参数验证失败: {}", e.getMessage());
            return R.error(e.getMessage());
        } catch (IOException e) {
            // IO异常
            log.error("导入Excel文件失败", e);
            return R.error("导入文件处理失败: " + e.getMessage());
        } catch (Exception e) {
            // 其他未知异常
            log.error("导入Excel处理异常", e);
            return R.error("导入处理失败，请稍后再试");
        }
    }
    
    /**
     * 导出商品数据Excel（支持查询条件）
     * 
     * @param reqDTO 查询条件，与分页查询接口相同
     */
    @PostMapping("/export/excel")
    public void exportProductExcel(@RequestBody @Validated QueryModel<SupplierProductPageQueryReqDTO> reqDTO,HttpServletResponse response) {
        try {
            // 获取当前登录企业ID
            Long enterpriseId = SecurityUtils.getLoginEnterpriseId();

            // 设置响应头信息
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("商品信息导出", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            
            // 调用业务服务直接输出Excel到响应流，传入查询条件
            log.info("开始导出商品数据，企业ID：{}，查询条件：{}", enterpriseId, reqDTO);
            supplierProductImportBizService.exportProductSkusToExcel(response, enterpriseId, reqDTO);
            log.info("商品数据导出完成，企业ID：{}", enterpriseId);
        } catch (Exception e) {
            log.error("导出商品数据异常", e);
            // 异常情况下不需要处理响应，因为响应流可能已经关闭或部分写入
        }
    }
} 