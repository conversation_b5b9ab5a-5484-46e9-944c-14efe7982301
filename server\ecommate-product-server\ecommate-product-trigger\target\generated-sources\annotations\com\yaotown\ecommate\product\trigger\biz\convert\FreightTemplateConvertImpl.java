package com.yaotown.ecommate.product.trigger.biz.convert;

import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierFreightTemplateEntity;
import com.yaotown.ecommate.product.trigger.biz.management.model.request.FreightTemplateReqDTO;
import com.yaotown.ecommate.product.trigger.biz.management.model.response.FreightTemplateRespDTO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T14:34:21+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Alibaba)"
)
public class FreightTemplateConvertImpl implements FreightTemplateConvert {

    @Override
    public SupplierFreightTemplateEntity convertToEntity(FreightTemplateReqDTO dto) {
        if ( dto == null ) {
            return null;
        }

        SupplierFreightTemplateEntity supplierFreightTemplateEntity = new SupplierFreightTemplateEntity();

        supplierFreightTemplateEntity.setId( dto.getId() );
        supplierFreightTemplateEntity.setTemplateName( dto.getTemplateName() );
        supplierFreightTemplateEntity.setRegionName( dto.getRegionName() );
        supplierFreightTemplateEntity.setRegionCodes( dto.getRegionCodes() );
        supplierFreightTemplateEntity.setFirstItem( dto.getFirstItem() );
        supplierFreightTemplateEntity.setFirstItemFee( dto.getFirstItemFee() );
        supplierFreightTemplateEntity.setAdditionalItem( dto.getAdditionalItem() );
        supplierFreightTemplateEntity.setAdditionalItemFee( dto.getAdditionalItemFee() );
        supplierFreightTemplateEntity.setStatus( dto.getStatus() );

        return supplierFreightTemplateEntity;
    }

    @Override
    public FreightTemplateRespDTO convertToRespDTO(SupplierFreightTemplateEntity entity) {
        if ( entity == null ) {
            return null;
        }

        FreightTemplateRespDTO freightTemplateRespDTO = new FreightTemplateRespDTO();

        freightTemplateRespDTO.setId( entity.getId() );
        freightTemplateRespDTO.setTemplateName( entity.getTemplateName() );
        freightTemplateRespDTO.setRegionName( entity.getRegionName() );
        freightTemplateRespDTO.setRegionCodes( entity.getRegionCodes() );
        freightTemplateRespDTO.setFirstItem( entity.getFirstItem() );
        freightTemplateRespDTO.setFirstItemFee( entity.getFirstItemFee() );
        freightTemplateRespDTO.setAdditionalItem( entity.getAdditionalItem() );
        freightTemplateRespDTO.setAdditionalItemFee( entity.getAdditionalItemFee() );
        freightTemplateRespDTO.setStatus( entity.getStatus() );
        freightTemplateRespDTO.setCreated( entity.getCreated() );
        freightTemplateRespDTO.setUpdated( entity.getUpdated() );

        return freightTemplateRespDTO;
    }
}
