package com.yaotown.ecommate.product.domain.enterprise.service.impI;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.common.core.entity.CurrentAccount;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.core.util.json.JsonUtils;
import com.yaotown.ecommate.common.security.core.util.SecurityUtils;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopPlatformTypeEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.valobj.ShopInfoSearchVO;
import com.yaotown.ecommate.product.domain.enterprise.repository.IShopInfoRepository;
import com.yaotown.ecommate.product.domain.enterprise.service.IShopInfoService;
import com.yaotown.ecommate.product.domain.product.ecomlink.adapter.port.IECommerceLinkErpLinkPort;
import com.yaotown.ecommate.product.domain.product.erplink.model.valobj.ErpResponseVO;
import com.yaotown.ecommate.product.domain.product.erplink.model.valobj.ErpRestResult;
import com.yaotown.ecommate.product.domain.product.erplink.service.IErpApiClientService;
import com.yaotown.ecommate.product.domain.product.listing.repository.IProductListingRepository;
import com.yaotown.ecommate.product.domain.product.listing.repository.IProductRepository;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.valobj.SupplierShopERPVO;
import com.yaotown.ecommate.product.types.enums.erp.ErpPlatformTypeEnum;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class ShopInfoServiceImpI implements IShopInfoService {

    private final IShopInfoRepository shopInfoRepository;
    private final IProductListingRepository productListingRepository;
    private final IProductRepository productRepository;
    
    @Resource
    private IECommerceLinkErpLinkPort ecommerceLinkErpLinkPort;
    
    @Resource
    private IErpApiClientService erpApiClientService;

    @Override
    public List<ShopInfoEntity> selectShopInfoList(Long accountId, List<Long> shopIds) {
        return shopInfoRepository.selectShopInfoList(accountId, shopIds);
    }

    @Override
    public ShopInfoEntity findShopInfo(Long shopId) {
        ShopInfoEntity shopInfo = shopInfoRepository.findShopInfo(shopId);
        return shopInfo;
    }

    /**
     * @param shopId
     */
    @Override
    public void updateUnbind(Long shopId) {

    }

    @Override
    public void shopRefresh(Long shopId) {

    }

    @Override
    public List<ShopInfoEntity> selectByPlatformType(String platformType, Long accountId) {
        return shopInfoRepository.selectByPlatformType(platformType, accountId);
    }

    @Override
    public List<ShopPlatformTypeEntity> selectShopType(Long accountId) {
        List<ShopPlatformTypeEntity> typeEntities = shopInfoRepository.selectShopType(accountId);
        List<ShopPlatformTypeEntity> type = new ArrayList<>();
        for (ShopPlatformTypeEntity shopPlatformType : typeEntities) {
            ErpPlatformTypeEnum anEnum = ErpPlatformTypeEnum.toEnum(shopPlatformType.getPlatformValue());
            ShopPlatformTypeEntity typeEntity = new ShopPlatformTypeEntity();
            typeEntity.setPlatformName(anEnum.getTitle());
            typeEntity.setPlatformValue(anEnum.getValue());
            typeEntity.setCount(shopPlatformType.getCount());
            type.add(typeEntity);
        }
        return type;
    }

    @Override
    public PageData<ShopInfoEntity> selectShopInfoLists(QueryModel<Void> queryModel, String platformType, Long accountId) {
        return shopInfoRepository.selectShopInfoLists(queryModel, platformType, accountId);
    }

    @Override
    public PageData<ShopInfoEntity> selectShopInfoList(QueryModel<ShopInfoSearchVO> queryModel) {
        return shopInfoRepository.selectShopInfoList(queryModel);
    }
    
    /**
     * 更新店铺信息
     *
     * @param shopInfo 店铺信息实体
     * @return 更新是否成功
     */
    @Override
    public boolean updateShopInfo(ShopInfoEntity shopInfo) {
        if (shopInfo == null || shopInfo.getShopId() == null) {
            log.error("更新店铺信息失败，参数异常: {}", shopInfo);
            return false;
        }
        
        try {
            // 获取现有店铺信息
            ShopInfoEntity existingShop = shopInfoRepository.findShopInfo(shopInfo.getShopId());
            if (existingShop == null) {
                log.error("更新店铺信息失败，店铺不存在: {}", shopInfo.getShopId());
                return false;
            }
            
            // 调用新的更新方法，不再使用保存方法
            return shopInfoRepository.updateShopInfo(shopInfo);
        } catch (Exception e) {
            log.error("更新店铺信息异常", e);
            return false;
        }
    }

    @Override
    public String shopAuthorization(String platform, String redirectSuccessUrl, String redirectFailureUrl) {
        // 获得用户信息
        CurrentAccount account = SecurityUtils.getLoginAccount();
        //调用ERP店铺授权
        //组装body
        SupplierShopERPVO supplierShopERPVO = new SupplierShopERPVO();
        supplierShopERPVO.setRedirectSuccessUrl(redirectSuccessUrl);
        supplierShopERPVO.setRedirectFailureUrl(redirectFailureUrl);
        String body = JsonUtils.toJsonString(supplierShopERPVO);
        ErpResponseVO shopAuthorization = ecommerceLinkErpLinkPort.getShopAuthorization(account.getAccountId(), platform, body);
        if (shopAuthorization.getStatusCode() == 200){
            // 从响应中提取data字段的URL链接并返回
            ErpRestResult result = shopAuthorization.getResult();
            if (result != null && result.getData() != null) {
                return result.getData().toString();
            } else {
                throw new BusinessException("获取授权链接失败：返回数据为空");
            }
        } else {
            ErpRestResult result = shopAuthorization.getResult();
            String errorMsg = result != null && result.getMessage() != null ?
                            result.getMessage() : "未知错误";
            throw new BusinessException("获取授权链接失败：" + errorMsg);
        }
    }

    @Override
    public Long createShop(ShopInfoEntity shopInfo) {
        // 获取当前登录账户信息
        CurrentAccount account = SecurityUtils.getLoginAccount();
        
        // 设置企业ID和账户ID
        if (shopInfo.getEnterpriseId() == null) {
            shopInfo.setEnterpriseId(account.getEnterpriseId());
        }
        if (shopInfo.getAccountId() == null) {
            shopInfo.setAccountId(account.getAccountId());
        }
        
        // 保存店铺信息并返回店铺ID
        return shopInfoRepository.saveShopInfo(shopInfo);
    }
}
