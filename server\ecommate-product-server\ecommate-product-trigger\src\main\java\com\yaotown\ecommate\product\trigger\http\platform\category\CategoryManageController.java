package com.yaotown.ecommate.product.trigger.http.platform.category;

import com.yaotown.common.base.core.entity.R;
import com.yaotown.common.base.core.util.CommonStreamUtil;
import com.yaotown.ecommate.product.domain.product.listing.adapter.port.IListingComputeProductPort;
import com.yaotown.ecommate.product.domain.product.listing.model.valobj.CategoryInfoVO;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.entity.CategoryEntity;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.valobj.CategoryMappingVO;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.valobj.SupplyCategoryInfoVO;
import com.yaotown.ecommate.product.domain.product.supplycenter.model.valobj.SupplyCategoryTreeInfoVO;
import com.yaotown.ecommate.product.domain.product.supplycenter.repository.ISupplyCategoryInfoRepository;
import com.yaotown.ecommate.product.trigger.biz.convert.CategoryConvert;
import com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request.category.CategoryDelectDTO;
import com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request.category.CategorySaveDTO;
import com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request.category.CategoryUpdateDTO;
import com.yaotown.ecommate.product.types.enums.erp.ErpPlatformTypeEnum;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @apiFolder 平台端/产品中心服务/类目维护管理
 * @classPrefixPath /v1/platform/
 */
@RestController
@AllArgsConstructor
@RequestMapping("/product/category")
@Slf4j
public class CategoryManageController {
    private final ISupplyCategoryInfoRepository supplyCategoryInfoRepository;
    private final IListingComputeProductPort listingComputeProductPort;

    /**
     * 获取类目树信息
     *
     * @param parentId   父类ID
     * @param platform   平台 {@link ErpPlatformTypeEnum}
     * @param showStatus 显示状态(0:不显示 1:显示)
     */
    @GetMapping("/tree/{platform}/{parentId}")
    public R<List<SupplyCategoryTreeInfoVO>> getCategory(@PathVariable("parentId") String parentId, @PathVariable("platform") String platform,
                                                         @RequestParam(required = false) Integer showStatus) {
        return R.success(supplyCategoryInfoRepository.queryByParentIdAndPlatformWithCache(parentId, platform, showStatus));
    }

    /**
     * 获取顶级类目列表信息
     *
     * @param platform   平台 {@link ErpPlatformTypeEnum}
     * @param showStatus 显示状态(0:不显示 1:显示)
     */
    @GetMapping("/top/list/{platform}")
    public R<List<SupplyCategoryInfoVO>> getCategoryTopList(@PathVariable("platform") String platform,
                                                            @RequestParam(required = false) Integer showStatus) {
        return R.success(supplyCategoryInfoRepository.queryTopListByPlatformWithCache(platform, showStatus));
    }

    /**
     * 获取类目详情
     *
     * @param categoryDelectDTO 类目ID,平台
     */
    @PostMapping("details")
    public R<SupplyCategoryInfoVO> getCategoryDetail(@RequestBody @Valid CategoryDelectDTO categoryDelectDTO) {
        return R.success(supplyCategoryInfoRepository.queryById(categoryDelectDTO.getCategoryId(), categoryDelectDTO.getPlatform()));
    }

    /**
     * 新增类目
     *
     * @param categoryDTO 类目信息
     */
    @PostMapping("/create")
    public R<SupplyCategoryInfoVO> addCategory(@Valid @RequestBody CategorySaveDTO categoryDTO) {
        CategoryEntity category = CategoryConvert.INSTANCE.toEntity(categoryDTO);
        return R.success(supplyCategoryInfoRepository.save(category));
    }

    /**
     * 修改类目
     *
     * @param categoryDTO 类目信息
     */
    @PostMapping("/update")
    public R<SupplyCategoryInfoVO> updateCategory(@Valid @RequestBody CategoryUpdateDTO categoryDTO) {
        CategoryEntity category = CategoryConvert.INSTANCE.toUpdateEntity(categoryDTO);
        return R.success(supplyCategoryInfoRepository.update(category));
    }

    /**
     * 删除类目
     *
     * @param categoryDelectDTO 类目ID、平台
     */
    @PostMapping("delete")
    public R<Boolean> deleteCategory(@RequestBody @Valid CategoryDelectDTO categoryDelectDTO) {
        return R.success(supplyCategoryInfoRepository.deleteById(categoryDelectDTO.getCategoryId(), categoryDelectDTO.getPlatform()));
    }

    /**
     * 获取类目映射
     *
     * @param platform               平台
     * @param platformCategoryLeafId 平台类目ID
     * @return
     */
    @GetMapping("/mapping/get/{platform}/{platformCategoryLeafId}")
    public R<CategoryMappingVO> getCategoryMapping(@PathVariable String platform, @PathVariable String platformCategoryLeafId) {
        return R.success(supplyCategoryInfoRepository.getCategoryMappingByPlatformId(platform, platformCategoryLeafId));
    }

    /**
     * 获取类目路径
     *
     * @param platform       平台
     * @param categoryLeafId 平台类目ID
     * @return
     */
    @GetMapping("/path/{platform}/{categoryLeafId}")
    public R<List<String>> getCategoryPath(@PathVariable String platform, @PathVariable String categoryLeafId) {
        List<CategoryInfoVO> categoryInfoVOS = listingComputeProductPort.getCategoryListByLeafId(categoryLeafId, platform);
        return R.success(CommonStreamUtil.transList(categoryInfoVOS, CategoryInfoVO::getId));
    }
}
