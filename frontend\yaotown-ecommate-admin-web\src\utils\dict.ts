/**
 * 数据字典工具类
 */
import { useDictStoreWithOut } from '@/store/modules/dict'
import { ElementPlusInfoType } from '@/types/elementPlus'

const dictStore = useDictStoreWithOut()

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export interface DictDataType {
  dictType: string
  label: string
  value: string | number | boolean
  colorType: ElementPlusInfoType | ''
  cssClass: string
}

export interface NumberDictDataType extends DictDataType {
  value: number
}

export interface StringDictDataType extends DictDataType {
  value: string
}

export const getDictOptions = (dictType: string) => {
  return dictStore.getDictByType(dictType) || []
}

export const getIntDictOptions = (dictType: string): NumberDictDataType[] => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 number 类型的 NumberDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getIntDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: NumberDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push(<NumberDictDataType>{
      ...dict,
      value: parseInt(dict.value + '')
    })
  })
  return dictOption
}

export const getStrDictOptions = (dictType: string) => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 string 类型的 StringDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getStrDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: StringDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + ''
    })
  })
  return dictOption
}

export const getBoolDictOptions = (dictType: string) => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + '' === 'true'
    })
  })
  return dictOption
}

export const getOptionsKeyValue = (dictType: string) => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  const obj = {}
  dictOptions.forEach((dict: DictDataType) => {
    obj[dict.value + ''] = dict.label
  })
  return obj
}

/**
 * 获取指定字典类型的指定值对应的字典对象
 * @param dictType 字典类型
 * @param value 字典值
 * @return DictDataType 字典对象
 */
export const getDictObj = (dictType: string, value: any): DictDataType | undefined => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  for (const dict of dictOptions) {
    if (dict.value === value + '') {
      return dict
    }
  }
}

/**
 * 获得字典数据的文本展示
 *
 * @param dictType 字典类型
 * @param value 字典数据的值
 * @return 字典名称
 */
export const getDictLabel = (dictType: string, value: any): string => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  const dictLabel = ref('')
  dictOptions.forEach((dict: DictDataType) => {
    if (dict.value === value + '') {
      dictLabel.value = dict.label
    }
  })
  return dictLabel.value
}

export enum DICT_TYPE {
  USER_TYPE = 'user_type', // 用户类型
  SYSTEM_CONFIG_TYPE = 'system_config_type', // 用户类型
  COMMON_STATUS = 'common_status', // 系统状态
  WHETHER_STATUS = 'whether_status', // 是否状态
  VISIBLE_STATUS = 'visible_status', // 显示状态
  SALE_STATUS = 'sale_status', // 上架状态
  PUBLISHED_STATUS = 'published_status', // 上架状态
  REPLY_STATUS = 'reply_status', // 回复状态
  TERMINAL = 'terminal', // 终端
  DATE_INTERVAL = 'date_interval', // 数据间隔
  SHOP_TYPE = 'shop_type', // 店铺类型
  AUTH_STATUS = 'auth_status', // 授权状态
  SHOP_STATE = 'shop_state', // 店铺状态
  PLATFORM_TYPE = 'platform_type', // 平台类型
  ACCOUNT_TYPE = 'account_type', // 账户类型
  ENTERPRISE_ACCOUNT_TYPE = 'enterprise_account_type', // 企业账户类型
  MESSAGE_TYPE = 'message_type', // 消息推送类型
  PUSH_LOCATION = 'push_location', // 消息推送类型
  PUSH_STATUS = 'push_status', // 推送状态
  SYSTEM_SEX_OPTION = 'system_sex_option', // 性别选项
  RECOMMENDED_LOCATION = 'recommended_location', // 性别选项
  ACTIVE_STATUS = 'active_sataus', // 活动状态

  // ========== PROMOTION 模块 ==========
  MESSAGE_JUMP_TYPE = 'message_jump_type', // 消息跳转类型
  JUMP_LINK_TYPE = 'jump_link_type', // 跳转链接类型

  // ========== SYSTEM 模块 ==========
  SYSTEM_USER_SEX = 'system_user_sex', // 用户性别
  MEMBER_LEVEL_OPTION = 'member_level_option', // 会员等级
  SYSTEM_MENU_TYPE = 'system_menu_type', // 菜单类型
  SYSTEM_ROLE_TYPE = 'system_role_type', // 角色类型
  SYSTEM_DATA_SCOPE = 'system_data_scope', // 数据权限类型
  SYSTEM_NOTICE_TYPE = 'system_notice_type', // 通知类型
  SYSTEM_LOGIN_TYPE = 'system_login_type', // 登陆日志的类型
  SYSTEM_LOGIN_RESULT = 'system_login_result', // 登陆结果
  SYSTEM_SMS_CHANNEL_CODE = 'system_sms_channel_code', // 短信渠道编码
  SYSTEM_SMS_TEMPLATE_TYPE = 'system_sms_template_type', // 短信模板的类型
  SYSTEM_SMS_SEND_STATUS = 'system_sms_send_status', // 短信发送状态
  SYSTEM_SMS_RECEIVE_STATUS = 'system_sms_receive_status', // 短信接收状态
  SYSTEM_OAUTH2_GRANT_TYPE = 'system_oauth2_grant_type', // OAuth 2.0 授权类型
  SYSTEM_MAIL_SEND_STATUS = 'system_mail_send_status', // 邮件发送状态
  SYSTEM_NOTIFY_TEMPLATE_TYPE = 'system_notify_template_type', // 站内信模版的类型
  SYSTEM_SOCIAL_TYPE = 'system_social_type', // 社交类型

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING = 'infra_boolean_string', // Boolean 是否类型
  INFRA_FILE_STORAGE = 'infra_file_storage', // 文件存储器


  // ========== PAY 模块 ==========
  PAY_CHANNEL_CODE = 'pay_channel_code', // 支付渠道编码类型
  PAY_ORDER_STATUS = 'pay_order_status', // 商户支付订单状态
  PAY_REFUND_STATUS = 'pay_refund_status', // 退款订单状态
  PAY_NOTIFY_STATUS = 'pay_notify_status', // 商户支付回调状态
  PAY_NOTIFY_TYPE = 'pay_notify_type', // 商户支付回调状态
  PAY_TRANSFER_STATUS = 'pay_transfer_status', // 转账订单状态

  // ========== MP 模块 ==========
  MP_AUTO_REPLY_REQUEST_MATCH = 'mp_auto_reply_request_match', // 自动回复请求匹配类型
  MP_MESSAGE_TYPE = 'mp_message_type', // 消息类型
}
