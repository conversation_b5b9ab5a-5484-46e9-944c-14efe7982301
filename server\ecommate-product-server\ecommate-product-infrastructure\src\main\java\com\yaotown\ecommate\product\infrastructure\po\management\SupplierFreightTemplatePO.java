package com.yaotown.ecommate.product.infrastructure.po.management;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 运费模板表
 */
@Data
@TableName(value = "yt_freight_template")
public class SupplierFreightTemplatePO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 所属企业id
     */
    @TableField(value = "enterprise_id")
    private Long enterpriseId;

    /**
     * 模板名称
     */
    @TableField(value = "template_name")
    private String templateName;

    /**
     * 地区名称
     */
    @TableField(value = "region_name")
    private String regionName;

    /**
     * 地区编码，多个用逗号分隔
     */
    @TableField(value = "region_codes")
    private String regionCodes;

    /**
     * 首件(个)
     */
    @TableField(value = "first_item")
    private Integer firstItem;

    /**
     * 首件运费(分)
     */
    @TableField(value = "first_item_fee")
    private Long firstItemFee;

    /**
     * 续件(个)
     */
    @TableField(value = "additional_item")
    private Integer additionalItem;

    /**
     * 续件运费(分)
     */
    @TableField(value = "additional_item_fee")
    private Long additionalItemFee;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 是否已删除(0:否, 1:是)
     */
    @TableField(value = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建人
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 创建人名字
     */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    private String creatorName;

    /**
     * 修改操作人
     */
    @TableField(value = "modifier_id", fill = FieldFill.INSERT_UPDATE)
    private Long modifierId;

    /**
     * 修改操作人名字
     */
    @TableField(value = "modifier_name", fill = FieldFill.INSERT_UPDATE)
    private String modifierName;

    /**
     * 创建时间
     */
    @TableField(value = "created", fill = FieldFill.INSERT)
    private Date created;

    /**
     * 更新时间
     */
    @TableField(value = "updated", fill = FieldFill.INSERT_UPDATE)
    private Date updated;
} 