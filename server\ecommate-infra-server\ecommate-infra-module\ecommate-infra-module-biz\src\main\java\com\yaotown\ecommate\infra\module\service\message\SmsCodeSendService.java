package com.yaotown.ecommate.infra.module.service.message;

import com.yaotown.ecommate.infra.module.api.message.dto.SmsCodeSendRequestDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SmsCodeCheckDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SmsCodeRequestDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SupplierSmsCodeRequestDTO;

public interface SmsCodeSendService {

    String sendSmsCode(SmsCodeSendRequestDTO reqDTO);

    String smsCodeSend(SmsCodeRequestDTO req);

    void codeVerify(SmsCodeCheckDTO smsCodeCheck, boolean deleteCode);

    String supplierSmsCodeSend(SupplierSmsCodeRequestDTO req);
}
