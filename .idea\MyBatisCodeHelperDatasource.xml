<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MyBatisCodeHelperDatasource">
    <option name="projectProfile">
      <ProjectProfile>
        <option name="controllerTemplateString" value="&#10;#* @vtlvariable name=&quot;tableName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;entityPackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;entityClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;servicePackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceInterfacePackage&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;serviceInterfaceClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;mapperPackageName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;mapperClassName&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;controllerPackage&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;tableRemark&quot; type=&quot;java.lang.String&quot; *#&#10;#* @vtlvariable name=&quot;myDate&quot; type=&quot;java.util.Date&quot; *#&#10;#* @vtlvariable name=&quot;simpleDateFormat&quot; type=&quot;java.text.SimpleDateFormat&quot; *#&#10;package $!{controllerPackage};&#10;import $!{entityPackageName}.$!{entityClassName};&#10;###set($realServiceName = $!{serviceClassName}+'Impl')&#10;import $!{servicePackageName}.$!{serviceClassName};&#10;import org.springframework.web.bind.annotation.*;&#10;&#10;#set($serviceFirstLower = $!{serviceClassName.substring(0,1).toLowerCase()}+$!{serviceClassName.substring(1,$!{serviceClassName.length()})})&#10;import org.springframework.beans.factory.annotation.Autowired;&#10;&#10;/**&#10;* $!{tableRemark}($!{tableName})表控制层&#10;*&#10;* <AUTHOR> class $!{entityClassName}Controller {&#10;/**&#10;* 服务对象&#10;*/&#10;    @Autowired&#10;    private $!{serviceClassName} $!{serviceFirstLower};&#10;&#10;    /**&#10;    * 通过主键查询单条数据&#10;    *&#10;    * @param id 主键&#10;    * @return 单条数据&#10;    */&#10;    @GetMapping(&quot;selectOne&quot;)&#10;    public $!{entityClassName} selectOne(Integer id) {&#10;    return $!{serviceFirstLower}.selectByPrimaryKey(id);&#10;    }&#10;&#10;}" />
        <option name="javaMapperPackage" value="com.yaotown.ecommate.trade.infrastructure.mapper" />
        <option name="javaMapperPath" value="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-infrastructure/src/main/java" />
        <option name="javaModelPackage" value="com.yaotown.ecommate.trade.infrastructure.po" />
        <option name="javaModelPath" value="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-infrastructure/src/main/java" />
        <option name="lastDatabaseCrudChooseModuleName" value="ecommate-product-infrastructure" />
        <option name="lombokDataAnnotation" value="true" />
        <option name="mapperAnnotaion" value="true" />
        <option name="moduleNameToPackageAndPathMap">
          <map>
            <entry key="ecommate-infra-module-api">
              <value>
                <UserPackageAndPathInfoByModule>
                  <option name="javaMapperPackage" value="com.yaotown.ecommate.product.infrastructure.mapper" />
                  <option name="javaMapperPath" value="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-infrastructure/src/main/java" />
                  <option name="javaModelPacakge" value="com.yaotown.ecommate.product.infrastructure.po" />
                  <option name="javaModelPath" value="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-infrastructure/src/main/java" />
                  <option name="javaServiceInterfacePath" value="$PROJECT_DIR$/server/ecommate-infra-server/ecommate-infra-module/ecommate-infra-module-api/src/main/java" />
                  <option name="javaServicePath" value="$PROJECT_DIR$/server/ecommate-infra-server/ecommate-infra-module/ecommate-infra-module-api/src/main/java" />
                  <option name="xmlPackage" value="mapper" />
                  <option name="xmlPath" value="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-infrastructure/src/main/resources" />
                </UserPackageAndPathInfoByModule>
              </value>
            </entry>
            <entry key="ecommate-product-infrastructure">
              <value>
                <UserPackageAndPathInfoByModule>
                  <option name="javaMapperPackage" value="com.yaotown.ecommate.trade.infrastructure.mapper" />
                  <option name="javaMapperPath" value="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-infrastructure/src/main/java" />
                  <option name="javaModelPacakge" value="com.yaotown.ecommate.trade.infrastructure.po" />
                  <option name="javaModelPath" value="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-infrastructure/src/main/java" />
                  <option name="javaServiceInterfacePackage" value="com.yaotown.ecommate.product.domain.product.listing.service" />
                  <option name="javaServiceInterfacePath" value="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-infrastructure/src/main/java" />
                  <option name="javaServicePackage" value="com.yaotown.ecommate.product.domain.product.listing.service" />
                  <option name="javaServicePath" value="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-infrastructure/src/main/java" />
                  <option name="xmlPackage" value="mapper" />
                  <option name="xmlPath" value="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-infrastructure/src/main/resources" />
                </UserPackageAndPathInfoByModule>
              </value>
            </entry>
          </map>
        </option>
        <option name="mybatisPlusIdType" value="ASSIGN_ID" />
        <option name="removeTablePreName" value="yt_" />
        <option name="tableGenerateConfigs">
          <map>
            <entry key="yht_db_center:ods_xyt_order_info_realtime0">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="ErpOrderInfoPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_category_mapping">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="CategoryMapping" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_event_task">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="EventTask" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_order_payment">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="OrderPayment" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_original_platform_order">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="OriginalPlatformOrder" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_payment">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="Payment" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_platform_order">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="PlatformOrderPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_platform_order_consignee">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="PlatformOrderConsignee" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_platform_order_item">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="PlatformOrderItem" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_platform_order_memo">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="PlatformOrderMemo" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_product_listing_platform_sku_relation">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="ProductListingPlatformSkuRelationPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_purchase_order">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="PurchaseOrder" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_purchase_order_delivery_remind">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="PurchaseOrderDeliveryRemindPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_purchase_order_item">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="PurchaseOrderItem" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_refund">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="Refund" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_supplier_distributor_relation">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="SupplierDistributorRelation" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_supplier_platform_product">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="SupplierPlatformProduct" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_supplier_product">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="SupplierProduct" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_supplier_product_category_attribute">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="SupplierProductCategoryAttribute" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_supplier_product_extra">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="SupplierProductExtra" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_supplier_product_image">
              <value>
                <TableGenerateConfig>
                  <option name="generatedKey" value="" />
                  <option name="javaModelName" value="SupplierProductImage" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="sequenceColumn" value="" />
                  <option name="sequenceId" value="" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_ecommerce_mate:yt_supplier_product_sku">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="SupplierProductSku" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_enterprise:yt_product">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="ProductPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_enterprise:yt_product_extra">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="ProductExtraPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_enterprise:yt_product_image">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="ProductImagePO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_enterprise:yt_product_listing">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="ProductListingPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_enterprise:yt_product_listing_platform_relation">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="ProductListingPlatformRelationPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_enterprise:yt_product_listing_shop">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="ProductListingShopPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_enterprise:yt_product_listing_sku">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="ProductListingSkuPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_enterprise:yt_product_sku">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="ProductSkuPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
            <entry key="yt_enterprise:yt_product_supply_relation">
              <value>
                <TableGenerateConfig>
                  <option name="javaModelName" value="ProductSupplyRelationPO" />
                  <option name="moduleName" value="ecommate-product-infrastructure" />
                  <option name="mybatisplusIdType" value="ASSIGN_ID" />
                  <option name="useActualColumnName" value="false" />
                </TableGenerateConfig>
              </value>
            </entry>
          </map>
        </option>
        <option name="useJakataAnnotation" value="true" />
        <option name="userMybatisPlus" value="true" />
        <option name="xmlMapperPackage" value="mapper" />
        <option name="xmlMapperPath" value="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-infrastructure/src/main/resources" />
      </ProjectProfile>
    </option>
  </component>
</project>