2025-07-31 15:38:28.163 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848
2025-07-31 15:38:28.163 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000
2025-07-31 15:38:28.165 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0
2025-07-31 15:38:28.170 [Thread-2] INFO  [ecommate-trade-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-07-31 15:38:28.210 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels.....
2025-07-31 15:38:28.210 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-07-31 15:38:28.210 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null
2025-07-31 15:38:28.212 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect properties labels: {}
2025-07-31 15:38:28.212 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null
2025-07-31 15:38:28.212 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect jvm labels: {}
2025-07-31 15:38:28.212 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect env raw labels: null
2025-07-31 15:38:28.212 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect env labels: {}
2025-07-31 15:38:28.213 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-07-31 15:38:28.229 [main] INFO  [ecommate-trade-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-31 15:38:28.229 [main] INFO  [ecommate-trade-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-31 15:38:28.303 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2025-07-31 15:38:28.458 [main] INFO  [ecommate-trade-server] c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-07-31 15:38:28.465 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 3baa3583-73b0-4d65-a27c-2a53749767ef_config-0
2025-07-31 15:38:28.503 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$696/0x000001d7b7429230
2025-07-31 15:38:28.503 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$697/0x000001d7b7429650
2025-07-31 15:38:28.505 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-31 15:38:28.506 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-31 15:38:28.514 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 9848}
2025-07-31 15:38:28.550 [main] INFO  [ecommate-trade-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:10848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-31 15:38:32.317 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-31 15:38:32.317 [main] INFO  [ecommate-trade-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-31 15:38:32.447 [main] INFO  [ecommate-trade-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-31 15:38:32.450 [main] INFO  [ecommate-trade-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-31 15:38:32.451 [main] INFO  [ecommate-trade-server] c.a.n.common.ability.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-31 15:38:32.451 [main] INFO  [ecommate-trade-server] c.a.n.c.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-31 15:38:32.472 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1753947503308_192.168.48.1_62532
2025-07-31 15:38:32.473 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Notify connected event to listeners.
2025-07-31 15:38:32.473 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.ClientWorker - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Connected,notify listen context...
2025-07-31 15:38:32.473 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-31 15:38:32.473 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [3baa3583-73b0-4d65-a27c-2a53749767ef_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$717/0x000001d7b7569b20
2025-07-31 15:38:32.574 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.utils.JvmUtil - isMultiInstance:false
2025-07-31 15:38:32.774 [main] INFO  [ecommate-trade-server] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-ecommate-trade-server-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-trade-server.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-trade-server,DEFAULT_GROUP'}]
2025-07-31 15:38:32.812 [main] INFO  [ecommate-trade-server] com.yaotown.ecommate.trade.TradeApplication - The following 1 profile is active: "local"
2025-07-31 15:38:34.805 [main] INFO  [ecommate-trade-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 15:38:34.810 [main] INFO  [ecommate-trade-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 15:38:34.876 [main] INFO  [ecommate-trade-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 44 ms. Found 0 Redis repository interfaces.
2025-07-31 15:38:36.029 [main] INFO  [ecommate-trade-server] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=dc182277-21a9-3c46-9b88-975e06506650
2025-07-31 15:38:37.215 [main] INFO  [ecommate-trade-server] com.yomahub.liteflow.util.LOGOPrinter - 
================================================================================================
		 _     ___ _____ _____      _____ _     _____        __
		| |   |_ _|_   _| ____|    |  ___| |   / _ \ \      / /
		| |    | |  | | |  _| _____| |_  | |  | | | \ \ /\ / / 
		| |___ | |  | | | |__|_____|  _| | |__| |_| |\ V  V /  
		|_____|___| |_| |_____|    |_|   |_____\___/  \_/\_/   

		Version: 2.13.2
		Make your code amazing.
		website：https://liteflow.cc
================================================================================================

2025-07-31 15:38:37.979 [main] INFO  [ecommate-trade-server] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 15:38:37.979 [main] INFO  [ecommate-trade-server] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5154 ms
2025-07-31 15:38:38.515 [main] INFO  [ecommate-trade-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:38:40.450 [main] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1,ecommerce_mate} inited
2025-07-31 15:38:40.827 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 15:38:41.235 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:config user=SA
2025-07-31 15:38:41.238 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 15:38:41.295 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-31 15:38:41.407 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@*************-07-31 15:38:41.407 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-31 15:38:42.387 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-07-31 15:38:42.478 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.pool.HikariPool - HikariPool-3 - Added connection com.mysql.cj.jdbc.ConnectionImpl@59f41bcf
2025-07-31 15:38:42.479 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-07-31 15:38:45.278 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 15:38:45.278 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: db_center ::: select 1
2025-07-31 15:38:45.606 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 15:38:45.606 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: db_center ::: select 1
2025-07-31 15:38:45.667 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 15:38:45.667 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: order_link ::: select 1
2025-07-31 15:38:45.694 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 15:38:45.694 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: order_link ::: select 1
2025-07-31 15:38:45.724 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 15:38:45.724 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: order_link ::: select 1
2025-07-31 15:38:45.790 [main] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2,db_center} inited
2025-07-31 15:38:45.791 [main] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [db_center] success
2025-07-31 15:38:45.791 [main] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [ecommerce_mate] success
2025-07-31 15:38:45.792 [main] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [ecommerce_mate]
2025-07-31 15:38:46.715 [main] INFO  [ecommate-trade-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:38:46.903 [main] INFO  [ecommate-trade-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-product-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 15:38:47.060 [main] INFO  [ecommate-trade-server] org.redisson.Version - Redisson 3.32.0
2025-07-31 15:38:47.565 [redisson-netty-1-4] INFO  [ecommate-trade-server] org.redisson.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6379
2025-07-31 15:38:47.815 [redisson-netty-1-19] INFO  [ecommate-trade-server] org.redisson.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6379
2025-07-31 15:38:50.138 [main] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-31 15:38:50.142 [main] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-07-31 15:38:50.146 [main] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-07-31 15:38:50.146 [main] INFO  [ecommate-trade-server] c.b.d.d.destroyer.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [db_center] success,
2025-07-31 15:38:50.146 [main] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-31 15:38:50.154 [main] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-31 15:38:50.154 [main] INFO  [ecommate-trade-server] c.b.d.d.destroyer.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [ecommerce_mate] success,
2025-07-31 15:38:50.154 [main] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-31 15:38:50.171 [main] INFO  [ecommate-trade-server] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-31 17:28:01.964 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848
2025-07-31 17:28:01.965 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000
2025-07-31 17:28:01.967 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0
2025-07-31 17:28:01.973 [Thread-2] INFO  [ecommate-trade-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-07-31 17:28:02.011 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels.....
2025-07-31 17:28:02.011 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-07-31 17:28:02.012 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null
2025-07-31 17:28:02.013 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect properties labels: {}
2025-07-31 17:28:02.013 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null
2025-07-31 17:28:02.013 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect jvm labels: {}
2025-07-31 17:28:02.013 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect env raw labels: null
2025-07-31 17:28:02.014 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - default nacos collect env labels: {}
2025-07-31 17:28:02.015 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-07-31 17:28:02.032 [main] INFO  [ecommate-trade-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-31 17:28:02.032 [main] INFO  [ecommate-trade-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-31 17:28:02.120 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2025-07-31 17:28:02.289 [main] INFO  [ecommate-trade-server] c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-07-31 17:28:02.296 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 7d97182e-7ec7-484e-9298-31d42880fff5_config-0
2025-07-31 17:28:02.339 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$696/0x00000143014294d0
2025-07-31 17:28:02.340 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$697/0x00000143014298f0
2025-07-31 17:28:02.340 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-31 17:28:02.341 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-31 17:28:02.352 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-31 17:28:02.388 [main] INFO  [ecommate-trade-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-31 17:28:03.701 [main] INFO  [ecommate-trade-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-31 17:28:03.704 [main] INFO  [ecommate-trade-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-31 17:28:03.705 [main] INFO  [ecommate-trade-server] c.a.n.common.ability.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-31 17:28:03.705 [main] INFO  [ecommate-trade-server] c.a.n.c.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-31 17:28:03.734 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1753954074536_192.168.48.1_64468
2025-07-31 17:28:03.735 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] Notify connected event to listeners.
2025-07-31 17:28:03.735 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.ClientWorker - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] Connected,notify listen context...
2025-07-31 17:28:03.736 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-31 17:28:03.736 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [7d97182e-7ec7-484e-9298-31d42880fff5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$717/0x0000014301562fd8
2025-07-31 17:28:03.832 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.utils.JvmUtil - isMultiInstance:false
2025-07-31 17:28:04.029 [main] INFO  [ecommate-trade-server] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-ecommate-trade-server-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-trade-server.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-trade-server,DEFAULT_GROUP'}]
2025-07-31 17:28:04.058 [main] INFO  [ecommate-trade-server] com.yaotown.ecommate.trade.TradeApplication - The following 1 profile is active: "local"
2025-07-31 17:28:06.256 [main] INFO  [ecommate-trade-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 17:28:06.261 [main] INFO  [ecommate-trade-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 17:28:06.331 [main] INFO  [ecommate-trade-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 47 ms. Found 0 Redis repository interfaces.
2025-07-31 17:28:07.645 [main] INFO  [ecommate-trade-server] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=dc182277-21a9-3c46-9b88-975e06506650
2025-07-31 17:28:09.025 [main] INFO  [ecommate-trade-server] com.yomahub.liteflow.util.LOGOPrinter - 
================================================================================================
		 _     ___ _____ _____      _____ _     _____        __
		| |   |_ _|_   _| ____|    |  ___| |   / _ \ \      / /
		| |    | |  | | |  _| _____| |_  | |  | | | \ \ /\ / / 
		| |___ | |  | | | |__|_____|  _| | |__| |_| |\ V  V /  
		|_____|___| |_| |_____|    |_|   |_____\___/  \_/\_/   

		Version: 2.13.2
		Make your code amazing.
		website：https://liteflow.cc
================================================================================================

2025-07-31 17:28:09.818 [main] INFO  [ecommate-trade-server] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-31 17:28:09.818 [main] INFO  [ecommate-trade-server] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5745 ms
2025-07-31 17:28:10.390 [main] INFO  [ecommate-trade-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 17:28:12.173 [main] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1,ecommerce_mate} inited
2025-07-31 17:28:12.566 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-31 17:28:12.994 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:config user=SA
2025-07-31 17:28:12.998 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-31 17:28:13.055 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-31 17:28:13.214 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1e8b3a8
2025-07-31 17:28:13.215 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-31 17:28:14.296 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-07-31 17:28:14.395 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.pool.HikariPool - HikariPool-3 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6ae67bf6
2025-07-31 17:28:14.395 [main] INFO  [ecommate-trade-server] com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-07-31 17:28:17.136 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 17:28:17.136 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: db_center ::: select 1
2025-07-31 17:28:17.208 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 17:28:17.208 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: order_link ::: select 1
2025-07-31 17:28:17.237 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 17:28:17.237 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: db_center ::: select 1
2025-07-31 17:28:17.315 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 17:28:17.315 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: db_center ::: select 1
2025-07-31 17:28:17.402 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Logic SQL: select 1
2025-07-31 17:28:17.403 [main] INFO  [ecommate-trade-server] ShardingSphere-SQL - Actual SQL: db_center ::: select 1
2025-07-31 17:28:17.507 [main] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2,db_center} inited
2025-07-31 17:28:17.508 [main] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [db_center] success
2025-07-31 17:28:17.508 [main] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [ecommerce_mate] success
2025-07-31 17:28:17.508 [main] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [ecommerce_mate]
2025-07-31 17:28:18.599 [main] INFO  [ecommate-trade-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 17:28:18.790 [main] INFO  [ecommate-trade-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-product-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 17:28:18.964 [main] INFO  [ecommate-trade-server] org.redisson.Version - Redisson 3.32.0
2025-07-31 17:28:19.503 [redisson-netty-1-4] INFO  [ecommate-trade-server] org.redisson.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6379
2025-07-31 17:28:19.700 [redisson-netty-1-19] INFO  [ecommate-trade-server] org.redisson.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6379
2025-07-31 17:28:20.750 [main] INFO  [ecommate-trade-server] o.a.r.s.autoconfigure.RocketMQAutoConfiguration - a producer (ecommate-trade-server) init on namesrv **************:9876
2025-07-31 17:28:21.523 [main] INFO  [ecommate-trade-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 17:28:22.102 [main] INFO  [ecommate-trade-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:orderManualSyncMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-31 17:28:22.301 [main] INFO  [ecommate-trade-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:orderSyncHandleMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-31 17:28:22.512 [main] INFO  [ecommate-trade-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:paymentOrderQueryMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-31 17:28:22.718 [main] INFO  [ecommate-trade-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:refundQueryMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-31 17:28:23.502 [main] INFO  [ecommate-trade-server] c.y.liteflow.process.impl.DeclWarpBeanProcess - proxy component[erpOrderAfterSaleHandler] has been found
2025-07-31 17:28:23.525 [main] INFO  [ecommate-trade-server] c.y.liteflow.process.impl.DeclWarpBeanProcess - proxy component[erpOrderStatusSyncHandler] has been found
2025-07-31 17:28:23.539 [main] INFO  [ecommate-trade-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-product-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-31 17:28:23.555 [main] INFO  [ecommate-trade-server] c.y.liteflow.process.impl.DeclWarpBeanProcess - proxy component[matchProductListingSkuHandler] has been found
2025-07-31 17:28:23.581 [main] INFO  [ecommate-trade-server] c.y.liteflow.process.impl.DeclWarpBeanProcess - proxy component[orderStatHandler] has been found
2025-07-31 17:28:23.592 [main] INFO  [ecommate-trade-server] c.y.liteflow.process.impl.DeclWarpBeanProcess - proxy component[platformOrderLogisticsHandler] has been found
2025-07-31 17:28:23.602 [main] INFO  [ecommate-trade-server] c.y.liteflow.process.impl.DeclWarpBeanProcess - proxy component[platformOrderSaveHandler] has been found
2025-07-31 17:28:23.612 [main] INFO  [ecommate-trade-server] c.y.liteflow.process.impl.DeclWarpBeanProcess - proxy component[purchaseOrderAutoGenerate] has been found
2025-07-31 17:28:23.626 [main] INFO  [ecommate-trade-server] c.y.l.process.impl.CmpAroundAspectBeanProcess - component[updatePaymentStatusHandler] has been found
2025-07-31 17:28:26.023 [main] INFO  [ecommate-trade-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-31 17:28:27.575 [main] INFO  [ecommate-trade-server] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-31 17:28:27.819 [main] INFO  [ecommate-trade-server] c.y.ecommate.common.id.config.IdAutoConfiguration - 构建ID生成器时使用随机workId，它的值为: 51
2025-07-31 17:28:29.528 [main] INFO  [ecommate-trade-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-31 17:28:29.671 [main] INFO  [ecommate-trade-server] c.y.liteflow.parser.factory.FlowParserProvider - flow info loaded from local file,path=config/flow.xml
2025-07-31 17:28:29.873 [main] INFO  [ecommate-trade-server] io.undertow - starting server: Undertow - 2.3.13.Final
2025-07-31 17:28:29.892 [main] INFO  [ecommate-trade-server] org.xnio - XNIO version 3.8.8.Final
2025-07-31 17:28:29.920 [main] INFO  [ecommate-trade-server] org.xnio.nio - XNIO NIO Implementation Version 3.8.8.Final
2025-07-31 17:28:30.009 [main] INFO  [ecommate-trade-server] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-31 17:28:30.099 [main] INFO  [ecommate-trade-server] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 38788 (http) with context path '/'
2025-07-31 17:28:30.104 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - initializer namespace from ans.namespace attribute : null
2025-07-31 17:28:30.104 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-07-31 17:28:30.104 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - initializer namespace from namespace attribute :null
2025-07-31 17:28:30.113 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-07-31 17:28:30.120 [main] INFO  [ecommate-trade-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-31 17:28:30.120 [main] INFO  [ecommate-trade-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-31 17:28:30.207 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of ff365c8e-2c5c-405d-b26c-5134c2f7e3fb
2025-07-31 17:28:30.211 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - Create naming rpc client for uuid->ff365c8e-2c5c-405d-b26c-5134c2f7e3fb
2025-07-31 17:28:30.211 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [ff365c8e-2c5c-405d-b26c-5134c2f7e3fb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-07-31 17:28:30.211 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [ff365c8e-2c5c-405d-b26c-5134c2f7e3fb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-07-31 17:28:30.212 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [ff365c8e-2c5c-405d-b26c-5134c2f7e3fb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-07-31 17:28:30.212 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [ff365c8e-2c5c-405d-b26c-5134c2f7e3fb] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-31 17:28:30.212 [main] INFO  [ecommate-trade-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-31 17:28:30.227 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [ff365c8e-2c5c-405d-b26c-5134c2f7e3fb] Success to connect to server [**************:8848] on start up, connectionId = 1753954101215_192.168.48.1_64503
2025-07-31 17:28:30.227 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [ff365c8e-2c5c-405d-b26c-5134c2f7e3fb] Notify connected event to listeners.
2025-07-31 17:28:30.227 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - Grpc connection connect
2025-07-31 17:28:30.227 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [ff365c8e-2c5c-405d-b26c-5134c2f7e3fb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-31 17:28:30.228 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - [ff365c8e-2c5c-405d-b26c-5134c2f7e3fb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$717/0x0000014301562fd8
2025-07-31 17:28:30.230 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - [REGISTER-SERVICE] ecommate-cfx registering service ecommate-trade-server with instance Instance{instanceId='null', ip='*************', port=38788, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={preserved.heart.beat.timeout=3000, preserved.ip.delete.timeout=3000, preserved.register.source=SPRING_CLOUD, version=1.0.0, IPv6=[2408:8459:860:1569:4709:cc1d:2f0b:53b4], preserved.heart.beat.interval=1000}}
2025-07-31 17:28:30.239 [main] INFO  [ecommate-trade-server] c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ecommate-trade-server *************:38788 register finished
2025-07-31 17:28:31.347 [main] INFO  [ecommate-trade-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-31 17:28:32.170 [main] INFO  [ecommate-trade-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-order-sync-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-order-sync_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='manual', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 17:28:32.871 [main] INFO  [ecommate-trade-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-order-sync-handle-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-order-sync-handle_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 17:28:33.620 [main] INFO  [ecommate-trade-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-trade-payment-order-query-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-trade-payment-order-query_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 17:28:34.396 [main] INFO  [ecommate-trade-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-trade-refund-payment-order-query-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-trade-refund-payment-order-query_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 17:28:34.426 [main] INFO  [ecommate-trade-server] com.yaotown.ecommate.trade.TradeApplication - Started TradeApplication in 36.713 seconds (process running for 37.86)
2025-07-31 17:28:34.434 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.CacheData - config listener notify warn timeout millis use default 60000 millis 
2025-07-31 17:28:34.434 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snapshot = true 
2025-07-31 17:28:34.435 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-trade-server-local.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-31 17:28:34.453 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-trade-server-local.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-31 17:28:34.453 [main] INFO  [ecommate-trade-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-trade-server-local.yaml, group=DEFAULT_GROUP
2025-07-31 17:28:34.454 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-trade-server+DEFAULT_GROUP+ecommate-cfx
2025-07-31 17:28:34.454 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-trade-server, group=DEFAULT_GROUP, cnt=1
2025-07-31 17:28:34.454 [main] INFO  [ecommate-trade-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-trade-server, group=DEFAULT_GROUP
2025-07-31 17:28:34.454 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-trade-server.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-31 17:28:34.454 [main] INFO  [ecommate-trade-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-trade-server.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-31 17:28:34.454 [main] INFO  [ecommate-trade-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-trade-server.yaml, group=DEFAULT_GROUP
2025-07-31 17:28:34.904 [RMI TCP Connection(3)-*************] INFO  [ecommate-trade-server] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 17:28:34.904 [RMI TCP Connection(3)-*************] INFO  [ecommate-trade-server] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 17:28:34.910 [RMI TCP Connection(3)-*************] INFO  [ecommate-trade-server] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-31 17:28:54.904 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] io.undertow - stopping server: Undertow - 2.3.13.Final
2025-07-31 17:28:54.916 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-31 17:28:54.920 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-31 17:28:54.920 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] ecommate-cfx deregistering service ecommate-trade-server with instance: Instance{instanceId='null', ip='*************', port=38788, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
2025-07-31 17:28:54.926 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-07-31 17:28:54.927 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-31 17:28:54.927 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-31 17:28:54.927 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-31 17:28:54.927 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-31 17:28:54.927 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-31 17:28:54.927 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-31 17:28:54.927 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-31 17:28:54.927 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-31 17:28:54.927 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-31 17:28:54.928 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-31 17:28:54.928 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-31 17:28:54.928 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->ff365c8e-2c5c-405d-b26c-5134c2f7e3fb
2025-07-31 17:28:54.928 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@6494ec90[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 8]
2025-07-31 17:28:54.928 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-31 17:28:54.928 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@99d3e12[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-31 17:28:54.928 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.common.remote.client - Close current connection 1753954101215_192.168.48.1_64503
2025-07-31 17:28:54.931 [nacos-grpc-client-executor-**************-13] INFO  [ecommate-trade-server] c.a.nacos.common.remote.client.grpc.GrpcClient - [1753954101215_192.168.48.1_64503]Ignore complete event,isRunning:false,isAbandon=false
2025-07-31 17:28:54.936 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] c.a.nacos.common.remote.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2a166b83[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 14]
2025-07-31 17:28:54.936 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->ff365c8e-2c5c-405d-b26c-5134c2f7e3fb
2025-07-31 17:28:54.936 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] c.a.n.client.auth.ram.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-31 17:28:54.936 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] c.a.n.client.auth.ram.identify.CredentialService - [null] CredentialService is freed
2025-07-31 17:28:54.936 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
2025-07-31 17:28:54.938 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-trade-refund-payment-order-query-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-trade-refund-payment-order-query_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 17:28:54.939 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-trade-payment-order-query-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-trade-payment-order-query_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 17:28:54.939 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-order-sync-handle-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-order-sync-handle_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 17:28:54.939 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-order-sync-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-order-sync_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='manual', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-31 17:28:57.327 [com.alibaba.nacos.client.Worker.1] INFO  [ecommate-trade-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-07-31 17:28:59.625 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-31 17:28:59.628 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-07-31 17:28:59.632 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-07-31 17:28:59.632 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] c.b.d.d.destroyer.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [db_center] success,
2025-07-31 17:28:59.632 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-31 17:28:59.638 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-31 17:28:59.639 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] c.b.d.d.destroyer.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [ecommerce_mate] success,
2025-07-31 17:28:59.639 [SpringApplicationShutdownHook] INFO  [ecommate-trade-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
