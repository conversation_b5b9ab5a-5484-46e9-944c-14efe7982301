package com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.common.base.core.util.CommonStreamUtil;
import com.yaotown.ecommate.common.core.entity.KeyValue;
import com.yaotown.ecommate.common.core.util.json.JsonUtils;
import com.yaotown.ecommate.trade.domain.common.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.FxgOrderAggregate;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.*;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.ErpOrderInfoSearchVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.AbstractChannelConnector;
import com.yaotown.ecommate.trade.types.enums.erp.ErpPlatformTypeEnum;
import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderShippingTypeEnum;
import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderStatusEnum;
import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderWarrantyStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 抖店渠道连接器
 *
 * <AUTHOR>
 * @date 2025/6/12
 */
@Component
public class FxgChannelConnector extends AbstractChannelConnector<FxgOrderAggregate> {

    @Override
    public String getPlatform() {
        return ErpPlatformTypeEnum.FXG.getValue();
    }

    @Override
    public Integer getOrderCount(Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        ErpOrderInfoSearchVO erpOrderInfoSearchVO = new ErpOrderInfoSearchVO()
                .setXytShopId(shopInfo.getXytShopId())
                .setStartTime(beginDate)
                .setEndTime(endDate);
        PageData<ErpOriginalPlatformOrderEntity> pageData = erpECommerceOrderRepository.selectOriginalOrderByPage(new QueryModel<>(1, 1, erpOrderInfoSearchVO));
        return (int) pageData.getPageTotal();
    }

    @Override
    public List<FxgOrderAggregate> getOrderPage(int pageIndex, int pageSize, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        ErpOrderInfoSearchVO erpOrderInfoSearchVO = new ErpOrderInfoSearchVO()
                .setXytShopId(shopInfo.getXytShopId())
                .setStartTime(beginDate)
                .setEndTime(endDate);
        String now = DateUtil.now();
        PageData<ErpOriginalPlatformOrderEntity> pageData = erpECommerceOrderRepository.selectOriginalOrderByPage(new QueryModel<>(pageIndex, pageSize, erpOrderInfoSearchVO));

        if (CollUtil.isEmpty(pageData.getPageContents())) {
            return Collections.emptyList();
        }
        return CommonStreamUtil.transList(pageData.getPageContents(), (originalOrder) -> {
            FxgOrderAggregate fxgOrderAggregate = JsonUtils.parseObject(originalOrder.getContent(), FxgOrderAggregate.class);
            fxgOrderAggregate.setEcommerceOrderId(originalOrder.getExtOrderId());
            fxgOrderAggregate.setPlatform(getPlatform());
            fxgOrderAggregate.setPlatformOrderId(originalOrder.getId());
            fxgOrderAggregate.setEnterpriseId(shopInfo.getEnterpriseId());
            fxgOrderAggregate.setShopId(shopInfo.getShopId());
            fxgOrderAggregate.setSyncTime(now);
            return fxgOrderAggregate;
        });
    }

    @Override
    public KeyValue<String, List<FxgOrderAggregate>> getOrderPageByCursor(String cursor, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        return new KeyValue<>(cursor, List.of());
    }

    @Override
    public EComLinkPlatformOrderEntity parsePlatformOrder(FxgOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return EComLinkPlatformOrderEntity.builder()
                .platformOrderId(ecommerceOrder.getPlatformOrderId())
                .platform(ecommerceOrder.getPlatform())
                .enterpriseId(ecommerceOrder.getEnterpriseId())
                .shopId(ecommerceOrder.getShopId())
                .extOrderId(ecommerceOrder.getOrderId())
                .payType(ecommerceOrder.getPayType())
                .paymentTradeId(ecommerceOrder.getChannelPaymentNo())
                .totalFee(parseFenCurrency(ecommerceOrder.getOrderAmount()))
                .postFee(parseFenCurrency(ecommerceOrder.getPostAmount()) + parseFenCurrency(ecommerceOrder.getPostInsuranceAmount()))
                .adjustFee(parseFenCurrency(ecommerceOrder.getModifyAmount()) + parseFenCurrency(ecommerceOrder.getModifyPostAmount()))
                .discountFee(parseFenCurrency(ecommerceOrder.getPromotionAmount()))
                .createTime(new Date(ecommerceOrder.getCreateTime() * 1000))
                .payTime(new Date(ecommerceOrder.getPayTime() * 1000))
                .updateTime(new Date(ecommerceOrder.getUpdateTime() * 1000))
                .deliveryTime(new Date(ecommerceOrder.getShipTime() * 1000))
                .orderStatus(this.parseOrderStatus(ecommerceOrder.getOrderStatus(), ecommerceOrder.getMainStatus()).getValue())
                .warrantyStatus(this.parseWarrantyStatus(ecommerceOrder).getValue())
                .shippingType(this.parseShippingType(ecommerceOrder).getValue())
                .build();
    }

    @Override
    public List<EComLinkPlatformOrderItemEntity> parsePlatformOrderItems(FxgOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        List<FxgOrderAggregate.SkuOrderList> skuOrderList = ecommerceOrder.getSkuOrderList();
        if (CollUtil.isEmpty(skuOrderList)) {
            return Collections.emptyList();
        }

        return CommonStreamUtil.transList(skuOrderList, skuOrder -> {
            EComLinkPlatformOrderItemEntity orderItemEntity = EComLinkPlatformOrderItemEntity.builder()
                    .extItemId(skuOrder.getSkuOrderId())
                    .totalFee(skuOrder.getOrderAmount())
                    .paidFee(skuOrder.getPayAmount())
                    .postFee(skuOrder.getPostAmount() + skuOrder.getPostInsuranceAmount())
                    .adjustFee(skuOrder.getModifyAmount() + skuOrder.getModifyPostAmount())
                    .discountFee(skuOrder.getPromotionAmount())
                    .num(skuOrder.getItemNum())
                    .price(skuOrder.getOriginAmount())
                    .extNumIid(skuOrder.getProductId())
                    .extSkuId(skuOrder.getSkuId())
                    .extOuterId(skuOrder.getCode())
                    .extSkuTitle(skuOrder.getProductName())
                    .imageUrl(skuOrder.getProductPic())
                    .itemStatus(this.parseOrderStatus(skuOrder.getOrderStatus(), skuOrder.getMainStatus()).getValue())
                    .warrantyStatus(this.parseItemWarrantyStatus(skuOrder).getValue())
                    .build();

            if (CollUtil.isNotEmpty(skuOrder.getSpec())) {
                String skuSpecChars = skuOrder.getSpec()
                        .stream()
                        .map(skuSpec -> skuSpec.getName() + ":" + skuSpec.getValue())
                        .collect(Collectors.joining(";"));
                orderItemEntity.setSkuSpecChars(skuSpecChars);
            }

            return orderItemEntity;
        });
    }

    @Override
    public List<EComLinkPlatformOrderLogisticsEntity> parsePlatformOrderLogistics(FxgOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        List<FxgOrderAggregate.LogisticsInfo> logisticsInfo = ecommerceOrder.getLogisticsInfo();
        if (CollUtil.isEmpty(logisticsInfo)) {
            return Collections.emptyList();
        }
        return CommonStreamUtil.transList(logisticsInfo, logistics -> EComLinkPlatformOrderLogisticsEntity.builder()
                .shippingTime(new Date(logistics.getShipTime() * 1000))
                .companyCode(logistics.getCompany())
                .companyName(logistics.getCompanyName())
                .deliveryNo(logistics.getTrackingNo())
                .build());
    }

    @Override
    public EComLinkPlatformOrderMemoEntity parsePlatformOrderMemo(FxgOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        EComLinkPlatformOrderMemoEntity orderMemoEntity = EComLinkPlatformOrderMemoEntity.builder()
                .buyerMessage(ecommerceOrder.getCancelReason())
                .buyerMemo(ecommerceOrder.getBuyerWords())
                .sellerMemo(ecommerceOrder.getSellerWords())
                .build();
        // 插旗信息：0：灰 1：紫 2: 青 3：绿 4： 橙 5： 红
        // xytFlag 卖家备注旗帜,1、2、3、4、5分别对应红、黄、绿、蓝、紫
        Integer sellerRemarkStars = ecommerceOrder.getSellerRemarkStars();
        switch (sellerRemarkStars) {
            case 1:
                orderMemoEntity.setSellerFlag(5);
                break;
            case 2:
                orderMemoEntity.setSellerFlag(4);
                break;
            case 3:
                orderMemoEntity.setSellerFlag(3);
                break;
            case 4:
                orderMemoEntity.setSellerFlag(2);
                break;
            case 5:
                orderMemoEntity.setSellerFlag(1);
                break;
        }

        if (StringUtils.isAllBlank(orderMemoEntity.getBuyerMessage(), orderMemoEntity.getBuyerMemo(), orderMemoEntity.getSellerMemo())
                && orderMemoEntity.getSellerFlag() == null)
            return null;

        return orderMemoEntity;
    }

    @Override
    public EComLinkPlatformOrderConsigneeEntity parsePlatformOrderConsignee(FxgOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        FxgOrderAggregate.PostAddr postAddr = ecommerceOrder.getPostAddr();
        if (postAddr == null) {
            return null;
        }
        EComLinkPlatformOrderConsigneeEntity consigneeEntity = EComLinkPlatformOrderConsigneeEntity.builder()
                .receiverMobile(ecommerceOrder.getEncryptPostTel())
                .receiverName(ecommerceOrder.getEncryptPostReceiver())
                .receiverState(postAddr.getProvince().getName())
                .receiverCity(postAddr.getCity().getName())
                .receiverTown(postAddr.getTown().getName())
                .receiverDistrict(postAddr.getStreet().getName())
                .receiverAddress(postAddr.getEncryptDetail())
                .build();

        if (StringUtils.isBlank(consigneeEntity.getReceiverCity()))
            consigneeEntity.setReceiverCity(consigneeEntity.getReceiverState());
        if (StringUtils.isBlank(consigneeEntity.getReceiverDistrict()))
            consigneeEntity.setReceiverDistrict(consigneeEntity.getReceiverTown());

        return consigneeEntity;
    }


    private PlatformOrderShippingTypeEnum parseShippingType(FxgOrderAggregate ecommerceOrder) {
        switch (ecommerceOrder.getOrderType()) {
            case 2:// 虚拟订单
            case 4:// 平台券码
            case 5:// 商家券码
                return PlatformOrderShippingTypeEnum.virtual;
            default:
                return PlatformOrderShippingTypeEnum.express;
        }
    }

    private PlatformOrderWarrantyStatusEnum parseWarrantyStatus(FxgOrderAggregate ecommerceOrder) {
        List<FxgOrderAggregate.SkuOrderList> skuOrderList = ecommerceOrder.getSkuOrderList();
        if (CollUtil.isEmpty(skuOrderList))
            return PlatformOrderWarrantyStatusEnum.NONE;

        Set<PlatformOrderWarrantyStatusEnum> warrantyStatusSet = new HashSet<>();
        for (int i = 0; i < skuOrderList.size(); i++) {
            FxgOrderAggregate.SkuOrderList skuOrder = skuOrderList.get(i);
            warrantyStatusSet.add(this.parseItemWarrantyStatus(skuOrder));
        }

        if (warrantyStatusSet.size() == 1)
            return warrantyStatusSet.iterator().next();
        if (warrantyStatusSet.contains(PlatformOrderWarrantyStatusEnum.REFUND_SUCCESS))
            return PlatformOrderWarrantyStatusEnum.REFUND_PARTIAL;
        return PlatformOrderWarrantyStatusEnum.NONE;
    }

    private PlatformOrderWarrantyStatusEnum parseItemWarrantyStatus(FxgOrderAggregate.SkuOrderList skuOrder) {
        FxgOrderAggregate.AfterSaleInfo afterSaleInfo = skuOrder.getAfterSaleInfo();
        switch (afterSaleInfo.getAfterSaleStatus()) {
            case 6:// 售后申请
            case 11:// 售后已发货
                return PlatformOrderWarrantyStatusEnum.REFUNDING;
            case 27:// 拒绝售后申请
            case 28:// 售后失败
                return PlatformOrderWarrantyStatusEnum.REFUND_CANCEL;
            case 12:// 售后成功
            case 51:// 取消成功
            case 53:// 逆向交易完成
                return PlatformOrderWarrantyStatusEnum.REFUND_SUCCESS;
            case 7:// 售后退货中
            case 13:// 售后换货商家发货
                return PlatformOrderWarrantyStatusEnum.RETURNING;
            case 14:// 售后换货用户收货
                return PlatformOrderWarrantyStatusEnum.RETURN_SUCCESS;
            case 29:// 退货后拒绝退款
                return PlatformOrderWarrantyStatusEnum.RETURN_CANCEL;
            default:
                return PlatformOrderWarrantyStatusEnum.NONE;
        }
    }

    private PlatformOrderStatusEnum parseOrderStatus(String orderStatus, String mainStatus) {
        String status = StringUtils.defaultIfBlank(
                orderStatus,
                mainStatus
        );

        return switch (status) {// 备货中
            case "2", "101" ->// 部分发货
                    PlatformOrderStatusEnum.TOBESHIPPED;
            case "3" ->// 已发货（全部发货）
                    PlatformOrderStatusEnum.DELIVERY;// 已取消
            // 发货前退款完结
            case "4", "21", "22" ->// 发货后退款完结
                    PlatformOrderStatusEnum.CANCEL;
            case "5" ->// 已完成（已收货）
                    PlatformOrderStatusEnum.SIGNED;
            default -> PlatformOrderStatusEnum.PENDING;
        };

    }
}
