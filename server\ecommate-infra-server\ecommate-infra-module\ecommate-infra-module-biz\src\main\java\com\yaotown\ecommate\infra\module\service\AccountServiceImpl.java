package com.yaotown.ecommate.infra.module.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.common.core.entity.OAuth2AccessToken;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.security.core.util.SecurityUtils;
import com.yaotown.ecommate.infra.module.config.wx.WxMinAppProperties;
import com.yaotown.ecommate.infra.module.constants.RocketMQConstants;
import com.yaotown.ecommate.infra.module.convert.accont.AccountConvert;
import com.yaotown.ecommate.infra.module.convert.enterprise.EnterpriseConvert;
import com.yaotown.ecommate.infra.module.enums.AccountStatusEnum;
import com.yaotown.ecommate.infra.module.enums.EnterpriseAccountTypeEnum;
import com.yaotown.ecommate.infra.module.enums.ServiceResultEnum;
import com.yaotown.ecommate.infra.module.enums.ThirdSystemTypeEnum;
import com.yaotown.ecommate.infra.module.enums.oauth2.OAuth2ClientConstants;
import com.yaotown.ecommate.infra.module.mapper.EnterpriseMapper;
import com.yaotown.ecommate.infra.module.mapper.VersionMapper;
import com.yaotown.ecommate.infra.module.mapper.account.AccountEnterpriseMapper;
import com.yaotown.ecommate.infra.module.mapper.account.AccountMapper;
import com.yaotown.ecommate.infra.module.mapper.account.SupplierAccountMapper;
import com.yaotown.ecommate.infra.module.mapper.account.ThirdAccountInfoMapper;
import com.yaotown.ecommate.infra.module.pojo.dto.base.CaptchaReqDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SmsCodeCheckDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.enterprise.account.*;
import com.yaotown.ecommate.infra.module.pojo.dto.supplier.MobileVerifyReqDTO;
import com.yaotown.ecommate.infra.module.pojo.entity.EnterprisePO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.AccountEnterprisePO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.AccountPO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.ThirdAccountInfoPO;
import com.yaotown.ecommate.infra.module.pojo.entity.dept.DeptPO;
import com.yaotown.ecommate.infra.module.pojo.vo.EnterpriseAccountPageReqVO;
import com.yaotown.ecommate.infra.module.pojo.vo.SupplierPageReqVO;
import com.yaotown.ecommate.infra.module.pojo.vo.UserPageReqVO;
import com.yaotown.ecommate.infra.module.pojo.vo.UserProfile.UserProfileUpdatePasswordReqVO;
import com.yaotown.ecommate.infra.module.pojo.vo.UserProfile.UserProfileUpdateReqVO;
import com.yaotown.ecommate.infra.module.service.message.SmsCodeSendService;
import com.yaotown.ecommate.infra.module.service.oauth2.OAuth2TokenService;
import com.yaotown.sdk.mq.bo.ObjectMQMsgBase;
import com.yaotown.sdk.mq.utils.RocketMQEnhanceTemplate;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.*;

import static com.yaotown.ecommate.infra.module.util.CollectionUtils.convertSet;
import static com.yaotown.ecommate.infra.module.util.CollectionUtils.singleton;

/**
 * 账户基础功能实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
@AllArgsConstructor
public class AccountServiceImpl implements AccountService {

    private final AccountMapper accountMapper;
    private final OAuth2TokenService oAuth2TokenService;
    private final AccountEnterpriseService accountEnterpriseService;
    private final EnterpriseMapper enterpriseMapper;
    private final AccountEnterpriseMapper accountEnterpriseMapper;
    private final CaptchaService captchaService;
    private final InvitationService invitationService;
    private final SmsCodeSendService smsCodeSendService;
    private final DeptService deptService;
    private final PasswordEncoder passwordEncoder;
    private final PermissionService permissionService;
    private final VersionMapper versionMapper;
    private final SupplierAccountMapper supplierAccountMapper;

    private final ThirdAccountInfoMapper thirdAccountInfoMapper;

    @Resource
    private RocketMQEnhanceTemplate rocketMQEnhanceTemplate;

    private final WxMinAppProperties wxMinAppProperties;
    /**
     * 注册
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginRespDTO register(RegisterReqDTO registerReqDTO, Integer accountType) {

        // 验证码校验 - 如果smsId为"CAPTCHA_VERIFIED"，则表示已通过图形验证码校验，跳过短信验证码校验
        if (!"CAPTCHA_VERIFIED".equals(registerReqDTO.getSmsId())) {
            SmsCodeCheckDTO smsCodeCheckDTO = SmsCodeCheckDTO.builder()
                    .phone(registerReqDTO.getAccount().getMobile())
                    .code(registerReqDTO.getCode())
                    .smsId(registerReqDTO.getSmsId())
                    .build();
            smsCodeSendService.codeVerify(smsCodeCheckDTO, true);
        }

        if (accountMapper.selectByAccountTypeMobile(registerReqDTO.getAccount().getMobile(),accountType) != null) {
            throw new BusinessException(ServiceResultEnum.SAME_MOBILE_EXIST.getResult());
        }

        AccountPO accountPO = AccountConvert.INSTANCE.toAccountPO(registerReqDTO.getAccount());
        accountPO.setAccountType(accountType);
        accountPO.setNickname(accountPO.getUsername());
        // 过期时间默认设置十年后
        accountPO.setExpiredAt(DateUtil.offset(DateUtil.date(), DateField.YEAR, 10));
        accountPO.setStatus(AccountStatusEnum.NORMAL.getId()); // 设置初始状态为正常
        EnterprisePO enterprisePO = EnterpriseConvert.INSTANCE.toEnterprisePO(registerReqDTO.getEnterprise());
        AccountEnterprisePO accountEnterprisePO = new AccountEnterprisePO();

        // MD5加密：密码+UUID
        accountPO.setHashSalt(UUID.randomUUID().toString(true));
        accountPO.setLoginPassword(SecureUtil.md5(registerReqDTO.getAccount().getLoginPassword() + accountPO.getHashSalt()));

        //注册同时登录
        LoginRespDTO loginRespDTO;


        // 子账号绑定企业
        if (StrUtil.isNotBlank(registerReqDTO.getAccount().getInvitationCode())) {
            Long bindEnterpriseId = Long.valueOf(invitationService.verifyCode(registerReqDTO.getAccount().getInvitationCode()));
            accountMapper.insert(accountPO);
            accountEnterprisePO
                    .setAccountId(accountPO.getId())
                    .setEnterpriseId(bindEnterpriseId)
                    .setEnterpriseAccountType(EnterpriseAccountTypeEnum.ENTERPRISE_SUB_ACCOUNT.getCode());
            accountEnterpriseMapper.insert(accountEnterprisePO);
            loginRespDTO = getLoginRespDTO(bindEnterpriseId, accountPO);
            return loginRespDTO;
        }
        // 主账号绑定企业
        else if (accountMapper.insert(accountPO) > 0 && enterpriseMapper.insert(enterprisePO) > 0) {

            accountEnterprisePO
                    .setAccountId(accountPO.getId())
                    .setEnterpriseId(enterprisePO.getId())
                    .setEnterpriseAccountType(EnterpriseAccountTypeEnum.ENTERPRISE_MAIN_ACCOUNT.getCode());
            accountEnterpriseMapper.insert(accountEnterprisePO);

            //事务提交后再发送消息
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronization() {
                        @Override
                        public void afterCompletion(int status) {
                            // 事务正常提交时发送消息
                            if (status == STATUS_COMMITTED) {
                                // 本地环境需要用自己的MQ
                                // 消息队列
                                ObjectMQMsgBase<Long> enterpriseMsg = new ObjectMQMsgBase<>(
                                        RocketMQConstants.SOURCE_TYPE_ENTERPRISE,
                                        RocketMQConstants.MSG_TYPE_ENTERPRISE,
                                        enterprisePO.getId()
                                );
                                rocketMQEnhanceTemplate.send(RocketMQConstants.ENTERPRISE_CREATE_TOPIC, RocketMQConstants.ENTERPRISE_CRATE_TAG, enterpriseMsg);
                            }
                        }
                    });
            loginRespDTO = getLoginRespDTO(enterprisePO.getId(), accountPO);
            return loginRespDTO;
        }

        throw new BusinessException(ServiceResultEnum.DB_ERROR.getResult());

    }


    /**
     * 登录
     */
    @Override
    public LoginRespDTO login(LoginReqDTO req, Integer accountType) {
        CaptchaReqDTO captcha = req.getCaptcha();
        if (Objects.nonNull(captcha)) {
            captchaService.verifyCaptcha(captcha.getCaptchaKey(), captcha.getCaptchaCode(), true);
        }
        String mobile = req.getMobile();
        String loginPassword = req.getLoginPassword();
        OAuth2AccessToken oAuth2AccessToken = Optional.ofNullable(accountMapper.selectByAccountTypeMobile(mobile,accountType))
                .map(accountPO -> {
                    // 密码验证
                    Optional.of(accountPO)
                            .filter(a -> a.getLoginPassword().equals(SecureUtil.md5(loginPassword + accountPO.getHashSalt())))
                            .orElseThrow(() -> new BusinessException(ServiceResultEnum.LOGIN_ERROR.getResult()));

                    // 企业账户验证
                    AccountEnterprisePO enterprise = Optional.ofNullable(
                                    accountEnterpriseService.getAccountEnterpriseByAccountId(accountPO.getId()).get(0))
                            .orElseThrow(() -> new BusinessException(ServiceResultEnum.ENTERPRISE_NOT_EXIST.getResult()));

                    // 令牌生成
                    return oAuth2TokenService.createAccessToken(
                            enterprise.getEnterpriseId(),
                            accountPO.getId(),
                            accountPO.getAccountType(),
                            OAuth2ClientConstants.CLIENT_ID_DEFAULT,
                            null
                    );
                })
                .orElseThrow(() -> new BusinessException(ServiceResultEnum.USER_NOT_EXIST_ERROR.getResult()));
        AccountPO accountPO = accountMapper.selectByAccountTypeMobile(mobile,accountType);
        //获取企业信息填充返回
        EnterprisePO enterprisePO = enterpriseMapper.selectById(oAuth2AccessToken.getEnterpriseId());
        LoginRespDTO loginRespDTO = AccountConvert.INSTANCE.toLoginRespDTO(accountPO);
        loginRespDTO.setAccessToken(oAuth2AccessToken.getAccessToken());
        loginRespDTO.setEnterpriseId(enterprisePO.getId());
        loginRespDTO.setEnterpriseName(enterprisePO.getEnterpriseName());
        return loginRespDTO;
    }

    /**
     * 管理员登录（仅允许平台账户登录）
     */
    @Override
    public LoginRespDTO adminLogin(AdminLoginReqDTO req) {
        // 1. 验证码校验（如果有）
        if (Objects.nonNull(req.getCaptcha())) {
            captchaService.verifyCaptcha(
                    req.getCaptcha().getCaptchaKey(),
                    req.getCaptcha().getCaptchaCode(),
                    true
            );
        }

        // 2. 查询账户信息
        AccountPO accountPO = Optional.ofNullable(accountMapper.selectByAdminMobile(req.getUsername()))
                .orElseThrow(() -> new BusinessException(ServiceResultEnum.USER_NOT_EXIST_ERROR.getResult()));

        // 3. 强制校验：必须是平台账户
        if (accountPO.getAccountType() != 0) { // 0=平台账户
            throw new BusinessException("非平台账户禁止登录后台");
        }

        // 4. 密码校验
        String encryptedPassword = SecureUtil.md5(req.getLoginPassword() + accountPO.getHashSalt());
        if (!accountPO.getLoginPassword().equals(encryptedPassword)) {
            throw new BusinessException(ServiceResultEnum.LOGIN_ERROR.getResult());
        }

        // 5. 生成Token（平台账户无企业ID）
        OAuth2AccessToken accessToken = oAuth2TokenService.createAccessToken(
                null, // 平台账户无企业ID
                accountPO.getId(),
                accountPO.getAccountType(),
                OAuth2ClientConstants.CLIENT_ID_DEFAULT,
                null
        );

        // 6. 返回结果
        LoginRespDTO resp = AccountConvert.INSTANCE.toLoginRespDTO(accountPO);
        resp.setAccessToken(accessToken.getAccessToken());
        return resp;
    }

    @Override
    public LoginRespDTO loginByMobile(LoginByMobileReqDTO req) {
        // 登录逻辑先查询手机号是否存在再验证验证码
        String mobile = req.getMobile();
        String code = req.getCode();
        String smsId = req.getSmsId();

        //进行手机号检查和验证码校验
        AccountPO accountPO = checkMobileAndVerifyCode(mobile, code, smsId);

        // 获取账户关联的企业信息
        AccountEnterprisePO enterprise = Optional.ofNullable(
                        accountEnterpriseService.getAccountEnterpriseByAccountId(accountPO.getId()).get(0))
                .orElseThrow(() -> new BusinessException(ServiceResultEnum.ENTERPRISE_NOT_EXIST.getResult()));

        // 生成访问令牌
        OAuth2AccessToken oAuth2AccessToken = oAuth2TokenService.createAccessToken(
                enterprise.getEnterpriseId(),
                accountPO.getId(),
                accountPO.getAccountType(),
                OAuth2ClientConstants.CLIENT_ID_DEFAULT,
                null
        );

        // 转换为登录响应对象
        LoginRespDTO loginRespDTO = AccountConvert.INSTANCE.toLoginRespDTO(accountPO);
        loginRespDTO.setAccessToken(oAuth2AccessToken.getAccessToken());
        return loginRespDTO;
    }

    @Override
    public Boolean forgetThePassword(ForgetThePasswordReqDTO req) {
        //忘记密码功能先查询手机号是否存在再验证验证码在保存新的密码
        String mobile = req.getMobile();
        String newPassword = req.getPassword();
        String code = req.getCode();
        String smsId = req.getSmsId();

        //进行手机号检查和验证码校验
        AccountPO accountPO = checkMobileAndVerifyCode(mobile, code, smsId);

        // 更新密码
        accountPO.setHashSalt(UUID.randomUUID().toString(true));
        accountPO.setLoginPassword(SecureUtil.md5(newPassword + accountPO.getHashSalt()));
        int rows = accountMapper.updateById(accountPO);

        return rows > 0;

    }

    @Override
    public Boolean changeMobile(ChangeMobileReqDTO req) {
        String newMobile = req.getNewMobile();
        String newCode = req.getCode();
        String newSmsId = req.getSmsId();

        // 获取当前登录用户的账户信息，通过 SecurityUtils 获取账户 ID
        Long accountId = SecurityUtils.getLoginAccountId();
        AccountPO accountPO = Optional.ofNullable(accountMapper.selectByAccountId(accountId))
                .orElseThrow(() -> new BusinessException(ServiceResultEnum.USER_NOT_EXIST_ERROR.getResult()));

        // 检查新手机号是否已被注册
        if (accountMapper.selectByMobile(newMobile) != null) {
            throw new BusinessException(ServiceResultEnum.SAME_MOBILE_EXIST.getResult());
        }

        // 验证新手机号验证码
        SmsCodeCheckDTO newSmsCodeCheckDTO = SmsCodeCheckDTO.builder()
                .phone(newMobile)
                .code(newCode)
                .smsId(newSmsId)
                .build();
        smsCodeSendService.codeVerify(newSmsCodeCheckDTO, true);

        // 更新手机号
        accountPO.setMobile(newMobile);
        int rows = accountMapper.updateById(accountPO);

        return rows > 0;
    }

    @Override
    public Boolean changePassword(ChangePasswordReqDTO req) {
        // 获取当前登录用户的账户信息，假设通过 SecurityUtils 获取账户 ID
        Long accountId = SecurityUtils.getLoginAccountId();
        AccountPO accountPO = Optional.ofNullable(accountMapper.selectByAccountId(accountId))
                .orElseThrow(() -> new BusinessException(ServiceResultEnum.USER_NOT_EXIST_ERROR.getResult()));

        // 验证旧密码
        String inputOldPasswordHash = SecureUtil.md5(req.getOldPassword() + accountPO.getHashSalt());
        if (!StrUtil.equals(inputOldPasswordHash, accountPO.getLoginPassword())) {
            throw new BusinessException(ServiceResultEnum.OLD_PASSWORD_ERROR.getResult());
        }

        // 更新密码
        accountPO.setHashSalt(UUID.randomUUID().toString(true));
        accountPO.setLoginPassword(SecureUtil.md5(req.getNewPassword() + accountPO.getHashSalt()));
        int rows = accountMapper.updateById(accountPO);

        return rows > 0;
    }


    /**
     * 检查手机号是否存在并验证验证码
     *
     * @param mobile 手机号
     * @param code   验证码
     * @param smsId  短信ID
     * @return 账户实体对象
     */
    private AccountPO checkMobileAndVerifyCode(String mobile, String code, String smsId) {
        // 查询手机号对应的账户是否存在
        AccountPO accountPO = Optional.ofNullable(accountMapper.selectByMobile(mobile))
                .orElseThrow(() -> new BusinessException(ServiceResultEnum.USER_NOT_EXIST_ERROR.getResult()));

        // 验证码校验
        SmsCodeCheckDTO smsCodeCheckDTO = SmsCodeCheckDTO.builder()
                .phone(mobile)
                .code(code)
                .smsId(smsId)
                .build();
        smsCodeSendService.codeVerify(smsCodeCheckDTO, true);
        return accountPO;
    }

    /**
     * 登出
     */
    @Override
    public void logout() {
        String loginToken = SecurityUtils.getLoginToken();
        oAuth2TokenService.removeAccessToken(loginToken);
    }

    /**
     * 用户注销（永久删除账户）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deactivateAccount(AccountDeactivateReqDTO req) {
        // 获取当前登录用户
        Long accountId = SecurityUtils.getLoginAccountId();
        AccountPO account = validateUserExists(accountId);
        
        // 验证验证码
        CaptchaReqDTO captcha = req.getCaptcha();
        captchaService.verifyCaptcha(captcha.getCaptchaKey(), captcha.getCaptchaCode(), false);
        
        // 验证密码
        String encodedPassword = SecureUtil.md5(req.getLoginPassword() + account.getHashSalt());
        if (!encodedPassword.equals(account.getLoginPassword())) {
            throw new BusinessException("密码验证失败，请检查密码是否正确");
        }
        
        // 检查用户状态，确保不是已删除状态
        if (account.getDeleteFlag() != null && account.getDeleteFlag() == 1) {
            throw new BusinessException("账户已被删除，无法重复注销");
        }
        
        // 将用户状态设置为停用，而不是删除用户数据
        AccountPO updateObj = new AccountPO();
        updateObj.setId(accountId);
        updateObj.setStatus(AccountStatusEnum.DISABLED.getId()); // 设置状态为停用
        accountMapper.updateById(updateObj);
        
        // 清除当前登录token
        String loginToken = SecurityUtils.getLoginToken();
        oAuth2TokenService.removeAccessToken(loginToken);

        log.info("用户注销成功，账户ID：{}", accountId);
        return true;
    }

    @Override
    public Boolean exists(AccountExistsReqDTO reqDTO) {
        // 判断验证码
        CaptchaReqDTO captcha = reqDTO.getCaptcha();
        captchaService.verifyCaptcha(captcha.getCaptchaKey(), captcha.getCaptchaCode(), false);
        return Objects.nonNull(accountMapper.selectByMobile(reqDTO.getMobile()));
    }

    @Override
    public AccountPO getUser(Long accountId) {
        return accountMapper.selectByAccountId(accountId);
    }

    @Override
    public Long createUser(AccountSaveReqDTO createReqDTO) {
        //TODO 1.1 校验账户配合(租戶下的用户数量限制校验)
//        tenantService.handleTenantInfo(tenant -> {
//            long count = accountMapper.selectCount();
//            if (count >= tenant.getAccountCount()) {
//                throw new BusinessException("创建用户失败，原因：超过租户最大租户配额 ");
//            }
//        });
        // 1.2 校验正确性
        validateUserForCreateOrUpdate(null, createReqDTO.getUsername(),
                createReqDTO.getMobile(), createReqDTO.getEmail(), createReqDTO.getDeptId());
        // 2.1 插入用户
        AccountPO user = BeanUtil.toBean(createReqDTO, AccountPO.class);
        // MD5加密：密码+UUID
        user.setHashSalt(UUID.randomUUID().toString(true));
        user.setLoginPassword(SecureUtil.md5(createReqDTO.getLoginPassword() + user.getHashSalt()));
        accountMapper.insert(user);

        return user.getId();
    }

    @Override
    public void updateUser(AccountSaveReqDTO updateReqDTO) {
        updateReqDTO.setLoginPassword(null); // 特殊：此处不更新密码
        // 1. 校验正确性
        AccountPO oldUser = validateUserForCreateOrUpdate(updateReqDTO.getId(), updateReqDTO.getUsername(),
                updateReqDTO.getMobile(), updateReqDTO.getEmail(), updateReqDTO.getDeptId());

        // 2.1 更新用户
        AccountPO updateObj = BeanUtil.toBean(updateReqDTO, AccountPO.class);
        accountMapper.updateById(updateObj);
    }

    @Override
    public void deleteUser(Long id) {
        // 1. 校验用户存在
        AccountPO user = validateUserExists(id);

        // 2.1 删除用户
        accountMapper.deleteById(id);
        // 2.2 删除用户关联数据
        permissionService.processUserDeleted(id);
    }

    @Override
    public void updateUserPassword(Long id, String password) {
        // 1. 校验用户存在
        AccountPO user = validateUserExists(id);

        // 2. 更新密码
        AccountPO updateObj = new AccountPO();
        updateObj.setId(id);
        updateObj.setHashSalt(UUID.randomUUID().toString(true));
        updateObj.setLoginPassword(SecureUtil.md5(password + updateObj.getHashSalt()));// 加密密码
        accountMapper.updateById(updateObj);

    }

    @Override
    public void updateUserStatus(Long id, Integer status) {
        // 校验用户存在
        validateUserExists(id);
        // 校验状态值是否有效
        AccountStatusEnum.findById(status).orElseThrow(() -> new BusinessException("无效的账号状态值"));
        // 更新状态
        AccountPO updateObj = new AccountPO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        accountMapper.updateById(updateObj);
    }

    @Override
    public PageData<AccountRespDTO> getUserPage(QueryModel<UserPageReqVO> queryModel) {
        UserPageReqVO param = queryModel.getParam();
        // 如果有角色编号，查询角色对应的用户编号
        Set<Long> userIds = param.getRoleId() != null ?
                permissionService.getUserRoleIdListByRoleId(singleton(param.getRoleId())) : null;
        // 分页查询
        Set<Long> deptCondition = getDeptCondition(param.getDeptId());
        return queryModel.queryPageData(()-> accountMapper.selectPageAccount(param, deptCondition, userIds));
    }

    @Override
    public List<AccountPO> getUserListByStatus(Integer status) {
        return accountMapper.selectListByStatus(status);
    }

    @Override
    public void updateUserProfile(Long id, UserProfileUpdateReqVO reqVO) {
        // 校验正确性
        validateUserExists(id);
        validateEmailUnique(id, reqVO.getEmail());
        validateMobileUnique(id, reqVO.getMobile());
        // 执行更新
        accountMapper.updateById(BeanUtil.toBean(reqVO, AccountPO.class).setId(id));
    }

    @Override
    public void updateUserPassword(Long id, UserProfileUpdatePasswordReqVO reqVO) {
        //获取用户
        AccountPO account = validateUserExists(id);
        // 校验旧密码密码
        validateOldPassword(id, reqVO.getOldPassword());
        // 执行更新
        AccountPO updateObj = new AccountPO().setId(id);
        updateObj.setHashSalt(UUID.randomUUID().toString(true));
        updateObj.setLoginPassword(SecureUtil.md5(reqVO.getNewPassword() + account.getHashSalt()));// 加密密码// 加密密码
        accountMapper.updateById(updateObj);
    }

    @Override
    public void saveOpenId(Long accountId, String jsCode) throws Exception {
        ThirdAccountInfoPO thirdAccountInfoByThirdOpenId = thirdAccountInfoMapper.findThirdAccountInfoByThirdOpenId(accountId, ThirdSystemTypeEnum.WX_MINI_APP.getCode());
        if (Objects.isNull(thirdAccountInfoByThirdOpenId)) {
            ThirdAccountInfoPO thirdAccountInfoPO = new ThirdAccountInfoPO();
            thirdAccountInfoPO.setAccountId(accountId);
            thirdAccountInfoPO.setThirdOpenId(getOpenId(jsCode));
            thirdAccountInfoPO.setThirdSystemTenantId("");
            thirdAccountInfoPO.setThirdSystemType(ThirdSystemTypeEnum.WX_MINI_APP.getCode());
            thirdAccountInfoMapper.insert(thirdAccountInfoPO);
        }
    }

    private String getOpenId(String jsCode) throws Exception {
        HttpClient client = HttpClient.newHttpClient();
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid="+wxMinAppProperties.getAppid()+"&secret="+wxMinAppProperties.getSecret()+"&js_code=" + jsCode + "&grant_type=authorization_code";
        URI uri = URI.create(url);
        //组装请求
        HttpRequest request = HttpRequest.newBuilder()
                .uri(uri)
                .header("Content-Type", "application/json")
                .GET()
                .build();
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        String body = response.body();
        JSONObject jsonObject = JSONObject.parseObject(body);

        if (!Objects.isNull(jsonObject.getString("errmsg"))) {
            throw new BusinessException(jsonObject.toString());

        }
        return jsonObject.getString("openid");
    }

    /**
     * 校验旧密码
     *
     * @param id          用户 id
     * @param oldPassword 旧密码
     */
    @VisibleForTesting
    void validateOldPassword(Long id, String oldPassword) {
        AccountPO user = accountMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        //获取加密的旧密码
        String oldPasswordEncode = oldPassword + user.getHashSalt();
        //如果旧密码不一致
        if (!oldPasswordEncode.equals(user.getLoginPassword())) {
            throw new BusinessException("用户密码校验失败");
        }
    }

    /**
     * 获得部门条件：查询指定部门的子部门编号们，包括自身
     *
     * @param deptId 部门编号
     * @return 部门编号集合
     */
    private Set<Long> getDeptCondition(Long deptId) {
        if (deptId == null) {
            return Collections.emptySet();
        }
        Set<Long> deptIds = convertSet(deptService.getChildDeptList(deptId), DeptPO::getId);
        deptIds.add(deptId); // 包括自身
        return deptIds;
    }

    private String encodePassword(String loginPassword) {

        return passwordEncoder.encode(loginPassword);
    }

    private AccountPO validateUserForCreateOrUpdate(Long id, String username, String mobile, String email,
                                                    Long deptId) {
        //TODO 关闭数据权限，避免因为没有数据权限，查询不到数据，进而导致唯一校验不正确

        // 校验用户存在
        AccountPO user = validateUserExists(id);
//        // 校验用户名唯一
//        validateUsernameUnique(id, username);
        // 校验手机号唯一
        validateMobileUnique(id, mobile);
        // 校验邮箱唯一
        validateEmailUnique(id, email);
        // 校验部门处于开启状态
        Collection<Long> ids = CollUtil.newArrayList(deptId);
        deptService.validateDeptList(ids);
        return user;
    }

    @VisibleForTesting
    AccountPO validateUserExists(Long id) {
        if (id == null) {
            return null;
        }
        AccountPO user = accountMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    @VisibleForTesting
    void validateUsernameUnique(Long id, String username) {
        if (StrUtil.isBlank(username)) {
            return;
        }
//        AccountPO user = accountMapper.selectByUsername(username);
//        if (user == null) {
//            return;
//        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw new BusinessException("用户账号已存在");
        }
    }

    @VisibleForTesting
    void validateMobileUnique(Long id, String mobile) {
        if (StrUtil.isBlank(mobile)) {
            return;
        }
        AccountPO user = accountMapper.selectByMobile(mobile);
        AccountPO accountPO = accountMapper.selectByAdminMobile(mobile);
        if (user == null && accountPO == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw new BusinessException("手机号已经存在");
        }
        if (user != null && !user.getId().equals(id)) {
            throw new BusinessException("手机号已经存在");
        }
    }

    @VisibleForTesting
    void validateEmailUnique(Long id, String email) {
        if (StrUtil.isBlank(email)) {
            return;
        }
        AccountPO user = accountMapper.selectByEmail(email);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw new BusinessException("邮箱已经存在");
        }
        if (!user.getId().equals(id)) {
            throw new BusinessException("邮箱已经存在");
        }
    }

    /**
     * 令牌生成
     *
     * @param enterpriseId 企业ID
     * @param account      账号实体
     */
    private LoginRespDTO getLoginRespDTO(Long enterpriseId, AccountPO account) {
        OAuth2AccessToken accessToken = oAuth2TokenService.createAccessToken(
                enterpriseId,
                account.getId(),
                account.getAccountType(),
                OAuth2ClientConstants.CLIENT_ID_DEFAULT,
                null);
        LoginRespDTO loginRespDTO = AccountConvert.INSTANCE.toLoginRespDTO(account);
        loginRespDTO.setEnterpriseId(enterpriseId);
        loginRespDTO.setAccessToken(accessToken.getAccessToken());
        return loginRespDTO;
    }

    /**
     * 获取供应商分页列表
     */
    @Override
    public PageData<SupplierEnterpriseRespDTO> getSupplierPage(QueryModel<SupplierPageReqVO> queryModel) {
        return queryModel.queryPageData(() -> supplierAccountMapper.selectPageSupplierAccount(queryModel.getParam()));
    }

    /**
     * 获取企业账户分页列表
     */
    @Override
    public PageData<EnterpriseAccountRespDTO> getEnterpriseAccountPage(QueryModel<EnterpriseAccountPageReqVO> queryModel) {
        // 执行查询
        return queryModel.queryPageData(() -> supplierAccountMapper.selectEnterpriseAccountPage(queryModel.getParam()));
    }

    /**
     * 验证手机号（子账户首次登录时使用）
     *
     * @param req 手机验证请求
     * @return 是否验证成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean verifyMobile(MobileVerifyReqDTO req) {
        // 1. 验证手机号与验证码
        String mobile = req.getMobile();
        String code = req.getCode();
        String smsId = req.getSmsId();

        // 获取当前登录用户
        Long accountId = SecurityUtils.getLoginAccountId();
        if (accountId == null) {
            throw new BusinessException("用户未登录");
        }

        // 检查账户是否需要验证
        AccountPO accountPO = accountMapper.selectById(accountId);
        if (accountPO == null) {
            throw new BusinessException("账户不存在");
        }

        // 只有需要验证手机号的账户才能进行验证
        if (accountPO.getNeedMobileVerify() == null || accountPO.getNeedMobileVerify() != 1) {
            throw new BusinessException("当前账户不需要验证手机号");
        }

        // 检查手机号是否匹配
        if (!accountPO.getMobile().equals(mobile)) {
            throw new BusinessException("手机号不匹配");
        }
        SmsCodeCheckDTO smsCodeCheck = SmsCodeCheckDTO.builder()
                .phone(mobile)
                .code(code)
                .smsId(smsId)
                .build();
        // 进行验证码校验
        smsCodeSendService.codeVerify(smsCodeCheck, true);

        // 2. 验证通过后，更新账户状态
        accountPO.setNeedMobileVerify(0); // 设置不再需要验证
        accountPO.setMobileVerify(1); // 设置手机号已验证
        
        // 3. 更新数据库
        accountMapper.updateById(accountPO);
        
        return true;
    }

    /**
     * 检查账户是否需要验证手机号
     *
     * @param accountId 账户ID
     * @return 是否需要验证手机号
     */
    @Override
    public Boolean needMobileVerify(Long accountId) {
        AccountPO accountPO = accountMapper.selectById(accountId);
        if (accountPO == null) {
            throw new BusinessException("账户不存在");
        }
        
        // 检查needMobileVerify字段
        return accountPO.getNeedMobileVerify() != null && accountPO.getNeedMobileVerify() == 1;
    }
}

