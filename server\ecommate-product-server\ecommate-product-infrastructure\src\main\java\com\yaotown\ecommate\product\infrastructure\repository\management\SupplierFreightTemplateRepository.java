package com.yaotown.ecommate.product.infrastructure.repository.management;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierFreightTemplateEntity;
import com.yaotown.ecommate.product.domain.product.management.repository.ISupplierFreightTemplateRepository;
import com.yaotown.ecommate.product.infrastructure.convert.management.SupplierFreightTemplateConvert;
import com.yaotown.ecommate.product.infrastructure.mapper.management.SupplierFreightTemplateMapper;
import com.yaotown.ecommate.product.infrastructure.po.management.SupplierFreightTemplatePO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商运费模板仓储实现
 */
@Repository
@AllArgsConstructor
public class SupplierFreightTemplateRepository implements ISupplierFreightTemplateRepository {

    private final SupplierFreightTemplateMapper supplierFreightTemplateMapper;

    @Override
    public boolean save(SupplierFreightTemplateEntity entity) {
        SupplierFreightTemplatePO po = SupplierFreightTemplateConvert.INSTANCE.convertToPO(entity);
        return supplierFreightTemplateMapper.insert(po) > 0;
    }

    @Override
    public boolean saveBatch(List<SupplierFreightTemplateEntity> entities) {
        if (CollUtil.isEmpty(entities)) {
            return false;
        }
        List<SupplierFreightTemplatePO> poList = entities.stream()
                .map(SupplierFreightTemplateConvert.INSTANCE::convertToPO)
                .collect(Collectors.toList());
        return supplierFreightTemplateMapper.insertBatchSomeColumn(poList) > 0;
    }

    @Override
    public boolean update(SupplierFreightTemplateEntity entity) {
        SupplierFreightTemplatePO po = SupplierFreightTemplateConvert.INSTANCE.convertToPO(entity);
        return supplierFreightTemplateMapper.updateById(po) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        SupplierFreightTemplatePO po = new SupplierFreightTemplatePO();
        po.setId(id);
        return supplierFreightTemplateMapper.removeById(po) > 0;
    }

    @Override
    public boolean deleteByTemplateName(String templateName, Long enterpriseId) {
        LambdaUpdateWrapper<SupplierFreightTemplatePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SupplierFreightTemplatePO::getTemplateName, templateName)
                .eq(SupplierFreightTemplatePO::getEnterpriseId, enterpriseId)
                .set(SupplierFreightTemplatePO::getDeleteFlag, 1);
        return supplierFreightTemplateMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public SupplierFreightTemplateEntity getById(Long id, Long enterpriseId) {
        LambdaQueryWrapper<SupplierFreightTemplatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFreightTemplatePO::getId, id)
                .eq(SupplierFreightTemplatePO::getEnterpriseId, enterpriseId)
                .eq(SupplierFreightTemplatePO::getDeleteFlag, 0);
        SupplierFreightTemplatePO po = supplierFreightTemplateMapper.selectOne(queryWrapper);
        return SupplierFreightTemplateConvert.INSTANCE.convertToEntity(po);
    }

    @Override
    public List<SupplierFreightTemplateEntity> listByTemplateName(String templateName, Long enterpriseId) {
        LambdaQueryWrapper<SupplierFreightTemplatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFreightTemplatePO::getTemplateName, templateName)
                .eq(SupplierFreightTemplatePO::getEnterpriseId, enterpriseId);
        List<SupplierFreightTemplatePO> poList = supplierFreightTemplateMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(poList)) {
            return new ArrayList<>();
        }
        return poList.stream()
                .map(SupplierFreightTemplateConvert.INSTANCE::convertToEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<SupplierFreightTemplateEntity> listActiveTemplate(Long enterpriseId) {
        return SupplierFreightTemplateConvert.INSTANCE.convertToEntityList(supplierFreightTemplateMapper.listActiveTemplate(enterpriseId));
    }

    @Override
    public PageData<SupplierFreightTemplateEntity> page(QueryModel<String> queryModel, Long enterpriseId) {
        String param = queryModel.getParam();
        return queryModel.queryPageData(() ->
                        supplierFreightTemplateMapper.list(
                                enterpriseId, param),
                SupplierFreightTemplateConvert.INSTANCE::convertToEntity);
    }

    @Override
    public List<String> listTemplateNames(Long enterpriseId) {
        return supplierFreightTemplateMapper.selectDistinctTemplateNames(enterpriseId);
    }
} 