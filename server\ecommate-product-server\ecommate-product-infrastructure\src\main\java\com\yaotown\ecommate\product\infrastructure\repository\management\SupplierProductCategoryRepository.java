package com.yaotown.ecommate.product.infrastructure.repository.management;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierProductCategoryEntity;
import com.yaotown.ecommate.product.domain.product.management.repository.ISupplierProductCategoryRepository;
import com.yaotown.ecommate.product.infrastructure.convert.management.SupplierProductCategoryConvert;
import com.yaotown.ecommate.product.infrastructure.mapper.management.SupplierProductCategoryMapper;
import com.yaotown.ecommate.product.infrastructure.po.management.SupplierProductCategoryPO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 供应商商品分类仓储实现类
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Repository
@AllArgsConstructor
public class SupplierProductCategoryRepository implements ISupplierProductCategoryRepository {

    private final SupplierProductCategoryMapper supplierProductCategoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCategory(SupplierProductCategoryEntity categoryEntity) {
        // 检查分类名称是否已存在
        if (checkCategoryNameExists(categoryEntity.getCategoryName(), categoryEntity.getParentId(), categoryEntity.getEnterpriseId())) {
            throw new BusinessException("分类名称已存在");
        }

        // 校验父分类是否存在
        if (categoryEntity.getParentId() != null && !categoryEntity.getParentId().equals(0L)) {
            SupplierProductCategoryPO parentPO = supplierProductCategoryMapper.selectById(categoryEntity.getParentId());
            if (parentPO == null || !Objects.equals(parentPO.getEnterpriseId(), categoryEntity.getEnterpriseId()) || parentPO.getDeleteFlag() == 1) {
                throw new BusinessException("父分类不存在");
            }

            // 设置层级
            categoryEntity.setLevel(parentPO.getLevel() + 1);
            if (categoryEntity.getLevel() > 3) {
                throw new BusinessException("分类层级不能超过3级");
            }
        } else {
            // 顶级分类
            categoryEntity.setParentId(0L);
            categoryEntity.setLevel(1);
        }

        // 初始化商品数量
        if (categoryEntity.getProductCount() == null) {
            categoryEntity.setProductCount(0L);
        }

        // 初始化权重
        if (categoryEntity.getWeight() == null) {
            categoryEntity.setWeight(0);
        }

        // 初始化状态
        if (categoryEntity.getStatus() == null) {
            categoryEntity.setStatus(1); // 默认显示
        }

        // 转换为PO
        SupplierProductCategoryPO categoryPO = SupplierProductCategoryConvert.INSTANCE.convertToCategoryPO(categoryEntity);
        categoryPO.setDeleteFlag(0);

        // 保存
        supplierProductCategoryMapper.insert(categoryPO);
        return categoryPO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(SupplierProductCategoryEntity categoryEntity) {
        // 校验分类是否存在
        SupplierProductCategoryPO existingPO = supplierProductCategoryMapper.selectById(categoryEntity.getId());
        if (existingPO == null || !Objects.equals(existingPO.getEnterpriseId(), categoryEntity.getEnterpriseId()) || existingPO.getDeleteFlag() == 1) {
            throw new BusinessException("分类不存在");
        }

        // 如果名称有变化，检查名称是否已存在
        if (categoryEntity.getCategoryName() != null && !categoryEntity.getCategoryName().equals(existingPO.getCategoryName())) {
            if (checkCategoryNameExists(categoryEntity.getCategoryName(), existingPO.getParentId(), categoryEntity.getEnterpriseId())) {
                throw new BusinessException("分类名称已存在");
            }
        }

        // 转换为PO
        SupplierProductCategoryPO categoryPO = new SupplierProductCategoryPO();
        categoryPO.setId(categoryEntity.getId());
        categoryPO.setCategoryName(categoryEntity.getCategoryName());
        categoryPO.setWeight(categoryEntity.getWeight());
        categoryPO.setRate(categoryEntity.getRate() != null ? categoryEntity.getRate() : existingPO.getRate());
        categoryPO.setStatus(categoryEntity.getStatus());

        // 更新
        return supplierProductCategoryMapper.updateSelectiveById(categoryPO) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(Long categoryId, Long enterpriseId) {
        // 校验分类是否存在
        SupplierProductCategoryPO existingPO = supplierProductCategoryMapper.selectById(categoryId);
        if (existingPO == null || !Objects.equals(existingPO.getEnterpriseId(), enterpriseId) || existingPO.getDeleteFlag() == 1) {
            throw new BusinessException("分类不存在");
        }

        // 检查是否有子分类
        long subCategoryCount = supplierProductCategoryMapper.countByParentId(categoryId, enterpriseId);
        if (subCategoryCount > 0) {
            throw new BusinessException("该分类下有子分类，不能删除");
        }

        // 检查分类下是否有商品
        if (existingPO.getProductCount() > 0) {
            throw new BusinessException("该分类下有商品，不能删除");
        }

        // 逻辑删除
        return supplierProductCategoryMapper.deleteCategory(existingPO) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long categoryId, Integer status, Long enterpriseId) {
        // 校验分类是否存在
        SupplierProductCategoryPO existingPO = supplierProductCategoryMapper.selectById(categoryId);
        if (existingPO == null || !Objects.equals(existingPO.getEnterpriseId(), enterpriseId) || existingPO.getDeleteFlag() == 1) {
            throw new BusinessException("分类不存在");
        }

        // 如果状态没有变化，直接返回成功
        if (Objects.equals(existingPO.getStatus(), status)) {
            return true;
        }

        // 更新状态
        return supplierProductCategoryMapper.updateStatus(categoryId, status, existingPO.getModifierId(), existingPO.getModifierName()) > 0;
    }

    @Override
    public SupplierProductCategoryEntity  getCategory(Long categoryId, Long enterpriseId) {
        SupplierProductCategoryPO categoryPO = supplierProductCategoryMapper.selectById(categoryId);
        if (categoryPO == null || !Objects.equals(categoryPO.getEnterpriseId(), enterpriseId) || categoryPO.getDeleteFlag() == 1) {
            return null;
        }
        return SupplierProductCategoryConvert.INSTANCE.convertToCategoryEntity(categoryPO);
    }

    @Override
    public List<SupplierProductCategoryEntity> listCategories(Long enterpriseId) {
        List<SupplierProductCategoryPO> categoryPOList = supplierProductCategoryMapper.selectByEnterpriseId(enterpriseId);
        return SupplierProductCategoryConvert.INSTANCE.convertToCategoryEntityList(categoryPOList);
    }

    @Override
    public List<SupplierProductCategoryEntity> listVisibleCategories(Long enterpriseId) {
        List<SupplierProductCategoryPO> categoryPOList = supplierProductCategoryMapper.selectVisibleByEnterpriseId(enterpriseId);
        return SupplierProductCategoryConvert.INSTANCE.convertToCategoryEntityList(categoryPOList);
    }

    @Override
    public List<SupplierProductCategoryEntity> listSubCategories(Long parentId, Long enterpriseId) {
        // 获取所有分类
        List<SupplierProductCategoryPO> allCategoryPOs = supplierProductCategoryMapper.selectByEnterpriseId(enterpriseId);
        List<SupplierProductCategoryEntity> allCategories = SupplierProductCategoryConvert.INSTANCE.convertToCategoryEntityList(allCategoryPOs);

        // 按照父ID分组
        Map<Long, List<SupplierProductCategoryEntity>> categoryMap = allCategories.stream()
                .collect(Collectors.groupingBy(SupplierProductCategoryEntity::getParentId));

        // 构建树形结构，从指定父分类开始
        return buildCategoryTree(categoryMap, parentId);
    }

    @Override
    public List<SupplierProductCategoryEntity> listVisibleSubCategories(Long parentId, Long enterpriseId) {
        // 获取所有显示状态的分类
        List<SupplierProductCategoryPO> allVisibleCategoryPOs = supplierProductCategoryMapper.selectVisibleByEnterpriseId(enterpriseId);
        List<SupplierProductCategoryEntity> allVisibleCategories = SupplierProductCategoryConvert.INSTANCE.convertToCategoryEntityList(allVisibleCategoryPOs);

        // 按照父ID分组
        Map<Long, List<SupplierProductCategoryEntity>> categoryMap = allVisibleCategories.stream()
                .collect(Collectors.groupingBy(SupplierProductCategoryEntity::getParentId));

        // 构建树形结构，从指定父分类开始
        return buildCategoryTree(categoryMap, parentId);
    }

    @Override
    public List<SupplierProductCategoryEntity> selectVisibleByLevel(Long enterpriseId,Integer lever) {
        return SupplierProductCategoryConvert.INSTANCE.convertToCategoryEntityList(supplierProductCategoryMapper.selectVisibleByLevel(enterpriseId, lever));
    }

    @Override
    public PageData<SupplierProductCategoryEntity> pageQueryCategories(QueryModel<Long> queryModel) {
        Long enterpriseId = queryModel.getParam();
        //查询一级类目
        return queryModel.queryPageData(() ->
                        supplierProductCategoryMapper.selectByLevel(
                                enterpriseId, 1),
                SupplierProductCategoryConvert.INSTANCE::convertToCategoryEntity);
    }

    @Override
    public List<SupplierProductCategoryEntity> getCategoryTree(Long enterpriseId) {
        // 获取所有分类
        List<SupplierProductCategoryEntity> allCategories = listCategories(enterpriseId);

        // 按照父ID分组
        Map<Long, List<SupplierProductCategoryEntity>> categoryMap = allCategories.stream()
                .collect(Collectors.groupingBy(SupplierProductCategoryEntity::getParentId));

        // 构建树形结构，从顶级分类开始
        return buildCategoryTree(categoryMap, 0L);
    }

    @Override
    public List<SupplierProductCategoryEntity> getVisibleCategoryTree(Long enterpriseId) {
        // 获取显示状态的分类
        List<SupplierProductCategoryEntity> visibleCategories = listVisibleCategories(enterpriseId);

        // 按照父ID分组
        Map<Long, List<SupplierProductCategoryEntity>> categoryMap = visibleCategories.stream()
                .collect(Collectors.groupingBy(SupplierProductCategoryEntity::getParentId));

        // 构建树形结构，从顶级分类开始
        return buildCategoryTree(categoryMap, 0L);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductCount(Long categoryId, long count) {
        // 校验分类是否存在
        SupplierProductCategoryPO existingPO = supplierProductCategoryMapper.selectById(categoryId);
        if (existingPO == null || existingPO.getDeleteFlag() == 1) {
            throw new BusinessException("分类不存在");
        }

        // 更新商品数量
        return supplierProductCategoryMapper.updateProductCount(categoryId, count) > 0;
    }

    @Override
    public boolean checkCategoryNameExists(String categoryName, Long parentId, Long enterpriseId) {
        SupplierProductCategoryPO categoryPO = supplierProductCategoryMapper.selectByCategoryName(enterpriseId, categoryName, parentId);
        return categoryPO != null;
    }

    @Override
    public SupplierProductCategoryEntity getCategoryByName(String categoryName, Long enterpriseId) {
        // 根据分类名称和企业ID查询分类
        SupplierProductCategoryPO categoryPO = supplierProductCategoryMapper.selectByCategoryName(enterpriseId, categoryName, null);
        if (categoryPO == null) {
            return null;
        }
        return SupplierProductCategoryConvert.INSTANCE.convertToCategoryEntity(categoryPO);
    }

    @Override
    public List<SupplierProductCategoryEntity> getCategoryPath(Long categoryId, Long enterpriseId) {
        List<SupplierProductCategoryEntity> path = new ArrayList<>();
        
        // 查询当前分类
        SupplierProductCategoryEntity category = getCategory(categoryId, enterpriseId);
        if (category == null) {
            return path;
        }
        
        // 添加当前分类
        path.add(category);
        
        // 如果是顶级分类，直接返回
        if (category.getParentId() == null || category.getParentId() == 0L) {
            return path;
        }
        
        // 递归获取父级分类
        Long parentId = category.getParentId();
        while (parentId != null && parentId > 0) {
            SupplierProductCategoryEntity parent = getCategory(parentId, enterpriseId);
            if (parent == null) {
                break;
            }
            
            // 将父级分类添加到路径开头，保证路径顺序是从一级到三级
            path.add(0, parent);
            
            // 获取上一级父分类ID
            parentId = parent.getParentId();
            if (parentId == null || parentId == 0L) {
                break;
            }
        }
        
        return path;
    }

    /**
     * 递归构建分类树
     *
     * @param categoryMap 按父ID分组的分类Map
     * @param parentId    当前父ID
     * @return 构建好的树形结构
     */
    private List<SupplierProductCategoryEntity> buildCategoryTree(Map<Long, List<SupplierProductCategoryEntity>> categoryMap, Long parentId) {
        List<SupplierProductCategoryEntity> children = categoryMap.get(parentId);
        if (children == null) {
            return new ArrayList<>();
        }

        for (SupplierProductCategoryEntity child : children) {
            List<SupplierProductCategoryEntity> subChildren = buildCategoryTree(categoryMap, child.getId());
            child.setChildren(subChildren);
        }

        return children;
    }
} 