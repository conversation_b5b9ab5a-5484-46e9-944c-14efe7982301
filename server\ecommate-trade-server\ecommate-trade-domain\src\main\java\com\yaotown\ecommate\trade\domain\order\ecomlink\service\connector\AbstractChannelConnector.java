package com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector;

import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.ECommerceOrderModel;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.OrderSyncWayVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.PlatformParamVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.repository.IErpECommerceOrderRepository;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 平台渠道连接器抽象类
 */
public abstract class AbstractChannelConnector<T extends ECommerceOrderModel> implements IChannelConnector<T> {

    @Resource
    protected IErpECommerceOrderRepository erpECommerceOrderRepository;

    @Override
    public PlatformParamVO getPlatformParam() {
        return PlatformParamVO.builder()
                .getTypes(new GetType[]{GetType.builder().value(0).name("默认时间").build()})
                .defaultPageSize(100)
                .intervalSeconds(8 * 3600)
                .aheadSeconds(300)
                .maxFetchCount(Integer.MAX_VALUE)
                .orderSyncWay(OrderSyncWayVO.PAGE)
                .saveDecryptConsignee(true)
                .circuitBreakerPages(5000)
                .defaultReturnAddressPageSize(100)
                .build();
    }

    protected Long parseFenCurrency(String value) {
        value = StringUtils.replace(value, "￥", "");
        value = StringUtils.replace(value, "$", "");
        return toCurrency(value, 0);
    }

    /**
     * 根据指定精度,将字符串转换成整数
     *
     * @param val       字符串值
     * @param precision 精度,如保留2位则填2(分为单位),如果保留3位则填3(厘为单位)
     * @return long 整数
     */
    protected Long toCurrency(String val, int precision) {
        if ((null == val) || (val.trim().length() == 0)) {
            return 0L;
        }

        val = val.replaceAll("￥", "").replaceAll(",", "").replaceAll("[$]", "");
        if ((null == val) || (val.trim().length() == 0)) {
            return 0L;
        }

        BigDecimal decimal = new BigDecimal(val);
        decimal = decimal.movePointRight(precision);

        return decimal.toBigInteger().longValue();
    }

}
