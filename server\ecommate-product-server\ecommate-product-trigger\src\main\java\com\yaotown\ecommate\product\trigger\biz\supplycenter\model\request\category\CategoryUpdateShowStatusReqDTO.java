package com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request.category;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CategoryUpdateShowStatusReqDTO {
    /**
     * 父级类目ID
     */
    @NotBlank(message = "父级类目ID不能为空")
    private String parentId;

    /**
     * 平台
     */
    @NotBlank(message = "")
    private String platform;

    /**
     * 是否显示(0:否, 1:是)
     */
    @NotNull(message = "")
    private Integer showStatus;
}
