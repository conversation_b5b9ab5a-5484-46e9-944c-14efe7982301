package com.yaotown.ecommate.product.infrastructure.mapper.management;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yaotown.ecommate.common.core.enums.BooleanEnum;
import com.yaotown.ecommate.product.infrastructure.po.management.SupplierFreightTemplatePO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 供应商运费模板Mapper
 */
@Mapper
public interface SupplierFreightTemplateMapper extends BaseMapper<SupplierFreightTemplatePO> {

    /**
     * 批量插入
     *
     * @param poList PO列表
     * @return 影响行数
     */
    int insertBatchSomeColumn(List<SupplierFreightTemplatePO> poList);

    /**
     * 查询企业的所有模板名称
     *
     * @param enterpriseId 企业ID
     * @return 模板名称列表
     */
    @Select("SELECT DISTINCT template_name FROM yt_freight_template WHERE enterprise_id = #{enterpriseId} AND delete_flag = 0")
    List<String> selectDistinctTemplateNames(@Param("enterpriseId") Long enterpriseId);

    default List<SupplierFreightTemplatePO> list(Long enterpriseId, String templateName) {
        LambdaQueryWrapper<SupplierFreightTemplatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFreightTemplatePO::getEnterpriseId, enterpriseId)
                .like(StringUtils.isNotBlank(templateName), SupplierFreightTemplatePO::getTemplateName, templateName)
                .orderByDesc(SupplierFreightTemplatePO::getUpdated);
        return selectList(queryWrapper);
    }

    int removeById(SupplierFreightTemplatePO po);


    default List<SupplierFreightTemplatePO> listActiveTemplate(Long enterpriseId){
        LambdaQueryWrapper<SupplierFreightTemplatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFreightTemplatePO::getEnterpriseId,enterpriseId);
        queryWrapper.eq(SupplierFreightTemplatePO::getStatus, BooleanEnum.TRUE.getId());
        return selectList(queryWrapper);
    }
} 