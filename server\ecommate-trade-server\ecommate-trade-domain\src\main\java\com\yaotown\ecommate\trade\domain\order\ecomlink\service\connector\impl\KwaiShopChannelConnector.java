package com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.impl;

import com.yaotown.ecommate.common.core.entity.KeyValue;
import com.yaotown.ecommate.trade.domain.common.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.FxgOrderAggregate;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.KwaiShopOrderAggregate;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.*;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.AbstractChannelConnector;
import com.yaotown.ecommate.trade.types.enums.erp.ErpPlatformTypeEnum;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 抖店渠道连接器
 *
 * <AUTHOR>
 * @date 2025/6/12
 */
@Component
public class KwaiShopChannelConnector extends AbstractChannelConnector<KwaiShopOrderAggregate> {

    @Override
    public String getPlatform() {
        return ErpPlatformTypeEnum.KWAISHOP.getValue();
    }

    @Override
    public Integer getOrderCount(Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        return 0;
    }

    @Override
    public List<KwaiShopOrderAggregate> getOrderPage(int pageIndex, int pageSize, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        return List.of();
    }

    @Override
    public KeyValue<String, List<KwaiShopOrderAggregate>> getOrderPageByCursor(String cursor, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        return new KeyValue<>(cursor, List.of());
    }

    @Override
    public EComLinkPlatformOrderEntity parsePlatformOrder(KwaiShopOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return null;
    }

    @Override
    public List<EComLinkPlatformOrderItemEntity> parsePlatformOrderItems(KwaiShopOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return List.of();
    }

    @Override
    public List<EComLinkPlatformOrderLogisticsEntity> parsePlatformOrderLogistics(KwaiShopOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return List.of();
    }

    @Override
    public EComLinkPlatformOrderMemoEntity parsePlatformOrderMemo(KwaiShopOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return null;
    }

    @Override
    public EComLinkPlatformOrderConsigneeEntity parsePlatformOrderConsignee(KwaiShopOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return null;
    }

}
