package com.yaotown.ecommate.product.trigger.biz.convert;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.ecommate.product.api.model.response.ShopInfoRespDTO;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopPlatformTypeEntity;
import com.yaotown.ecommate.product.trigger.biz.enterprise.model.response.ShopInfoDTO;
import com.yaotown.ecommate.product.trigger.biz.enterprise.model.response.ShopPlatformTypeDTO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T14:34:21+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Alibaba)"
)
public class ShopInfoConvertImpl implements ShopInfoConvert {

    @Override
    public ShopInfoEntity toShopInfoEntity(ShopInfoDTO shopInfoDTO) {
        if ( shopInfoDTO == null ) {
            return null;
        }

        ShopInfoEntity shopInfoEntity = new ShopInfoEntity();

        shopInfoEntity.setShopId( shopInfoDTO.getShopId() );
        shopInfoEntity.setShopName( shopInfoDTO.getShopName() );
        shopInfoEntity.setPlatformType( shopInfoDTO.getPlatformType() );
        shopInfoEntity.setAuthTime( shopInfoDTO.getAuthTime() );
        shopInfoEntity.setLogoUrl( shopInfoDTO.getLogoUrl() );

        return shopInfoEntity;
    }

    @Override
    public PageData<ShopInfoDTO> toShopInfoEntityList(PageData<ShopInfoEntity> shopInfoEntities) {
        if ( shopInfoEntities == null ) {
            return null;
        }

        PageData<ShopInfoDTO> pageData = new PageData<ShopInfoDTO>();

        pageData.setPageContents( toShopInfo( shopInfoEntities.getPageContents() ) );
        pageData.setPageNum( shopInfoEntities.getPageNum() );
        pageData.setPageSize( shopInfoEntities.getPageSize() );
        pageData.setPageTotal( shopInfoEntities.getPageTotal() );
        pageData.setPages( shopInfoEntities.getPages() );

        return pageData;
    }

    @Override
    public PageData<ShopInfoRespDTO> toShopInfoRespDTOPageData(PageData<ShopInfoEntity> shopInfoEntities) {
        if ( shopInfoEntities == null ) {
            return null;
        }

        PageData<ShopInfoRespDTO> pageData = new PageData<ShopInfoRespDTO>();

        pageData.setPageContents( shopInfoEntityListToShopInfoRespDTOList( shopInfoEntities.getPageContents() ) );
        pageData.setPageNum( shopInfoEntities.getPageNum() );
        pageData.setPageSize( shopInfoEntities.getPageSize() );
        pageData.setPageTotal( shopInfoEntities.getPageTotal() );
        pageData.setPages( shopInfoEntities.getPages() );

        return pageData;
    }

    @Override
    public List<ShopInfoEntity> toShopInfoVOList(List<ShopInfoDTO> shopInfo) {
        if ( shopInfo == null ) {
            return null;
        }

        List<ShopInfoEntity> list = new ArrayList<ShopInfoEntity>( shopInfo.size() );
        for ( ShopInfoDTO shopInfoDTO : shopInfo ) {
            list.add( toShopInfoEntity( shopInfoDTO ) );
        }

        return list;
    }

    @Override
    public List<ShopPlatformTypeDTO> toShopShopPlatformType(List<ShopPlatformTypeEntity> shopPlatformTypeEntities) {
        if ( shopPlatformTypeEntities == null ) {
            return null;
        }

        List<ShopPlatformTypeDTO> list = new ArrayList<ShopPlatformTypeDTO>( shopPlatformTypeEntities.size() );
        for ( ShopPlatformTypeEntity shopPlatformTypeEntity : shopPlatformTypeEntities ) {
            list.add( shopPlatformTypeEntityToShopPlatformTypeDTO( shopPlatformTypeEntity ) );
        }

        return list;
    }

    @Override
    public List<ShopInfoDTO> toShopInfo(List<ShopInfoEntity> shopInfo) {
        if ( shopInfo == null ) {
            return null;
        }

        List<ShopInfoDTO> list = new ArrayList<ShopInfoDTO>( shopInfo.size() );
        for ( ShopInfoEntity shopInfoEntity : shopInfo ) {
            list.add( shopInfoEntityToShopInfoDTO( shopInfoEntity ) );
        }

        return list;
    }

    @Override
    public ShopInfoRespDTO toShopInfoRespDTO(ShopInfoEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ShopInfoRespDTO shopInfoRespDTO = new ShopInfoRespDTO();

        shopInfoRespDTO.setShopId( entity.getShopId() );
        shopInfoRespDTO.setState( entity.getState() );
        shopInfoRespDTO.setExShopId( entity.getExShopId() );
        shopInfoRespDTO.setXytShopId( entity.getXytShopId() );
        shopInfoRespDTO.setExpiredDate( entity.getExpiredDate() );
        shopInfoRespDTO.setEnterpriseId( entity.getEnterpriseId() );
        shopInfoRespDTO.setShopType( entity.getShopType() );
        shopInfoRespDTO.setShopName( entity.getShopName() );
        shopInfoRespDTO.setPlatformType( entity.getPlatformType() );
        shopInfoRespDTO.setContactsName( entity.getContactsName() );
        shopInfoRespDTO.setContactsMobile( entity.getContactsMobile() );
        shopInfoRespDTO.setShopUrl( entity.getShopUrl() );
        shopInfoRespDTO.setAuthMode( entity.getAuthMode() );
        shopInfoRespDTO.setAuthStatus( entity.getAuthStatus() );
        shopInfoRespDTO.setAuthTime( entity.getAuthTime() );
        shopInfoRespDTO.setAppKey( entity.getAppKey() );
        shopInfoRespDTO.setAppSecret( entity.getAppSecret() );
        shopInfoRespDTO.setAccessToken( entity.getAccessToken() );
        shopInfoRespDTO.setRefreshToken( entity.getRefreshToken() );
        shopInfoRespDTO.setNextRefreshTokenTime( entity.getNextRefreshTokenTime() );
        shopInfoRespDTO.setRemarks( entity.getRemarks() );
        shopInfoRespDTO.setLogoUrl( entity.getLogoUrl() );
        shopInfoRespDTO.setAccountId( entity.getAccountId() );
        shopInfoRespDTO.setXytTenantId( entity.getXytTenantId() );

        return shopInfoRespDTO;
    }

    protected List<ShopInfoRespDTO> shopInfoEntityListToShopInfoRespDTOList(List<ShopInfoEntity> list) {
        if ( list == null ) {
            return null;
        }

        List<ShopInfoRespDTO> list1 = new ArrayList<ShopInfoRespDTO>( list.size() );
        for ( ShopInfoEntity shopInfoEntity : list ) {
            list1.add( toShopInfoRespDTO( shopInfoEntity ) );
        }

        return list1;
    }

    protected ShopPlatformTypeDTO shopPlatformTypeEntityToShopPlatformTypeDTO(ShopPlatformTypeEntity shopPlatformTypeEntity) {
        if ( shopPlatformTypeEntity == null ) {
            return null;
        }

        ShopPlatformTypeDTO shopPlatformTypeDTO = new ShopPlatformTypeDTO();

        shopPlatformTypeDTO.setPlatformName( shopPlatformTypeEntity.getPlatformName() );
        shopPlatformTypeDTO.setPlatformValue( shopPlatformTypeEntity.getPlatformValue() );
        shopPlatformTypeDTO.setCount( shopPlatformTypeEntity.getCount() );

        return shopPlatformTypeDTO;
    }

    protected ShopInfoDTO shopInfoEntityToShopInfoDTO(ShopInfoEntity shopInfoEntity) {
        if ( shopInfoEntity == null ) {
            return null;
        }

        ShopInfoDTO shopInfoDTO = new ShopInfoDTO();

        shopInfoDTO.setShopId( shopInfoEntity.getShopId() );
        shopInfoDTO.setShopName( shopInfoEntity.getShopName() );
        shopInfoDTO.setAuthTime( shopInfoEntity.getAuthTime() );
        shopInfoDTO.setLogoUrl( shopInfoEntity.getLogoUrl() );
        shopInfoDTO.setPlatformType( shopInfoEntity.getPlatformType() );

        return shopInfoDTO;
    }
}
