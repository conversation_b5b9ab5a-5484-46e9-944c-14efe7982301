package com.yaotown.ecommate.product.domain.enterprise.service.connector.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.core.util.json.JsonUtils;
import com.yaotown.ecommate.product.domain.enterprise.repository.IXytShopInfoRepository;
import com.yaotown.ecommate.product.domain.enterprise.service.connector.ShopRefreshTokenConnector;
import com.yaotown.ecommate.product.types.enums.erp.ErpPlatformTypeEnum;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 视频号小店刷新Token实现
 * 
 * <AUTHOR>
 * @date 2025/7/22
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SphxdRefreshTokenConnector implements ShopRefreshTokenConnector {

    private final IXytShopInfoRepository xytShopInfoRepository;

    @Override
    public String getPlatform() {
        return ErpPlatformTypeEnum.SPHXD.getValue();
    }

    @Override
    public void refreshToken(RefreshTokenContext refreshTokenContext) {
        SphxdTokenResponse tokenResponse = this.getToken(refreshTokenContext.getAppKey(), refreshTokenContext.getAppSecret());
        refreshTokenContext.setAccessToken(tokenResponse.getAccessToken());
        refreshTokenContext.setExpiresInSeconds(tokenResponse.getExpiresIn());
    }

    private SphxdTokenResponse getToken(String appId, String secret) {
        // 先查询小亚通店铺信息表，看是否有对应的appId记录
        Map<String, Object> shopInfo = xytShopInfoRepository.findShopInfoByAppIdAndPlatformType(appId,secret, ErpPlatformTypeEnum.SPHXD.getValue());
        
        // 如果查到记录，检查next_refresh_token_time是否有效
        if (shopInfo != null && !shopInfo.isEmpty()) {
            // 检查next_refresh_token_time是否小于当前时间
            Object nextRefreshTokenTime = shopInfo.get("next_refresh_token_time");
            if (nextRefreshTokenTime instanceof Date) {
                Date now = new Date();
                if (((Date) nextRefreshTokenTime).before(now)) {
                    log.info("[刷新店铺Token][视频号小店] 从数据库获取的token已过期，next_refresh_token_time小于当前时间: appId={}", appId);
                    // 不使用数据库查询的结果，直接调用接口获取新token
                    return getTokenFromApi(appId, secret);
                }
            }
            
            log.info("[刷新店铺Token][视频号小店] 从数据库获取token信息: appId={}", appId);
            return buildTokenResponseFromDb(shopInfo);
        }
        
        // 如果没有查到记录，调用接口获取token
        log.info("[刷新店铺Token][视频号小店] 从数据库未查询到token信息，调用接口获取: appId={}", appId);
        return getTokenFromApi(appId, secret);
    }
    
    /**
     * 从API获取Token
     * 
     * @param appId 应用ID
     * @param secret 密钥
     * @return Token响应
     */
    private SphxdTokenResponse getTokenFromApi(String appId, String secret) {
        String url = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s&force_refresh=true", appId, secret);
        HttpRequest httpRequest = HttpRequest.get(url);
        String responseStr;

        try (HttpResponse response = httpRequest.execute()) {
            responseStr = response.body();
            log.info("[刷新店铺Token][视频号小店] response : {}", responseStr);
        } catch (Exception e) {
            log.error("[刷新店铺Token][视频号小店] error : {}", e.getMessage());
            throw new BusinessException("视频号小店刷新Token失败");
        }

        if (StringUtils.isBlank(responseStr))
            throw new BusinessException(responseStr);
        return JsonUtils.parseObject(responseStr, SphxdTokenResponse.class);
    }
    
    /**
     * 从数据库记录构建TokenResponse
     *
     * @param shopInfo 数据库查询结果
     * @return TokenResponse对象
     */
    private SphxdTokenResponse buildTokenResponseFromDb(Map<String, Object> shopInfo) {
        SphxdTokenResponse response = new SphxdTokenResponse();
        
        // 设置access_token
        Object accessToken = shopInfo.get("access_token");
        if (accessToken != null) {
            response.setAccessToken(accessToken.toString());
        } else {
            log.warn("[刷新店铺Token][视频号小店] 数据库中access_token为空");
        }
        
        // 计算过期时间
        Object expiredDate = shopInfo.get("expired_date");
        if (expiredDate instanceof Date) {
            // 计算当前时间到过期时间的秒数
            long currentTime = System.currentTimeMillis();
            long expireTime = ((Date) expiredDate).getTime();
            int expiresIn = (int) ((expireTime - currentTime) / 1000);
            // 确保过期时间为正数
            response.setExpiresIn(Math.max(expiresIn, 0));
            log.info("[刷新店铺Token][视频号小店] 从数据库计算token过期时间: {}秒", response.getExpiresIn());
        } else {
            // 默认过期时间
            response.setExpiresIn(7200);
            log.info("[刷新店铺Token][视频号小店] 使用默认过期时间: 7200秒");
        }
        
        return response;
    }

    /**
     * 视频号小店刷新Token API响应对象
     */
    @Data
    public static class SphxdTokenResponse {

        /**
         * 获取到的凭证
         */
        @JsonProperty("access_token")
        private String accessToken;

        /**
         * 凭证有效时间，单位：秒，目前是7200秒之内的值
         */
        @JsonProperty("expires_in")
        private Integer expiresIn;

        /**
         * 错误码
         */
        @JsonProperty("errcode")
        private Integer errcode;

        /**
         * 错误信息
         */
        @JsonProperty("errmsg")
        private String errmsg;
    }
}
