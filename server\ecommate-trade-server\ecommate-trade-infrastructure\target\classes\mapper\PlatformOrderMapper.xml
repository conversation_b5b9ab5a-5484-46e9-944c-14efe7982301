<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaotown.ecommate.trade.infrastructure.mapper.PlatformOrderMapper">
  <resultMap id="BaseResultMap" type="com.yaotown.ecommate.trade.infrastructure.po.PlatformOrderPO">
    <!--@mbg.generated-->
    <!--@Table yt_platform_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="xyt_order_id" jdbcType="BIGINT" property="xytOrderId" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="supplier_enterprise_id" jdbcType="BIGINT" property="supplierEnterpriseId" />
    <result column="current_enterprise_id" jdbcType="BIGINT" property="currentEnterpriseId" />
    <result column="xyt_tenant_id" jdbcType="BIGINT" property="xytTenantId" />
    <result column="xyt_supplier_tenant_id" jdbcType="BIGINT" property="xytSupplierTenantId" />
    <result column="xyt_brand_tenant_id" jdbcType="BIGINT" property="xytBrandTenantId" />
    <result column="xyt_current_tenant_id" jdbcType="BIGINT" property="xytCurrentTenantId" />
    <result column="ext_order_id" jdbcType="VARCHAR" property="extOrderId" />
    <result column="wms_order_id" jdbcType="VARCHAR" property="wmsOrderId" />
    <result column="order_kind" jdbcType="SMALLINT" property="orderKind" />
    <result column="ext_app_id" jdbcType="VARCHAR" property="extAppId" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="wms_create_time" jdbcType="TIMESTAMP" property="wmsCreateTime" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="total_fee" jdbcType="BIGINT" property="totalFee" />
    <result column="discount_fee" jdbcType="BIGINT" property="discountFee" />
    <result column="post_fee" jdbcType="BIGINT" property="postFee" />
    <result column="paid_fee" jdbcType="BIGINT" property="paidFee" />
    <result column="paid_points" jdbcType="INTEGER" property="paidPoints" />
    <result column="payment_trade_id" jdbcType="VARCHAR" property="paymentTradeId" />
    <result column="adjust_fee" jdbcType="BIGINT" property="adjustFee" />
    <result column="order_status" jdbcType="SMALLINT" property="orderStatus" />
    <result column="warranty_status" jdbcType="SMALLINT" property="warrantyStatus" />
    <result column="revoke_status" jdbcType="SMALLINT" property="revokeStatus" />
    <result column="order_status_reason" jdbcType="SMALLINT" property="orderStatusReason" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="ex_shop_id" jdbcType="VARCHAR" property="exShopId" />
    <result column="xyt_shop_id" jdbcType="VARCHAR" property="xytShopId" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="buyer_id" jdbcType="VARCHAR" property="buyerId" />
    <result column="referee_id" jdbcType="VARCHAR" property="refereeId" />
    <result column="referee_nick" jdbcType="VARCHAR" property="refereeNick" />
    <result column="shipping_type" jdbcType="VARCHAR" property="platformOrderShippingTypeEnum" />
    <result column="sku_num" jdbcType="INTEGER" property="skuNum" />
    <result column="sku_brief" jdbcType="VARCHAR" property="skuBrief" />
    <result column="wms_id" jdbcType="BIGINT" property="wmsId" />
    <result column="xyt_create_op" jdbcType="BIGINT" property="xytCreateOp" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
    <result column="done_time" jdbcType="TIMESTAMP" property="doneTime" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="done_version" jdbcType="BIGINT" property="doneVersion" />
    <result column="xyt_reserve_stock_id" jdbcType="BIGINT" property="xytReserveStockId" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, xyt_order_id, enterprise_id, supplier_enterprise_id, current_enterprise_id, xyt_tenant_id, 
    xyt_supplier_tenant_id, xyt_brand_tenant_id, xyt_current_tenant_id, ext_order_id, 
    wms_order_id, order_kind, ext_app_id, pay_time, create_time, update_time, wms_create_time, 
    pay_type, total_fee, discount_fee, post_fee, paid_fee, paid_points, payment_trade_id, 
    adjust_fee, order_status, warranty_status, revoke_status, order_status_reason, platform, 
    shop_id, ex_shop_id, xyt_shop_id, buyer_nick, buyer_id, referee_id, referee_nick, 
    shipping_type, sku_num, sku_brief, wms_id, xyt_create_op, sync_time, done_time, delivery_time, 
    done_version, xyt_reserve_stock_id, delete_flag
  </sql>

  <sql id="Column_List">
    ${po}.id, ${po}.xyt_order_id, ${po}.enterprise_id, ${po}.supplier_enterprise_id, ${po}.current_enterprise_id, ${po}.xyt_tenant_id,
    ${po}.xyt_supplier_tenant_id, ${po}.xyt_brand_tenant_id, ${po}.xyt_current_tenant_id, ${po}.ext_order_id,
    ${po}.wms_order_id, ${po}.order_kind, ${po}.ext_app_id, ${po}.pay_time, ${po}.create_time, ${po}.update_time, ${po}.wms_create_time,
    ${po}.pay_type, ${po}.total_fee, ${po}.discount_fee, ${po}.post_fee, ${po}.paid_fee, ${po}.paid_points, ${po}.payment_trade_id,
    ${po}.adjust_fee, ${po}.order_status, ${po}.warranty_status, ${po}.revoke_status, ${po}.order_status_reason, ${po}.platform,
    ${po}.shop_id, ${po}.ex_shop_id, ${po}.xyt_shop_id, ${po}.buyer_nick, ${po}.buyer_id, ${po}.referee_id, ${po}.referee_nick,
    ${po}.shipping_type, ${po}.sku_num, ${po}.sku_brief, ${po}.wms_id, ${po}.xyt_create_op, ${po}.sync_time, ${po}.done_time, ${po}.delivery_time,
    ${po}.done_version, ${po}.xyt_reserve_stock_id, ${po}.delete_flag
  </sql>

  <select id="selectPlatformOrderList" resultMap="BaseResultMap">
    SELECT
    <include refid="Column_List">
      <property name="po" value="po"/>
    </include>
    FROM yt_platform_order po
    WHERE po.delete_flag = 0
    <if test="queryWithListingRelation == true">
      AND EXISTS (
      SELECT 1 FROM yt_platform_order_item poi
      WHERE poi.platform_order_id = po.id
      AND poi.product_listing_id >0
      AND poi.product_id >0
      AND poi.sku_id >0
      AND poi.listing_sku_id >0
      )
    </if>
    <!-- 后续可能还需要根据采购单的状态一起判断 -->
    <if test="orderQueryStatus != null">
      <!-- 聚合状态1:待发货 - 平台订单状态 -->
      <if test="orderQueryStatus == 1">
        AND po.order_status IN (0,5,10,20, 25, 30, 40)
      </if>
      <!-- 聚合状态2:已发货 - 平台订单状态 -->
      <if test="orderQueryStatus == 2">
        AND po.order_status IN (50, 60)
      </if>
      <!-- 聚合状态3:已完成 - 平台订单状态 -->
      <if test="orderQueryStatus == 3">
        AND po.order_status IN (70, 90)
      </if>
      <!-- 聚合状态4:已失效 - 平台订单状态 -->
      <if test="orderQueryStatus == 4">
        AND po.order_status IN (80, 999, 9999)
      </if>
      <!-- 聚合状态5:售后中 - 平台订单状态 -->
      <if test="orderQueryStatus == 5">
        AND po.warranty_status IN (1, 4, 7)
      </if>
    </if>
    <if test="platformOrderIds != null and platformOrderIds.size() > 0">
      AND po.id IN
      <foreach collection="platformOrderIds" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
    <if test="enterpriseId != null">
      AND po.enterprise_id = #{enterpriseId}
    </if>
    ORDER BY po.create_time DESC
  </select>

  <select id="selectCountStatus" resultType="com.yaotown.ecommate.trade.domain.order.fulfillment.model.entity.OrderStatusStatisticsEntity">
      SELECT
      CASE
      WHEN po.order_status IN (0, 5, 10, 20, 25, 30, 40) THEN 1
      WHEN po.order_status IN (50, 60) THEN 2
      WHEN po.order_status IN (70, 90) THEN 3
      WHEN po.order_status IN (80, 999, 9999) THEN 4
      ELSE NULL
      END AS aggregate_status,
      COUNT(*) AS count
      FROM yt_platform_order po
      WHERE po.delete_flag = 0
      <if test="loginEnterpriseId != null">
          AND po.enterprise_id = #{loginEnterpriseId}
      </if>
      <if test="queryWithListingRelation == true">
          AND EXISTS (
          SELECT 1 FROM yt_platform_order_item poi
          WHERE poi.platform_order_id = po.id -- 使用别名
          AND poi.product_listing_id > 0
          AND poi.product_id > 0
          AND poi.sku_id > 0
          AND poi.listing_sku_id > 0
          )
      </if>
      GROUP BY aggregate_status
  </select>

  <select id="selectWarrantyCountStatus" resultType="com.yaotown.ecommate.trade.domain.order.fulfillment.model.entity.OrderStatusStatisticsEntity">
    SELECT
    IF(po.warranty_status IN (1, 4, 7), 5, -1) AS aggregate_status,
    COUNT(*) AS count
    FROM yt_platform_order po
    WHERE po.delete_flag = 0
    AND po.warranty_status IN (1, 4, 7)
    <if test="loginEnterpriseId != null">
      AND po.enterprise_id = #{loginEnterpriseId}
    </if>
    <if test="queryWithListingRelation == true">
      AND EXISTS (
      SELECT 1 FROM yt_platform_order_item poi
      WHERE poi.platform_order_id = po.id -- 使用别名
      AND poi.product_listing_id > 0
      AND poi.product_id > 0
      AND poi.sku_id > 0
      AND poi.listing_sku_id > 0
      )
    </if>
    GROUP BY aggregate_status
  </select>
</mapper>