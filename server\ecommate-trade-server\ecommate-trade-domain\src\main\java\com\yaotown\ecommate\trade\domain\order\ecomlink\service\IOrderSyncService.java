package com.yaotown.ecommate.trade.domain.order.ecomlink.service;

import com.yaotown.ecommate.trade.domain.common.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.EComLinkPlatformOrderAggregate;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.ECommerceOrderModel;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.IChannelConnector;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 订单同步服务
 *
 * <AUTHOR>
 * @date 2025/6/12
 */
public interface IOrderSyncService {

    /**
     * 定时任务拉取
     *
     * @param shopInfo
     */
    void syncOrderByJob(ShopInfoEntity shopInfo);

    /**
     * 按时间拉取
     *
     * @param startDate
     * @param endDate
     * @param shopInfo
     * @return
     */
    int syncOrderByFetchTime(Date startDate, Date endDate, ShopInfoEntity shopInfo);

    /**
     * 按游标模式拉取
     *
     * @param startDate
     * @param endDate
     * @param shopInfo
     * @param getType
     * @return
     */
    Set<String> syncOrderByCursor(Date startDate, Date endDate, ShopInfoEntity shopInfo, IChannelConnector.GetType getType);

    /**
     * 按拉取时间分页拉取
     *
     * @param startDate
     * @param endDate
     * @param shopInfo
     * @param getType
     * @return
     */
    Set<String> syncOrderByPage(Date startDate, Date endDate, ShopInfoEntity shopInfo, IChannelConnector.GetType getType);

    /**
     * 获取拉取平台类型(为空表示不限制)
     *
     * @return
     */
    List<String> getSyncPlatformTypes();

    /**
     * 解析电商平台订单
     *
     * @param ecommerceOrder
     * @param shopInfoEntity
     * @return
     */
    EComLinkPlatformOrderAggregate parseOrder(ECommerceOrderModel ecommerceOrder, ShopInfoEntity shopInfoEntity);

    /**
     * 转换处理平台订单信息
     *
     * @param platformOrderAggregate
     * @param shopInfo
     * @return
     */
    List<Long> convertOrder(EComLinkPlatformOrderAggregate platformOrderAggregate, ShopInfoEntity shopInfo);
}
