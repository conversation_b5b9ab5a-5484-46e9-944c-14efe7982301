CREATE TABLE `yt_platform_order_consignee`
(
    `id`                     BIGINT        NOT NULL COMMENT '主键',
    `platform_order_id`      BIGINT        NOT NULL DEFAULT '0' COMMENT '平台订单id',
    `ext_order_id`           VARCHAR(48)   NOT NULL COMMENT '外部订单ID',
    `platform`               VARCHAR(32)   NOT NULL COMMENT '订单来源平台',
    `enterprise_id`          BIGINT        NOT NULL COMMENT '订单所属企业id',
    `supplier_enterprise_id` BIGINT                 DEFAULT NULL COMMENT '订单供应商企业id',
    `current_enterprise_id`  BIGINT        NOT NULL COMMENT '订单当前企业id(分销拆单时，当前企业id为拆分后的企业id)',
    `receiver_name`          VARCHAR(512)  NOT NULL COMMENT '收件人姓名',
    `receiver_country`       VARCHAR(64)            DEFAULT NULL COMMENT '收件人国家',
    `receiver_state`         VARCHAR(64)   NOT NULL COMMENT '收件人州/省',
    `receiver_city`          VARCHAR(64)   NOT NULL COMMENT '收件人市',
    `receiver_district`      VARCHAR(64)            DEFAULT NULL COMMENT '收件人区，县',
    `receiver_town`          VARCHAR(64)            DEFAULT NULL COMMENT '收件人街道，乡镇',
    `receiver_address`       VARCHAR(1024) NOT NULL COMMENT '收件人详细地址',
    `receiver_zip`           VARCHAR(12)            DEFAULT NULL COMMENT '收件人邮政编码',
    `receiver_mobile`        VARCHAR(512)           DEFAULT NULL COMMENT '收件人手机号',
    `receiver_phone`         VARCHAR(512)           DEFAULT NULL COMMENT '收件人手机号',
    `done_time`              DATETIME               DEFAULT NULL COMMENT '完成时间',
    `done_version`           BIGINT        NOT NULL COMMENT '操作版本(1:默认 4:记录换货操作 5:组合商品原记录)',
    `delete_flag`            TINYINT       NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`                DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id) USING BTREE,
    KEY `idx_platform_order_id` (`platform_order_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='平台订单收件人表';

CREATE TABLE `yt_platform_order_memo`
(
    `id`                     BIGINT      NOT NULL COMMENT '主键ID',
    `platform_order_id`      BIGINT      NOT NULL DEFAULT '0' COMMENT '平台订单id',
    `ext_order_id`           VARCHAR(48) NOT NULL COMMENT '外部订单ID',
    `platform`               VARCHAR(32) NOT NULL COMMENT '订单来源平台',
    `enterprise_id`          BIGINT      NOT NULL COMMENT '订单所属企业id',
    `supplier_enterprise_id` BIGINT               DEFAULT NULL COMMENT '订单供应商企业id',
    `current_enterprise_id`  BIGINT      NOT NULL COMMENT '订单当前企业id(分销拆单时，当前企业id为拆分后的企业id)',
    `order_memo`             VARCHAR(1024)        DEFAULT NULL COMMENT '订单备注',
    `buyer_message`          VARCHAR(1024)        DEFAULT NULL COMMENT '买家留言',
    `buyer_memo`             VARCHAR(2048)        DEFAULT NULL COMMENT '买家备注',
    `seller_message`         VARCHAR(1024)        DEFAULT NULL COMMENT '卖家留言',
    `seller_memo`            VARCHAR(1024)        DEFAULT NULL COMMENT '卖家备注',
    `seller_flag`            SMALLINT             DEFAULT NULL COMMENT '卖家备注旗帜,1、2、3、4、5分别对应红、黄、绿、蓝、紫',
    `delete_flag`            TINYINT     NOT NULL DEFAULT '0' COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`             BIGINT               DEFAULT NULL COMMENT '创建人',
    `creator_name`           VARCHAR(50)          DEFAULT NULL COMMENT '创建人名字',
    `modifier_id`            BIGINT               DEFAULT NULL COMMENT '修改操作人',
    `modifier_name`          VARCHAR(50)          DEFAULT NULL COMMENT '修改操作人名字',
    `created`                DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id) USING BTREE,
    KEY `idx_order_id` (`platform_order_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='平台订单备注表';
