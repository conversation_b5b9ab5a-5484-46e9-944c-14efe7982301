package com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抖店订单聚合对象
 * 包含抖店订单列表和详情的数据结构
 *
 * <AUTHOR>
 * @date 2025/7/29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FxgOrderAggregate extends ECommerceOrderModel {

    // ==================== 基础订单信息 ====================

    /**
     * 预约发货时间
     */
    @JsonProperty("appointment_ship_time")
    private String appointmentShipTime;

    /**
     * 买家留言
     */
    @JsonProperty("buyer_words")
    private String buyerWords;

    /**
     * 卖家留言
     */
    @JsonProperty("seller_words")
    private String sellerWords;

    /**
     * 卖家旗帜
     */
    @JsonProperty("seller_remark_stars")
    private Integer sellerRemarkStars;

    /**
     * 取消原因
     */
    @JsonProperty("cancel_reason")
    private String cancelReason;

    /**
     * 渠道支付单号
     */
    @JsonProperty("channel_payment_no")
    private String channelPaymentNo;

    /**
     * 下单时间，时间戳，秒
     */
    @JsonProperty("create_time")
    private Long createTime;

    /**
     * 抖店开放ID
     */
    @JsonProperty("doudian_open_id")
    private String doudianOpenId;

    /**
     * 期望发货时间
     */
    @JsonProperty("exp_ship_time")
    private String expShipTime;

    /**
     * 完成时间
     */
    @JsonProperty("finish_time")
    private Long finishTime;

    /**
     * 最晚收货时间
     */
    @JsonProperty("latest_receipt_time")
    private String latestReceiptTime;

    /**
     * 主状态
     */
    @JsonProperty("main_status")
    private String mainStatus;

    /**
     * 主状态描述
     */
    @JsonProperty("main_status_desc")
    private String mainStatusDesc;

    /**
     * 补邮费金额
     */
    @JsonProperty("make_up_post_amount")
    private String makeUpPostAmount;

    /**
     * 脱敏支付电话
     */
    @JsonProperty("mask_pay_tel")
    private String maskPayTel;

    /**
     * 脱敏收货人
     */
    @JsonProperty("mask_post_receiver")
    private String maskPostReceiver;

    /**
     * 脱敏收货电话
     */
    @JsonProperty("mask_post_tel")
    private String maskPostTel;

    /**
     * 改价金额
     */
    @JsonProperty("modify_amount")
    private String modifyAmount;

    /**
     * 改邮费金额
     */
    @JsonProperty("modify_post_amount")
    private String modifyPostAmount;

    /**
     * 订单优惠总金额（单位：分） = 店铺优惠金额 + 平台优惠金额 + 达人优惠金额
     */
    @JsonProperty("promotion_amount")
    private String promotionAmount;

    /**
     * 仅平台承担费用金额
     */
    @JsonProperty("only_platform_cost_amount")
    private String onlyPlatformCostAmount;

    /**
     * 开放ID
     */
    @JsonProperty("open_id")
    private String openId;

    /**
     * 订单金额
     */
    @JsonProperty("order_amount")
    private String orderAmount;

    /**
     * 订单过期时间
     */
    @JsonProperty("order_expire_time")
    private Long orderExpireTime;

    /**
     * 订单ID
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * 订单等级
     */
    @JsonProperty("order_level")
    private String orderLevel;

    /**
     * 订单状态
     */
    @JsonProperty("order_status")
    private String orderStatus;

    /**
     * 订单状态描述
     */
    @JsonProperty("order_status_desc")
    private String orderStatusDesc;

    /**
     * 【订单类型】 0、普通订单 2、虚拟商品订单 4、电子券（poi核销） 5、三方核销 6、服务市场
     */
    @JsonProperty("order_type")
    private Integer orderType;

    /**
     * 订单类型描述
     */
    @JsonProperty("order_type_desc")
    private String orderTypeDesc;

    /**
     * 原始店铺ID
     */
    @JsonProperty("original_shop_id")
    private String originalShopId;

    /**
     * 包装费
     */
    @JsonProperty("packing_amount")
    private String packingAmount;

    /**
     * 支付金额
     */
    @JsonProperty("pay_amount")
    private String payAmount;

    /**
     * 快递费（单位：分）
     */
    @JsonProperty("post_amount")
    private String postAmount;

    /**
     * 运费险金额（单位：分）
     */
    @JsonProperty("post_insurance_amount")
    private String postInsuranceAmount;

    /**
     * 支付时间，时间戳，秒
     */
    @JsonProperty("pay_time")
    private Long payTime;

    /**
     * 支付类型
     */
    @JsonProperty("pay_type")
    private String payType;

    /**
     * 收货日期
     */
    @JsonProperty("receipt_date")
    private String receiptDate;

    /**
     * 发货时间，时间戳，秒
     */
    @JsonProperty("ship_time")
    private Long shipTime;

    /**
     * 店铺承担费用金额
     */
    @JsonProperty("shop_cost_amount")
    private String shopCostAmount;

    /**
     * 店铺ID
     */
    @JsonProperty("shop_id")
    private String platformShopId;

    /**
     * 店铺名称
     */
    @JsonProperty("shop_name")
    private String shopName;

    /**
     * 目标到达时间
     */
    @JsonProperty("target_arrival_time")
    private String targetArrivalTime;

    /**
     * 税费金额
     */
    @JsonProperty("tax_amount")
    private String taxAmount;

    /**
     * 总优惠金额
     */
    @JsonProperty("total_promotion_amount")
    private String totalPromotionAmount;

    /**
     * 交易类型：0-普通，1-拼团，2-定金预售，3-订金找货，4-拍卖，5-0元单，6-回收，7-寄卖，10-寄样，11-0元抽奖(超级福袋)，12-达人买样，13-普通定制，16-大众竞拍，18-小时达，102-定金预售的赠品单，103-收款
     */
    @JsonProperty("trade_type")
    private String tradeType;

    /**
     * 交易类型描述
     */
    @JsonProperty("trade_type_desc")
    private String tradeTypeDesc;

    /**
     * 订单更新时间，时间戳，秒
     */
    @JsonProperty("update_time")
    private Long updateTime;

    /**
     * 催发货次数
     */
    @JsonProperty("urge_deliver_Times")
    private String urgeDeliverTimes;

    /**
     * 密文收件人电话
     */
    @JsonProperty("encrypt_post_tel")
    private String encryptPostTel;

    /**
     * 密文收件人名称
     */
    @JsonProperty("encrypt_post_receiver")
    private String encryptPostReceiver;

    // ==================== 额外对象字段 ====================

    private Map<String, Object> additionalProperties = new HashMap<>();


    // 添加动态属性
    @JsonAnySetter
    public void addAdditionalProperty(String key, Object value) {
        this.additionalProperties.put(key, value);
    }

    // 获取动态属性
    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    // ==================== 复杂对象字段 ====================

    /**
     * 物流信息
     */
    @JsonProperty("logistics_info")
    private List<LogisticsInfo> logisticsInfo;

    /**
     * SKU订单列表
     */
    @JsonProperty("sku_order_list")
    private List<SkuOrderList> skuOrderList;

    /**
     * 收件人地址
     */
    @JsonProperty("post_addr")
    private PostAddr postAddr;

    /**
     * 物流信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class LogisticsInfo {

        /**
         * 物流单号
         */
        @JsonProperty("tracking_no")
        private String trackingNo;

        /**
         * 物流公司名称
         */
        @JsonProperty("company_name")
        private String companyName;

        /**
         * 物流公司
         */
        @JsonProperty("company")
        private String company;

        /**
         * 发货时间，时间戳，秒
         */
        @JsonProperty("ship_time")
        private Long shipTime;

        // ==================== 额外对象字段 ====================

        private Map<String, Object> additionalProperties = new HashMap<>();


        // 添加动态属性
        @JsonAnySetter
        public void addAdditionalProperty(String key, Object value) {
            this.additionalProperties.put(key, value);
        }

        // 获取动态属性
        @JsonAnyGetter
        public Map<String, Object> getAdditionalProperties() {
            return this.additionalProperties;
        }
    }

    /**
     * SKU订单列表
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class SkuOrderList {
        /**
         * 商品ID
         */
        @JsonProperty("product_id")
        private String productId;

        /**
         * 商品名称
         */
        @JsonProperty("product_name")
        private String productName;

        /**
         * 商品图片
         */
        @JsonProperty("product_pic")
        private String productPic;

        /**
         * SKU ID
         */
        @JsonProperty("sku_id")
        private String skuId;

        /**
         * SKU订单ID
         */
        @JsonProperty("order_id")
        private String skuOrderId;

        /**
         * 商家后台商品编码
         */
        @JsonProperty("code")
        private String code;

        /**
         * 数量
         */
        @JsonProperty("item_num")
        private Integer itemNum;

        /**
         * 订单金额（单位：分）
         */
        @JsonProperty("order_amount")
        private Long orderAmount;

        /**
         * 支付金额（单位：分）
         */
        @JsonProperty("pay_amount")
        private Long payAmount;

        /**
         * 运费（单位：分）
         */
        @JsonProperty("post_amount")
        private Long postAmount;

        /**
         * 运费险金额（单位：分）
         */
        @JsonProperty("post_insurance_amount")
        private Long postInsuranceAmount;

        /**
         * 改价金额变化量（单位：分）
         */
        @JsonProperty("modify_amount")
        private Long modifyAmount;

        /**
         * 改价运费金额变化量（单位：分）
         */
        @JsonProperty("modify_post_amount")
        private Long modifyPostAmount;

        /**
         * 订单优惠总金额（单位：分） = 店铺优惠金额 + 平台优惠金额 + 达人优惠金额
         */
        @JsonProperty("promotion_amount")
        private Long promotionAmount;

        /**
         * 商品现价（单位：分）
         */
        @JsonProperty("origin_amount")
        private Long originAmount;

        /**
         * SKU规格
         */
        @JsonProperty("after_sale_info")
        private AfterSaleInfo afterSaleInfo;

        /**
         * 订单状态
         */
        @JsonProperty("order_status")
        private String orderStatus;

        /**
         * 主状态
         */
        @JsonProperty("main_status")
        private String mainStatus;

        /**
         * 规格信息
         */
        @JsonProperty("spec")
        private List<SkuSpec> spec;

        // ==================== 额外对象字段 ====================

        private Map<String, Object> additionalProperties = new HashMap<>();


        // 添加动态属性
        @JsonAnySetter
        public void addAdditionalProperty(String key, Object value) {
            this.additionalProperties.put(key, value);
        }

        // 获取动态属性
        @JsonAnyGetter
        public Map<String, Object> getAdditionalProperties() {
            return this.additionalProperties;
        }
    }

    /**
     * SKU规格
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class SkuSpec {
        /**
         * 规格名称
         */
        @JsonProperty("name")
        private String name;

        /**
         * 规格值
         */
        @JsonProperty("value")
        private String value;
    }

    /**
     * 售后信息
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class AfterSaleInfo {
        /**
         * 售后状态，0-售后初始化， 6-售后申请， 7-售后退货中， 27-拒绝售后申请， 12-售后成功， 28-售后失败， 11-售后已发货， 29-退货后拒绝退款， 13-售后换货商家发货， 14-售后换货用户收货， 51-取消成功， 53-逆向交易完成
         */
        @JsonProperty("after_sale_status")
        private Integer afterSaleStatus;

        /**
         * 售后类型:0 售后退货退款:1-售后退款 2-售前退款 3-换货 4-系统取消 5-用户取消
         */
        @JsonProperty("after_sale_type")
        private Integer afterSaleType;

        /**
         * 退款状态:1-待退款；3-退款成功； 4-退款失败；当买家发起售后后又主动取消售后，此时after_sale_status=28并且refund_status=1的状态不变，不会流转至4状态；
         */
        @JsonProperty("refund_status")
        private Integer refundStatus;
    }

    @Data
    public static class PostAddr {
        /**
         * 密文收件人地址
         */
        @JsonProperty("encrypt_detail")
        private String encryptDetail;

        /**
         * 省/直辖市
         */
        @JsonProperty("province")
        private Struct province;

        /**
         * 市/区
         */
        @JsonProperty("city")
        private Struct city;

        /**
         * 区县
         */
        @JsonProperty("town")
        private Struct town;

        /**
         * 街道
         */
        @JsonProperty("street")
        private Struct street;
    }

    @Data
    public static class Struct {
        /**
         * 地区ID
         */
        @JsonProperty("id")
        private String id;

        /**
         * 名称
         */
        @JsonProperty("name")
        private String name;
    }

}