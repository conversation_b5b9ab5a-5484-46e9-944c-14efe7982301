package com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小红书订单聚合对象
 * 包含小红书订单列表和详情的数据结构
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class XhsOrderAggregate extends ECommerceOrderModel {

    // ==================== 基础订单信息 ====================

    /**
     * 订单ID
     */
    @JsonProperty("orderId")
    private String orderId;

    /**
     * 订单类型，1现货 2定金预售 3全款预售(废弃) 4全款预售(新) 5补发
     */
    @JsonProperty("orderType")
    private Integer orderType;

    /**
     * 订单状态，1已下单待付款 2已支付处理中 3清关中 4待发货 5部分发货 6待收货 7已完成 8已关闭 9已取消 10换货申请中
     */
    @JsonProperty("orderStatus")
    private Integer orderStatus;

    /**
     * 售后状态，1无售后 2售后处理中 3售后完成 4售后拒绝 5售后关闭 6平台介入中 7售后取消
     */
    @JsonProperty("orderAfterSalesStatus")
    private Integer orderAfterSalesStatus;

    /**
     * 申请取消状态，0未申请取消 1取消处理中
     */
    @JsonProperty("cancelStatus")
    private Integer cancelStatus;

    /**
     * 创建时间 单位ms
     */
    @JsonProperty("createdTime")
    private Long createdTime;

    /**
     * 支付时间 单位ms
     */
    @JsonProperty("paidTime")
    private Long paidTime;

    /**
     * 更新时间 单位ms
     */
    @JsonProperty("updateTime")
    private Long updateTime;

    /**
     * 订单发货时间 单位ms
     */
    @JsonProperty("deliveryTime")
    private Long deliveryTime;

    /**
     * 订单取消时间 单位ms
     */
    @JsonProperty("cancelTime")
    private Long cancelTime;

    /**
     * 订单完成时间 单位ms
     */
    @JsonProperty("finishTime")
    private Long finishTime;

    /**
     * 承诺最晚发货时间 单位ms
     */
    @JsonProperty("promiseLastDeliveryTime")
    private Long promiseLastDeliveryTime;

    /**
     * 物流方案id
     */
    @JsonProperty("planInfoId")
    private String planInfoId;

    /**
     * 物流方案名称
     */
    @JsonProperty("planInfoName")
    private String planInfoName;

    /**
     * 商家标记优先级，ark订单列表展示旗子颜色 1灰旗 2红旗 3黄旗 4绿旗 5蓝旗 6紫旗
     */
    @JsonProperty("sellerRemarkFlag")
    private Integer sellerRemarkFlag;

    /**
     * 预售最早发货时间 单位ms
     */
    @JsonProperty("presaleDeliveryStartTime")
    private Long presaleDeliveryStartTime;

    /**
     * 预售最晚发货时间 单位ms
     */
    @JsonProperty("presaleDeliveryEndTime")
    private Long presaleDeliveryEndTime;

    /**
     * 收件人国家id
     */
    @JsonProperty("receiverCountryId")
    private String receiverCountryId;

    /**
     * 收件人国家名称
     */
    @JsonProperty("receiverCountryName")
    private String receiverCountryName;

    /**
     * 收件人省份id
     */
    @JsonProperty("receiverProvinceId")
    private String receiverProvinceId;

    /**
     * 收件人省份
     */
    @JsonProperty("receiverProvinceName")
    private String receiverProvinceName;

    /**
     * 收件人城市id
     */
    @JsonProperty("receiverCityId")
    private String receiverCityId;

    /**
     * 收件人城市
     */
    @JsonProperty("receiverCityName")
    private String receiverCityName;

    /**
     * 收件人区县id
     */
    @JsonProperty("receiverDistrictId")
    private String receiverDistrictId;

    /**
     * 收件人区县名称
     */
    @JsonProperty("receiverDistrictName")
    private String receiverDistrictName;

    /**
     * 订单商品总净重 单位g
     */
    @JsonProperty("totalNetWeightAmount")
    private Integer totalNetWeightAmount;

    /**
     * 订单实付金额(包含运费和定金) 单位分
     */
    @JsonProperty("totalPayAmount")
    private Integer totalPayAmount;

    /**
     * 订单实付运费 单位分
     */
    @JsonProperty("totalShippingFree")
    private Integer totalShippingFree;

    /**
     * 订单定金 单位分
     */
    @JsonProperty("totalDepositAmount")
    private Integer totalDepositAmount;

    /**
     * 商家承担总优惠金额 单位分
     */
    @JsonProperty("totalMerchantDiscount")
    private Integer totalMerchantDiscount;

    /**
     * 平台承担总优惠金额 单位分
     */
    @JsonProperty("totalRedDiscount")
    private Integer totalRedDiscount;

    /**
     * 商家实收(=用户支付金额+定金+平台优惠) 单位分
     */
    @JsonProperty("merchantActualReceiveAmount")
    private Integer merchantActualReceiveAmount;

    /**
     * 改价总金额 单位分
     */
    @JsonProperty("totalChangePriceAmount")
    private Integer totalChangePriceAmount;

    /**
     * 是否拆包 true已拆包 false未拆包
     */
    @JsonProperty("unpack")
    private Boolean unpack;

    /**
     * 物流模式 red_express三方备货直邮，red_domestic_trade三方备货内贸，red_standard三方备货保税仓，
     * red_auto三方自主发货，red_box三方小包，red_bonded三方保税
     */
    @JsonProperty("logistics")
    private String logistics;

    /**
     * 支付方式 1：支付宝 2：微信 3：apple 内购 4：apple pay 5：花呗分期 7：支付宝免密支付 8：云闪付 -1：其他
     */
    @JsonProperty("paymentType")
    private Integer paymentType;

    /**
     * 三方支付渠道单号
     */
    @JsonProperty("outTradeNo")
    private String outTradeNo;

    /**
     * 支付渠道优惠金额 单位分
     */
    @JsonProperty("outPromotionAmount")
    private Integer outPromotionAmount;

    /**
     * 店铺id
     */
    @JsonProperty("shopId")
    private String platformShopId;

    /**
     * 店铺名称
     */
    @JsonProperty("shopName")
    private String shopName;

    /**
     * 用户id
     */
    @JsonProperty("userId")
    private String userId;

    /**
     * 物流模式 1: 普通内贸 2：保税bbc 3: 直邮bc 4:行邮cc
     */
    @JsonProperty("logisticsMode")
    private Integer logisticsMode;

    /**
     * 收件人地址信息相关ID
     */
    @JsonProperty("openAddressId")
    private String openAddressId;

    /**
     * 原始ERP商店ID
     */
    @JsonProperty("iboss_shop_id")
    private Integer ibossShopId;

    /**
     * 原始平台ID
     */
    @JsonProperty("iboss_platform_id")
    private String ibossPlatformId;

    /**
     * 平台订单号
     */
    @JsonProperty("iboss_ext_order_id")
    private String ibossExtOrderId;

    /**
     * 同步时间
     */
    @JsonProperty("iboss_sync_time")
    private String ibossSyncTime;

    // ==================== 额外对象字段 ====================
    private Map<String, Object> additionalProperties = new HashMap<>();

    // 添加动态属性
    @JsonAnySetter
    public void addAdditionalProperty(String key, Object value) {
        this.additionalProperties.put(key, value);
    }

    // 获取动态属性
    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    // ==================== 复杂对象字段 ====================

    /**
     * sku列表
     */
    @JsonProperty("skuList")
    private List<SkuInfo> skuList;

    /**
     * 拆包信息节点
     */
    @JsonProperty("simpleDeliveryOrderList")
    private List<SimpleDeliveryOrder> simpleDeliveryOrderList;

    /**
     * 订单标签列表
     */
    @JsonProperty("orderTagList")
    private List<String> orderTagList;

    /**
     * 三方保税节点 金额单位 分
     */
    @JsonProperty("boundExtendInfo")
    private BoundExtendInfo boundExtendInfo;

    /**
     * 小包转运节点
     */
    @JsonProperty("transferExtendInfo")
    private TransferExtendInfo transferExtendInfo;

    /**
     * 收件人信息
     */
    @JsonProperty("iboss_ext_consignee")
    private ReceiverInfo receiverInfo;

    /**
     * 商品SKU信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SkuInfo {
        /**
         * 商品id
         */
        @JsonProperty("skuId")
        private String skuId;

        /**
         * 商品名称
         */
        @JsonProperty("skuName")
        private String skuName;

        /**
         * 商家编码
         */
        @JsonProperty("erpcode")
        private String erpcode;

        /**
         * 规格
         */
        @JsonProperty("skuSpec")
        private String skuSpec;

        /**
         * 商品图片url
         */
        @JsonProperty("skuImage")
        private String skuImage;

        /**
         * 商品数量
         */
        @JsonProperty("skuQuantity")
        private Integer skuQuantity;

        /**
         * 总支付金额（考虑总件数）商品总实付
         */
        @JsonProperty("totalPaidAmount")
        private Integer totalPaidAmount;

        /**
         * 商家承担总优惠
         */
        @JsonProperty("totalMerchantDiscount")
        private Integer totalMerchantDiscount;

        /**
         * 平台承担总优惠
         */
        @JsonProperty("totalRedDiscount")
        private Integer totalRedDiscount;

        /**
         * 商品税金
         */
        @JsonProperty("totalTaxAmount")
        private Integer totalTaxAmount;

        /**
         * 商品总净重
         */
        @JsonProperty("totalNetWeight")
        private Integer totalNetWeight;

        /**
         * 是否赠品，1 赠品 0 普通商品
         */
        @JsonProperty("skuTag")
        private Integer skuTag;

        /**
         * 是否是渠道商品
         */
        @JsonProperty("isChannel")
        private Boolean isChannel;

        /**
         * 是否支持无物流发货, 1: 支持无物流发货 0：不支持无物流发货
         */
        @JsonProperty("deliveryMode")
        private Integer deliveryMode;

        /**
         * 达人id
         */
        @JsonProperty("kolId")
        private String kolId;

        /**
         * Sku售后状态
         */
        @JsonProperty("skuAfterSaleStatus")
        private Integer skuAfterSaleStatus;

        /**
         * 商品ID
         */
        @JsonProperty("itemId")
        private String itemId;

        /**
         * 商品名称
         */
        @JsonProperty("itemName")
        private String itemName;

        /**
         * 商品sku信息列表
         */
        @JsonProperty("skuDetailList")
        private List<SkuDetail> skuDetailList;

        /**
         * 额外属性
         */
        private Map<String, Object> additionalProperties = new HashMap<>();

        @JsonAnySetter
        public void addAdditionalProperty(String key, Object value) {
            this.additionalProperties.put(key, value);
        }

        @JsonAnyGetter
        public Map<String, Object> getAdditionalProperties() {
            return this.additionalProperties;
        }
    }

    /**
     * 商品sku详细信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SkuDetail {
        /**
         * 单品商品Id
         */
        @JsonProperty("skuId")
        private String skuId;

        /**
         * 商家编码
         */
        @JsonProperty("erpCode")
        private String erpCode;

        /**
         * 商品条码
         */
        @JsonProperty("barcode")
        private String barcode;

        /**
         * 小红书编码
         */
        @JsonProperty("scSkuCode")
        private String scSkuCode;

        /**
         * 购买数量
         */
        @JsonProperty("quantity")
        private Integer quantity;

        /**
         * 商品名
         */
        @JsonProperty("skuName")
        private String skuName;

        /**
         * 单个sku价格（不含税）
         */
        @JsonProperty("pricePerSku")
        private Integer pricePerSku;

        /**
         * 单个sku税金
         */
        @JsonProperty("taxPerSku")
        private Integer taxPerSku;

        /**
         * 单个sku实付
         */
        @JsonProperty("paidAmountPerSku")
        private Integer paidAmountPerSku;

        /**
         * 单个sku定金
         */
        @JsonProperty("depositAmountPerSku")
        private Integer depositAmountPerSku;

        /**
         * 单个sku商家承担优惠
         */
        @JsonProperty("merchantDiscountPerSku")
        private Integer merchantDiscountPerSku;

        /**
         * 单个sku平台承担优惠
         */
        @JsonProperty("redDiscountPerSku")
        private Integer redDiscountPerSku;

        /**
         * 单个sku原价
         */
        @JsonProperty("rawPricePerSku")
        private Integer rawPricePerSku;
    }

    /**
     * 拆包信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SimpleDeliveryOrder {
        /**
         * 发货订单索引标识
         */
        @JsonProperty("deliveryOrderIndex")
        private Integer deliveryOrderIndex;

        /**
         * 发货订单状态 1:已下单待付款 2:已支付处理中 3:清关中 4:待发货 6:待收货 7:已完成 8:已关闭 9:已取消 10:换货申请中
         */
        @JsonProperty("status")
        private Integer status;

        /**
         * 拆包快递单号
         */
        @JsonProperty("expressTrackingNo")
        private String expressTrackingNo;

        /**
         * 快递公司代码
         */
        @JsonProperty("expressCompanyCode")
        private String expressCompanyCode;

        /**
         * 商品ID列表
         */
        @JsonProperty("itemIdList")
        private List<String> itemIdList;

        /**
         * SKU ID列表 此发货订单中有哪些商品，status=4待发货时，列表中的sku可以拆包发货。status=6时，列表中的sku共享相同的快递公司和单号，修改时一起修改
         */
        @JsonProperty("skuIdList")
        private List<String> skuIdList;
    }

    /**
     * 三方保税节点
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BoundExtendInfo {
        /**
         * 交易流水号
         */
        @JsonProperty("payNo")
        private String payNo;

        /**
         * 交易渠道，AliPay=支付宝，TP=微信
         */
        @JsonProperty("payChannel")
        private String payChannel;

        /**
         * 订单价值（货值，订单商品申价之和（税前价））
         */
        @JsonProperty("productValue")
        private Integer productValue;

        /**
         * 订单支付金额（含运费）
         */
        @JsonProperty("payAmount")
        private Integer payAmount;

        /**
         * 订单税金
         */
        @JsonProperty("taxAmount")
        private Integer taxAmount;

        /**
         * 运费 含运费税
         */
        @JsonProperty("shippingFee")
        private Integer shippingFee;

        /**
         * 订单优惠
         */
        @JsonProperty("discountAmount")
        private Integer discountAmount;

        /**
         * 海关三级地址区域编码
         */
        @JsonProperty("zoneCodes")
        private List<String> zoneCodes;
    }

    /**
     * 小包转运节点
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TransferExtendInfo {
        /**
         * 国际快递单号
         */
        @JsonProperty("internationalExpressNo")
        private String internationalExpressNo;

        /**
         * 订单申报金额
         */
        @JsonProperty("orderDeclaredAmount")
        private Integer orderDeclaredAmount;

        /**
         * 大头笔
         */
        @JsonProperty("paintMarker")
        private String paintMarker;

        /**
         * 集包地
         */
        @JsonProperty("collectionPlace")
        private String collectionPlace;

        /**
         * 三段码
         */
        @JsonProperty("threeSegmentCode")
        private String threeSegmentCode;
    }

    /**
     * 收件人信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReceiverInfo {
        /**
         * 订单ID
         */
        @JsonProperty("orderId")
        private String orderId;

        /**
         * 收件人姓名
         */
        @JsonProperty("receiverName")
        private String receiverName;

        /**
         * 收件人电话
         */
        @JsonProperty("receiverPhone")
        private String receiverPhone;

        /**
         * 收件人地址
         */
        @JsonProperty("receiverAddress")
        private String receiverAddress;

        /**
         * 收件人省份
         */
        @JsonProperty("receiverProvinceName")
        private String receiverProvinceName;

        /**
         * 收件人城市
         */
        @JsonProperty("receiverCityName")
        private String receiverCityName;

        /**
         * 收件人区县
         */
        @JsonProperty("receiverDistrictName")
        private String receiverDistrictName;

        /**
         * 收件人街道/镇
         */
        @JsonProperty("receiverTownName")
        private String receiverTownName;

        /**
         * 是否匹配
         */
        @JsonProperty("matched")
        private Boolean matched;
    }
}
