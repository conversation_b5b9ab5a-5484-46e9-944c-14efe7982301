package com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 视频号小商店订单聚合类
 * 对应微信小店API: https://developers.weixin.qq.com/doc/store/shop/API/order/get.html
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SphxdOrderAggregate extends ECommerceOrderModel {

    /**
     * 秒级时间戳
     */
    @JsonProperty("create_time")
    private Long createTime;

    /**
     * 秒级时间戳
     */
    @JsonProperty("update_time")
    private Long updateTime;

    /**
     * 订单ID
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * 订单状态，具体枚举值请参考OrderStatus枚举
     * 10：待付款
     * 12：礼物待收下
     * 13：凑单买凑团中
     * 20：待发货
     * 21：部分发货
     * 30：待收货
     * 100：完成
     * 200：全部商品售后之后，订单取消
     * 250：未付款用户主动取消或超时未付款订单自动取消
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 订单归属人身份标识，在自购场景为支付者（买家），在礼物场景为收礼者
     */
    @JsonProperty("openid")
    private String openid;

    /**
     * 订单归属人在开放平台的唯一标识符，在自购场景为支付者（买家），在礼物场景为收礼者，
     * 若当前微信小店已绑定到微信开放平台账号下，绑定成功后产生的订单会返回
     */
    @JsonProperty("unionid")
    private String unionid;

    /**
     * 订单详细数据信息
     */
    @JsonProperty("order_detail")
    private OrderDetail orderDetail;

    /**
     * 售后信息
     */
    @JsonProperty("aftersale_detail")
    private AfterSaleDetail aftersaleDetail;

    /**
     * 是否礼物订单
     */
    @JsonProperty("is_present")
    private Boolean isPresent;

    /**
     * 礼物单类型，具体枚举值请参考PresentSendType枚举
     * 0：普通单聊
     * 1：普通群聊
     * 2：公众号/服务号送礼
     * 3：视频号送礼
     * 4：企业微信
     * 5：小程序
     * 6：小程序/小游戏送礼
     */
    @JsonProperty("present_send_type")
    private Integer presentSendType;

    /**
     * 礼物订单ID(已废弃)
     */
    @JsonProperty("present_order_id")
    private String presentOrderId;

    /**
     * 礼物订单ID
     */
    @JsonProperty("present_order_id_str")
    private String presentOrderIdStr;

    /**
     * 礼物订单留言
     */
    @JsonProperty("present_note")
    private String presentNote;

    /**
     * 礼物订单赠送者openid,仅送礼订单返回该字段
     */
    @JsonProperty("present_giver_openid")
    private String presentGiverOpenid;

    /**
     * 礼物订单赠送者在开放平台的唯一标识符，
     * 若当前微信小店已绑定到微信开放平台账号下，绑定成功后产生的订单会返回
     */
    @JsonProperty("present_giver_unionid")
    private String presentGiverUnionid;

    /**
     * 订单详细数据信息
     * 参考微信小店文档 OrderDetail 结构体
     */
    @Data
    public static class OrderDetail {
        /**
         * 商品列表
         */
        @JsonProperty("product_infos")
        private List<ProductInfo> productInfos;

        /**
         * 价格信息
         */
        @JsonProperty("price_info")
        private PriceInfo priceInfo;

        /**
         * 支付信息
         */
        @JsonProperty("pay_info")
        private PayInfo payInfo;

        /**
         * 配送信息
         */
        @JsonProperty("delivery_info")
        private DeliveryInfo deliveryInfo;

        /**
         * 优惠券信息
         */
        @JsonProperty("coupon_info")
        private CouponInfo couponInfo;

        /**
         * 额外信息
         */
        @JsonProperty("ext_info")
        private ExtInfo extInfo;

        /**
         * 分佣信息
         */
        @JsonProperty("commission_infos")
        private List<CommissionInfo> commissionInfos;

        /**
         * 分享员信息
         */
        @JsonProperty("sharer_info")
        private SharerInfo sharerInfo;

        /**
         * 结算信息
         */
        @JsonProperty("settle_info")
        private SettleInfo settleInfo;

        /**
         * 分享员信息
         */
        @JsonProperty("sku_sharer_infos")
        private List<SkuSharerInfo> skuSharerInfos;

        /**
         * 授权账号信息
         */
        @JsonProperty("agent_info")
        private AgentInfo agentInfo;

        /**
         * 订单来源信息
         */
        @JsonProperty("source_infos")
        private List<SourceInfo> sourceInfos;

        /**
         * 订单退款信息
         */
        @JsonProperty("refund_info")
        private RefundInfo refundInfo;

        /**
         * 需代写的商品贺卡信息
         */
        @JsonProperty("greeting_card_info")
        private GreetingCardInfo greetingCardInfo;

        /**
         * 商品定制信息
         */
        @JsonProperty("custom_info")
        private CustomInfo customInfo;
    }

    /**
     * 商品列表
     * 参考微信小店文档 ProductInfo 结构体
     */
    @Data
    public static class ProductInfo {
        /**
         * 商品spuid
         */
        @JsonProperty("product_id")
        private String productId;

        /**
         * 商品skuid
         */
        @JsonProperty("sku_id")
        private String skuId;

        /**
         * sku小图
         */
        @JsonProperty("thumb_img")
        private String thumbImg;

        /**
         * sku数量
         */
        @JsonProperty("sku_cnt")
        private Integer skuCnt;

        /**
         * 售卖单价，单位为分
         */
        @JsonProperty("sale_price")
        private Integer salePrice;

        /**
         * 商品标题
         */
        @JsonProperty("title")
        private String title;

        /**
         * 正在售后/退款流程中的 sku 数量
         */
        @JsonProperty("on_aftersale_sku_cnt")
        private Integer onAftersaleSkuCnt;

        /**
         * 完成售后/退款的 sku 数量
         */
        @JsonProperty("finish_aftersale_sku_cnt")
        private Integer finishAftersaleSkuCnt;

        /**
         * 商品编码
         */
        @JsonProperty("sku_code")
        private String skuCode;

        /**
         * 市场单价，单位为分
         */
        @JsonProperty("market_price")
        private Integer marketPrice;

        /**
         * sku属性
         */
        @JsonProperty("sku_attrs")
        private List<AttrInfo> skuAttrs;

        /**
         * sku实付总价，取estimate_price和change_price中较小值
         */
        @JsonProperty("real_price")
        private Integer realPrice;

        /**
         * 商品外部spuid
         */
        @JsonProperty("out_product_id")
        private String outProductId;

        /**
         * 商品外部skuid
         */
        @JsonProperty("out_sku_id")
        private String outSkuId;

        /**
         * 是否有商家优惠金额，非必填，默认为false
         */
        @JsonProperty("is_discounted")
        private Boolean isDiscounted;

        /**
         * 使用所有优惠后sku总价，非必填，有优惠金额时必填
         */
        @JsonProperty("estimate_price")
        private Integer estimatePrice;

        /**
         * 是否修改过价格，非必填，默认为false
         */
        @JsonProperty("is_change_price")
        private Boolean isChangePrice;

        /**
         * 改价后sku总价，非必填，is_change_price为true时有值
         */
        @JsonProperty("change_price")
        private Integer changePrice;

        /**
         * 区域库存id
         */
        @JsonProperty("out_warehouse_id")
        private String outWarehouseId;

        /**
         * 商品发货信息
         */
        @JsonProperty("sku_deliver_info")
        private SkuDeliverInfo skuDeliverInfo;

        /**
         * 商品额外服务信息
         */
        @JsonProperty("extra_service")
        private ProductExtraService extraService;

        /**
         * 是否使用了会员积分抵扣
         */
        @JsonProperty("use_deduction")
        private Boolean useDeduction;

        /**
         * 会员积分抵扣金额，单位为分
         */
        @JsonProperty("deduction_price")
        private Integer deductionPrice;

        /**
         * 商品优惠券信息
         */
        @JsonProperty("order_product_coupon_info_list")
        private List<OrderProductCouponInfo> orderProductCouponInfoList;

        /**
         * 商品发货时效，超时此时间未发货即为发货超时
         */
        @JsonProperty("delivery_deadline")
        private Integer deliveryDeadline;

        /**
         * 商家优惠金额，单位为分
         */
        @JsonProperty("merchant_discounted_price")
        private Integer merchantDiscountedPrice;

        /**
         * 达人优惠金额，单位为分
         */
        @JsonProperty("finder_discounted_price")
        private Integer finderDiscountedPrice;

        /**
         * 是否赠品，非必填，赠品商品返回，1:是赠品
         */
        @JsonProperty("is_free_gift")
        private Integer isFreeGift;

        /**
         * 赠品信息
         */
        @JsonProperty("free_gift_info")
        private OrderProductFreeGiftInfo freeGiftInfo;

        /**
         * 商品常量编号，订单内商品唯一标识，下单后不会发生变化
         */
        @JsonProperty("product_unique_id")
        private String productUniqueId;

        /**
         * 更换sku信息
         */
        @JsonProperty("change_sku_info")
        private ChangeSkuInfo changeSkuInfo;

        /**
         * 订单内商品维度会员权益优惠金额
         */
        @JsonProperty("vip_discounted_price")
        private Integer vipDiscountedPrice;

        /**
         * 订单内商品维度一起买优惠金额
         */
        @JsonProperty("bulkbuy_discounted_price")
        private Integer bulkbuyDiscountedPrice;
    }

    /**
     * 商品发货信息
     * 参考微信小店文档 SkuDeliverInfo 结构体
     */
    @Data
    public static class SkuDeliverInfo {
        /**
         * 商品发货类型：0：现货，1：全款预售
         */
        @JsonProperty("stock_type")
        private Integer stockType;

        /**
         * 预计发货时间(stock_type=1时返回该字段)
         */
        @JsonProperty("predict_delivery_time")
        private Integer predictDeliveryTime;

        /**
         * 预售类型(stock_type=1时返回该字段)，枚举值请参考PresaleDeliveryType枚举
         * 0：付款后n天发货
         * 1：预售结束后n天发货
         */
        @JsonProperty("full_payment_presale_delivery_type")
        private Integer fullPaymentPresaleDeliveryType;
    }

    /**
     * 商品额外服务信息
     * 参考微信小店文档 ProductExtraService 结构体
     */
    @Data
    public static class ProductExtraService {
        /**
         * 7天无理由：0：不支持，1：支持
         */
        @JsonProperty("seven_day_return")
        private Integer sevenDayReturn;

        /**
         * 商家运费险：0：不支持，1：支持
         */
        @JsonProperty("freight_insurance")
        private Integer freightInsurance;
    }

    /**
     * 商品优惠券信息
     * 参考微信小店文档 OrderProductCouponInfo 结构体
     */
    @Data
    public static class OrderProductCouponInfo {
        /**
         * 用户优惠券id
         */
        @JsonProperty("user_coupon_id")
        private String userCouponId;

        /**
         * 优惠券类型，枚举值请参考OrderCouponType枚举
         * 1：商家优惠
         * 2：达人优惠
         * 3：平台优惠
         */
        @JsonProperty("coupon_type")
        private Integer couponType;

        /**
         * 优惠金额，单位为分，该张优惠券、抵扣该商品的金额
         */
        @JsonProperty("discounted_price")
        private Integer discountedPrice;

        /**
         * 优惠券id
         */
        @JsonProperty("coupon_id")
        private String couponId;
    }

    /**
     * 属性信息
     * 参考微信小店文档 AttrInfo 结构体
     */
    @Data
    public static class AttrInfo {
        /**
         * 属性键（属性自定义用）
         */
        @JsonProperty("attr_key")
        private String attrKey;

        /**
         * 属性值value（属性自定义用）
         */
        @JsonProperty("attr_value")
        private String attrValue;
    }

    /**
     * 支付信息
     * 参考微信小店文档 PayInfo 结构体
     */
    @Data
    public static class PayInfo {
        /**
         * 预支付id
         */
        @JsonProperty("prepay_id")
        private String prepayId;

        /**
         * 预支付时间，秒级时间戳
         */
        @JsonProperty("prepay_time")
        private Integer prepayTime;

        /**
         * 支付时间，秒级时间戳
         */
        @JsonProperty("pay_time")
        private Integer payTime;

        /**
         * 支付订单号
         */
        @JsonProperty("transaction_id")
        private String transactionId;

        /**
         * 支付方式，已支付订单会返回本字段，具体枚举值请参考PaymentMethod枚举
         * 1：微信支付
         * 2：先用后付；未入库已取消
         * 3：入库异常
         * 4：已入库
         * 5：质检中
         * 6：待出库
         * 7：出库异常
         * 8：待自提
         * 10：已取消已自提
         * 11：已收货
         * 12：待重新送检
         * 13：已达送检上限
         * 14：待驿站入库
         */
        @JsonProperty("payment_method")
        private Integer paymentMethod;
    }

    /**
     * 价格信息
     * 参考微信小店文档 PriceInfo 结构体
     */
    @Data
    public static class PriceInfo {
        /**
         * 商品总价，单位为分
         */
        @JsonProperty("product_price")
        private Integer productPrice;

        /**
         * 用户实付金额，单位为分
         */
        @JsonProperty("order_price")
        private Integer orderPrice;

        /**
         * 运费，单位为分
         */
        @JsonProperty("freight")
        private Integer freight;

        /**
         * 商家优惠金额，单位为分，非必填，有商家优惠时返回
         */
        @JsonProperty("discounted_price")
        private Integer discountedPrice;

        /**
         * 是否有商家优惠券优惠
         */
        @JsonProperty("is_discounted")
        private Boolean isDiscounted;

        /**
         * 订单原始价格，单位为分
         */
        @JsonProperty("original_order_price")
        private Integer originalOrderPrice;

        /**
         * 商品预估价格，单位为分
         */
        @JsonProperty("estimate_product_price")
        private Integer estimateProductPrice;

        /**
         * 改价后降低金额，单位为分
         */
        @JsonProperty("change_down_price")
        private Integer changeDownPrice;

        /**
         * 改价后运费，单位为分
         */
        @JsonProperty("change_freight")
        private Integer changeFreight;

        /**
         * 是否修改运费
         */
        @JsonProperty("is_change_freight")
        private Boolean isChangeFreight;

        /**
         * 是否使用了会员积分抵扣
         */
        @JsonProperty("use_deduction")
        private Boolean useDeduction;

        /**
         * 会员积分抵扣金额，单位为分
         */
        @JsonProperty("deduction_price")
        private Integer deductionPrice;

        /**
         * 商家实收金额，单位为分
         */
        @JsonProperty("merchant_receieve_price")
        private Integer merchantReceievePrice;

        /**
         * 商家优惠金额，单位为分，含义同discounted_price，必填
         */
        @JsonProperty("merchant_discounted_price")
        private Integer merchantDiscountedPrice;

        /**
         * 达人优惠金额，单位为分
         */
        @JsonProperty("finder_discounted_price")
        private Integer finderDiscountedPrice;

        /**
         * 订单维度会员权益优惠金额
         */
        @JsonProperty("vip_discounted_price")
        private Integer vipDiscountedPrice;
    }

    /**
     * 配送信息
     * 参考微信小店文档 DeliveryInfo 结构体
     */
    @Data
    public static class DeliveryInfo {
        /**
         * 地址信息
         */
        @JsonProperty("address_info")
        private AddressInfo addressInfo;

        /**
         * 发货物流信息
         */
        @JsonProperty("delivery_product_info")
        private List<DeliveryProductInfo> deliveryProductInfo;

        /**
         * 发货完成时间，秒级时间戳
         */
        @JsonProperty("ship_done_time")
        private Integer shipDoneTime;

        /**
         * 订单发货方式，0：普通物流，1/3：虚拟发货，由商品的同名字段决定
         */
        @JsonProperty("deliver_method")
        private Integer deliverMethod;

        /**
         * 用户下单后申请修改收货地址，商家同意后该字段会覆盖订单地址信息
         */
        @JsonProperty("address_under_review")
        private AddressInfo addressUnderReview;

        /**
         * 修改地址申请时间，秒级时间戳
         */
        @JsonProperty("address_apply_time")
        private Integer addressApplyTime;

        /**
         * 电子面单代发时的订单密文
         */
        @JsonProperty("ewaybill_order_code")
        private String ewaybillOrderCode;

        /**
         * 订单质检类型 2：生鲜类质检； 1：珠宝玉石类质检； 0：不需要； 不传递本字段表示不需要
         */
        @JsonProperty("quality_inspect_type")
        private Integer qualityInspectType;

        /**
         * 质检信息，quality_inspect_type>0时返回
         */
        @JsonProperty("quality_inspect_info")
        private QualityInsepctInfo qualityInspectInfo;

        /**
         * 虚拟商品充值账户信息
         */
        @JsonProperty("recharge_info")
        private RechargeInfo rechargeInfo;
    }

    /**
     * 发货物流信息
     * 参考微信小店文档 DeliveryProductInfo 结构体
     */
    @Data
    public static class DeliveryProductInfo {
        /**
         * 快递单号
         */
        @JsonProperty("waybill_id")
        private String waybillId;

        /**
         * 快递公司编码
         */
        @JsonProperty("delivery_id")
        private String deliveryId;

        /**
         * 包裹中商品信息
         */
        @JsonProperty("product_infos")
        private List<FreightProductInfo> productInfos;

        /**
         * 快递公司名称
         */
        @JsonProperty("delivery_name")
        private String deliveryName;

        /**
         * 发货时间，秒级时间戳
         */
        @JsonProperty("delivery_time")
        private Integer deliveryTime;

        /**
         * 配送方式，具体枚举值请参考DeliveryType枚举
         * 1：自寄快递
         * 2：在线签约快递单（已废弃）
         * 3：虚拟商品无需物流发货
         * 4：在线快递散单（已废弃）
         */
        @JsonProperty("deliver_type")
        private Integer deliverType;

        /**
         * 发货地址
         */
        @JsonProperty("delivery_address")
        private AddressInfo deliveryAddress;
    }

    /**
     * 虚拟商品充值账户信息
     * 参考微信小店文档 RechargeInfo 结构体
     */
    @Data
    public static class RechargeInfo {
        /**
         * 虚拟商品充值账号，当account_type=qq或phone_number或mail的时候返回
         */
        @JsonProperty("account_no")
        private String accountNo;

        /**
         * 账号充值类型，可选项: weixin(微信号),qq(qq),phone_number(电话号码),mail(邮箱)
         */
        @JsonProperty("account_type")
        private String accountType;

        /**
         * 当account_type="weixin"的时候返回
         */
        @JsonProperty("wx_openid")
        private String wxOpenid;
    }

    /**
     * 包裹中商品信息
     * 参考微信小店文档 FreightProductInfo 结构体
     */
    @Data
    public static class FreightProductInfo {
        /**
         * 商品id
         */
        @JsonProperty("product_id")
        private String productId;

        /**
         * sku_id
         */
        @JsonProperty("sku_id")
        private String skuId;

        /**
         * 商品数量
         */
        @JsonProperty("product_cnt")
        private Integer productCnt;
    }

    /**
     * 地址信息
     * 参考微信小店文档 AddressInfo 结构体
     */
    @Data
    public static class AddressInfo {
        /**
         * 收货人姓名
         */
        @JsonProperty("user_name")
        private String userName;

        /**
         * 邮编
         */
        @JsonProperty("postal_code")
        private String postalCode;

        /**
         * 省份
         */
        @JsonProperty("province_name")
        private String provinceName;

        /**
         * 城市
         */
        @JsonProperty("city_name")
        private String cityName;

        /**
         * 区
         */
        @JsonProperty("county_name")
        private String countyName;

        /**
         * 详细地址
         */
        @JsonProperty("detail_info")
        private String detailInfo;

        /**
         * 国家码，已废弃，请勿使用
         */
        @JsonProperty("national_code")
        private String nationalCode;

        /**
         * 联系方式
         */
        @JsonProperty("tel_number")
        private String telNumber;

        /**
         * 门牌号码
         */
        @JsonProperty("house_number")
        private String houseNumber;

        /**
         * 虚拟发货订单联系方式(deliver_method=1时返回)
         */
        @JsonProperty("virtual_order_tel_number")
        private String virtualOrderTelNumber;

        /**
         * 额外的联系方式信息（虚拟号码相关）
         */
        @JsonProperty("tel_number_ext_info")
        private TelNumberExtInfo telNumberExtInfo;

        /**
         * 0：不使用虚拟号码，1：使用虚拟号码
         */
        @JsonProperty("use_tel_number")
        private Integer useTelNumber;

        /**
         * 标识当前店铺下一个唯一的用户收货地址
         */
        @JsonProperty("hash_code")
        private String hashCode;
    }

    /**
     * 联系方式信息
     * 参考微信小店文档 TelNumberExtInfo 结构体
     */
    @Data
    public static class TelNumberExtInfo {
        /**
         * 脱敏手机号
         */
        @JsonProperty("real_tel_number")
        private String realTelNumber;

        /**
         * 完整的虚拟号码
         */
        @JsonProperty("virtual_tel_number")
        private String virtualTelNumber;

        /**
         * 主动兑换的虚拟号码过期时间
         */
        @JsonProperty("virtual_tel_expire_time")
        private Integer virtualTelExpireTime;

        /**
         * 主动兑换虚拟号码次数
         */
        @JsonProperty("get_virtual_tel_cnt")
        private Integer getVirtualTelCnt;
    }

    /**
     * 优惠券信息
     * 参考微信小店文档 CouponInfo 结构体
     */
    @Data
    public static class CouponInfo {
        /**
         * 用户优惠券id
         */
        @JsonProperty("user_coupon_id")
        private String userCouponId;
    }

    /**
     * 售后信息
     * 参考微信小店文档 AfterSaleDetail 结构体
     */
    @Data
    public static class AfterSaleDetail {
        /**
         * 正在售后流程的售后单数
         */
        @JsonProperty("on_aftersale_order_cnt")
        private Integer onAftersaleOrderCnt;

        /**
         * 售后单列表
         */
        @JsonProperty("aftersale_order_list")
        private List<AfterSaleOrderInfo> aftersaleOrderList;
    }

    /**
     * 售后单列表
     * 参考微信小店文档 AfterSaleOrderInfo 结构体
     */
    @Data
    public static class AfterSaleOrderInfo {
        /**
         * 售后单ID
         */
        @JsonProperty("aftersale_order_id")
        private Integer aftersaleOrderId;

        /**
         * 售后单状态(已废弃，请勿使用，售后信息请调用售后接口）
         */
        @JsonProperty("status")
        private Integer status;
    }

    /**
     * 额外信息
     * 参考微信小店文档 ExtInfo 结构体
     */
    @Data
    public static class ExtInfo {
        /**
         * 用户备注
         */
        @JsonProperty("customer_notes")
        private String customerNotes;

        /**
         * 商家备注
         */
        @JsonProperty("merchant_notes")
        private String merchantNotes;

        /**
         * 确认收货时间，包括用户主动确认收货和超时自动确认收货
         */
        @JsonProperty("confirm_receipt_time")
        private Integer confirmReceiptTime;

        /**
         * 视频号id
         */
        @JsonProperty("finder_id")
        private String finderId;

        /**
         * 直播id
         */
        @JsonProperty("live_id")
        private String liveId;

        /**
         * 下单场景，枚举值见OrderScene
         * 1：其他
         * 2：直播间
         * 3：短视频
         * 4：商品分享
         * 5：商品橱窗主页
         * 6：公众号文章商品卡片
         */
        @JsonProperty("order_scene")
        private Integer orderScene;

        /**
         * 会员权益-session_id
         */
        @JsonProperty("vip_order_session_id")
        private String vipOrderSessionId;

        /**
         * 用于判断分佣单是否已生成【0：未生成 1:已生成】
         */
        @JsonProperty("commission_handling_progress")
        private Integer commissionHandlingProgress;
    }

    /**
     * 分佣信息
     * 参考微信小店文档 CommissionInfo 结构体
     */
    @Data
    public static class CommissionInfo {
        /**
         * 商品skuid
         */
        @JsonProperty("sku_id")
        private String skuId;

        /**
         * 分账方昵称
         */
        @JsonProperty("nickname")
        private String nickname;

        /**
         * 分账方类型，0：达人，1：带货机构
         */
        @JsonProperty("type")
        private Integer type;

        /**
         * 分账状态， 1：未结算，2：已结算
         */
        @JsonProperty("status")
        private Integer status;

        /**
         * 分账金额
         */
        @JsonProperty("amount")
        private Integer amount;

        /**
         * 达人视频号id
         */
        @JsonProperty("finder_id")
        private String finderId;

        /**
         * 达人openfinderid
         */
        @JsonProperty("openfinderid")
        private String openfinderid;

        /**
         * 新带货平台 id
         */
        @JsonProperty("talent_id")
        private String talentId;

        /**
         * 带货机构 id
         */
        @JsonProperty("agency_id")
        private String agencyId;
    }

    /**
     * 分享员信息
     * 参考微信小店文档 SharerInfo 结构体
     */
    @Data
    public static class SharerInfo {
        /**
         * 分享员openid
         */
        @JsonProperty("sharer_openid")
        private String sharerOpenid;

        /**
         * 分享员unionid
         */
        @JsonProperty("sharer_unionid")
        private String sharerUnionid;

        /**
         * 分享员类型，0：普通分享员，1：店铺分享员
         */
        @JsonProperty("sharer_type")
        private Integer sharerType;

        /**
         * 分享场景，具体枚举值请参考ShareScene枚举
         * 1：直播间
         * 2：橱窗
         * 3：短视频
         * 4：视频号主页
         * 5：商品详情页
         * 6：带商品的公众号文章
         * 7：商品链接
         * 8：商品二维码
         * 9：商品短链
         * 10：分享直播间
         * 11：分享预约直播间
         * 12：视频号橱窗的短链
         * 13：视频号橱窗的二维码
         */
        @JsonProperty("share_scene")
        private Integer shareScene;

        /**
         * 分享员数据是否已经解析完成【1:解析完成 0:解析中】
         */
        @JsonProperty("handling_progress")
        private Integer handlingProgress;
    }

    /**
     * sku分享员信息
     * 参考微信小店文档 SkuSharerInfo 结构体
     */
    @Data
    public static class SkuSharerInfo {
        /**
         * 分享员openid
         */
        @JsonProperty("sharer_openid")
        private String sharerOpenid;

        /**
         * 分享员unionid
         */
        @JsonProperty("sharer_unionid")
        private String sharerUnionid;

        /**
         * 分享员类型，0：普通分享员，1：店铺分享员
         */
        @JsonProperty("sharer_type")
        private Integer sharerType;

        /**
         * 分享场景，具体枚举值请参考ShareScene枚举
         */
        @JsonProperty("share_scene")
        private Integer shareScene;

        /**
         * 商品skuid
         */
        @JsonProperty("sku_id")
        private String skuId;

        /**
         * 是否来自企微分享
         */
        @JsonProperty("from_wecom")
        private Boolean fromWecom;
    }

    /**
     * 结算信息
     * 参考微信小店文档 SettleInfo 结构体
     */
    @Data
    public static class SettleInfo {
        /**
         * 预计技术服务费，单位为分
         */
        @JsonProperty("predict_commission_fee")
        private Integer predictCommissionFee;

        /**
         * 实际技术服务费，单位为分（未结算时本字段为空）
         */
        @JsonProperty("commission_fee")
        private Integer commissionFee;

        /**
         * 预计人气卡返佣金额，单位为分（未发起结算时本字段为空）
         */
        @JsonProperty("predict_wecoin_commission")
        private Integer predictWecoinCommission;

        /**
         * 实际人气卡返佣金额，单位为分（未结算时本字段为空）
         */
        @JsonProperty("wecoin_commission")
        private Integer wecoinCommission;

        /**
         * 商家结算时间
         */
        @JsonProperty("settle_time")
        private Integer settleTime;
    }

    /**
     * 授权账号信息
     * 参考微信小店文档 AgentInfo 结构体
     */
    @Data
    public static class AgentInfo {
        /**
         * 授权视频号id
         */
        @JsonProperty("agent_finder_id")
        private String agentFinderId;

        /**
         * 授权视频号昵称
         */
        @JsonProperty("agent_finder_nickname")
        private String agentFinderNickname;
    }

    /**
     * 订单成交来源信息
     * 参考微信小店文档 SourceInfo 结构体
     */
    @Data
    public static class SourceInfo {
        /**
         * 商品skuid
         */
        @JsonProperty("sku_id")
        private String skuId;

        /**
         * 账号关联类型，0：关联账号，1：合作账号，2：授权号，100：达人带货，101：带货机构推广，102：其他
         */
        @JsonProperty("sale_channel")
        private Integer saleChannel;

        /**
         * 带货账号类型，1：视频号，2：公众号，3：小程序，4：企业微信，5：带货达人，6：服务号，1000：带货机构
         */
        @JsonProperty("account_type")
        private Integer accountType;

        /**
         * 带货账号id，取决于带货账号类型
         */
        @JsonProperty("account_id")
        private String accountId;

        /**
         * 带货账号昵称
         */
        @JsonProperty("account_nickname")
        private String accountNickname;

        /**
         * 带货内容类型，1：企微成员转发
         */
        @JsonProperty("content_type")
        private Integer contentType;

        /**
         * 带货内容id，取决于带货内容类型（企微成员user_id）
         */
        @JsonProperty("content_id")
        private String contentId;

        /**
         * 自营推客推广的带货机构id
         */
        @JsonProperty("promoter_head_supplier_id")
        private String promoterHeadSupplierId;

        /**
         * 公众号/服务号 id，仅在account_type=2/6的情况下返回
         */
        @JsonProperty("original_id")
        private String originalId;
    }

    /**
     * 订单退款信息
     * 参考微信小店文档 RefundInfo 结构体
     */
    @Data
    public static class RefundInfo {
        /**
         * 退还运费金额，礼物订单(is_present=true)可能存在
         */
        @JsonProperty("refund_freight")
        private Integer refundFreight;
    }

    /**
     * 质检信息
     * 参考微信小店文档 QualityInsepctInfo 结构体
     */
    @Data
    public static class QualityInsepctInfo {
        /**
         * 质检状态 珠宝玉石类质检请参考InspectStatus枚举； 生鲜类质检请参考FreshInspectStatus枚举；
         * 100：待上传打包信息
         * 200：预检中
         * 201：预检不通过
         * 202：预检通过
         */
        @JsonProperty("inspect_status")
        private Integer inspectStatus;
    }

    /**
     * 需代写的商品贺卡信息
     * 参考微信小店文档 GreetingCardInfo 结构体
     */
    @Data
    public static class GreetingCardInfo {
        /**
         * 贺卡落款，用户选填
         */
        @JsonProperty("giver_name")
        private String giverName;

        /**
         * 贺卡称谓，用户选填
         */
        @JsonProperty("receiver_name")
        private String receiverName;

        /**
         * 贺卡内容，用户必填
         */
        @JsonProperty("greeting_message")
        private String greetingMessage;
    }

    /**
     * 商品定制信息
     * 参考微信小店文档 CustomInfo 结构体
     */
    @Data
    public static class CustomInfo {
        /**
         * 定制图片，custom_type=2时返回
         */
        @JsonProperty("custom_img_url")
        private String customImgUrl;

        /**
         * 定制文字，custom_type=1时返回
         */
        @JsonProperty("custom_word")
        private String customWord;

        /**
         * 定制类型，枚举值请参考CustomType枚举
         * 1：文字定制
         * 2：图片定制
         */
        @JsonProperty("custom_type")
        private Integer customType;

        /**
         * 定制预览图片，开启了定制预览时返回
         */
        @JsonProperty("custom_preview_img_url")
        private String customPreviewImgUrl;
    }

    /**
     * 赠品信息
     * 参考微信小店文档 OrderProductFreeGiftInfo 结构体
     */
    @Data
    public static class OrderProductFreeGiftInfo {
        /**
         * 赠品对应的主品信息
         */
        @JsonProperty("main_product_list")
        private List<MainProductInfo> mainProductList;
    }

    /**
     * 主品信息
     * 参考微信小店文档 MainProductInfo 结构体
     */
    @Data
    public static class MainProductInfo {
        /**
         * 赠品数量
         */
        @JsonProperty("gift_cnt")
        private Integer giftCnt;

        /**
         * 活动id
         */
        @JsonProperty("task_id")
        private String taskId;

        /**
         * 商品id
         */
        @JsonProperty("product_id")
        private String productId;

        /**
         * 主品sku_id
         */
        @JsonProperty("sku_id")
        private String skuId;
    }

    /**
     * 更换sku信息
     * 参考微信小店文档 ChangeSkuInfo 结构体
     */
    @Data
    public static class ChangeSkuInfo {
        /**
         * 发货前更换sku状态。3：等待商家处理，4：商家审核通过，5：商家拒绝，6：用户主动取消，7：超时默认拒绝
         */
        @JsonProperty("preshipment_change_sku_state")
        private Integer preshipmentChangeSkuState;

        /**
         * 原sku_id
         */
        @JsonProperty("old_sku_id")
        private Integer oldSkuId;

        /**
         * 用户申请更换的sku_id
         */
        @JsonProperty("new_sku_id")
        private Integer newSkuId;

        /**
         * 商家处理请求的最后时间，只有当前换款请求处于"等待商家处理"才有值
         */
        @JsonProperty("ddl_time_stamp")
        private Integer ddlTimeStamp;
    }
}