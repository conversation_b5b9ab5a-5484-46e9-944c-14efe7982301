package com.yaotown.ecommate.infra.module.mapper.account;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yaotown.ecommate.infra.module.enums.AccountStatusEnum;
import com.yaotown.ecommate.common.core.enums.AccountTypeEnum;
import com.yaotown.ecommate.common.core.enums.DeleteFlagEnum;
import com.yaotown.ecommate.infra.module.pojo.dto.enterprise.account.AccountRespDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.platform.account.SupplierAccountPageReqDTO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.AccountPO;
import com.yaotown.ecommate.infra.module.pojo.vo.UserPageReqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface AccountMapper extends BaseMapper<AccountPO> {

    /**
     * 查询企业端账户手机是否存在
     * @param mobile 手机号
     * @return
     */
    default AccountPO selectByMobile(String mobile){
        LambdaQueryWrapper<AccountPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountPO::getMobile, mobile);
        queryWrapper.eq(AccountPO::getAccountType, AccountTypeEnum.ENTERPRISE_ACCOUNT.getId());
        queryWrapper.eq(AccountPO::getDeleteFlag, DeleteFlagEnum.UN_DELETE.getId());
        queryWrapper.eq(AccountPO::getStatus, AccountStatusEnum.NORMAL.getId()); // 只查询状态为正常的账户
        return selectOne(queryWrapper);
    }

    default AccountPO selectByAccountId(Long accountId) {
        LambdaQueryWrapper<AccountPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountPO::getId, accountId);
        queryWrapper.eq(AccountPO::getDeleteFlag, DeleteFlagEnum.UN_DELETE.getId());
        queryWrapper.eq(AccountPO::getStatus, AccountStatusEnum.NORMAL.getId());
        return selectOne(queryWrapper);
    }

    default AccountPO selectByAdminMobile(String username){
        LambdaQueryWrapper<AccountPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountPO::getUsername, username);
        queryWrapper.eq(AccountPO::getAccountType, AccountTypeEnum.PLATFORM_ACCOUNT.getId());
        queryWrapper.eq(AccountPO::getDeleteFlag, DeleteFlagEnum.UN_DELETE.getId());
        queryWrapper.eq(AccountPO::getStatus, AccountStatusEnum.NORMAL.getId()); // 只查询状态为正常的账户
        return selectOne(queryWrapper);
    }

//    default AccountPO selectByUsername(String username) {
//        LambdaQueryWrapper<AccountPO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(AccountPO::getUsername, username);
//        queryWrapper.eq(AccountPO::getDeleteFlag, DeleteFlagEnum.UN_DELETE.getId());
//        return selectOne(queryWrapper);
//    }
    default AccountPO selectByEmail(String email) {
        LambdaQueryWrapper<AccountPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountPO::getEmail, email);
        queryWrapper.eq(AccountPO::getDeleteFlag, DeleteFlagEnum.UN_DELETE.getId());
        return selectOne(queryWrapper);
    }


    default List<AccountPO> selectListByStatus(Integer status) {
        LambdaQueryWrapper<AccountPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountPO::getStatus, status);
        return selectList(queryWrapper);
    }

    List<AccountRespDTO> selectPageAccount(@Param("param") UserPageReqVO param,@Param("deptIds") Collection<Long> deptIds,@Param("userIds") Collection<Long> userIds);

    List<AccountPO> selectPage(@Param("offset") int offset, @Param("pageSize") int pageSize);

    default AccountPO selectByAccountTypeMobile(String mobile, Integer accountType){
        LambdaQueryWrapper<AccountPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountPO::getMobile, mobile);
        queryWrapper.eq(AccountPO::getAccountType,accountType);
        queryWrapper.eq(AccountPO::getDeleteFlag, DeleteFlagEnum.UN_DELETE.getId());
        return selectOne(queryWrapper);
    }
    
    /**
     * 分页查询供应商账户
     * 
     * @param param 查询条件
     * @return 账户列表
     */
    List<AccountPO> selectSupplierAccountPage(@Param("param") SupplierAccountPageReqDTO param);
}