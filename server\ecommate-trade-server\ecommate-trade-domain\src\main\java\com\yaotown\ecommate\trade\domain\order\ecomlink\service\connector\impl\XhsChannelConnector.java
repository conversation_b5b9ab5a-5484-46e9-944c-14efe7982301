package com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.common.base.core.util.CommonStreamUtil;
import com.yaotown.ecommate.common.core.entity.KeyValue;
import com.yaotown.ecommate.common.core.util.json.JsonUtils;
import com.yaotown.ecommate.trade.domain.common.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.KwaiShopOrderAggregate;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.XhsOrderAggregate;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.*;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.EComLinkPlatformOrderEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.EComLinkPlatformOrderItemEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.EComLinkPlatformOrderLogisticsEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.ErpOriginalPlatformOrderEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.ErpOrderInfoSearchVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.AbstractChannelConnector;
import com.yaotown.ecommate.trade.types.enums.erp.ErpPlatformTypeEnum;
import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderShippingTypeEnum;
import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderStatusEnum;
import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderWarrantyStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 小红书渠道连接器
 *
 * <AUTHOR>
 * @date 2025/6/12
 */
@Component
public class XhsChannelConnector extends AbstractChannelConnector<XhsOrderAggregate> {

    @Override
    public String getPlatform() {
        return ErpPlatformTypeEnum.XHS.getValue();
    }

    @Override
    public Integer getOrderCount(Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        ErpOrderInfoSearchVO erpOrderInfoSearchVO = new ErpOrderInfoSearchVO()
                .setXytShopId(shopInfo.getXytShopId())
                .setStartTime(beginDate)
                .setEndTime(endDate);
        PageData<ErpOriginalPlatformOrderEntity> pageData = erpECommerceOrderRepository.selectOriginalOrderByPage(new QueryModel<>(1, 1, erpOrderInfoSearchVO));
        return (int) pageData.getPageTotal();
    }

    @Override
    public List<XhsOrderAggregate> getOrderPage(int pageIndex, int pageSize, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        ErpOrderInfoSearchVO erpOrderInfoSearchVO = new ErpOrderInfoSearchVO()
                .setXytShopId(shopInfo.getXytShopId())
                .setStartTime(beginDate)
                .setEndTime(endDate);
        String now = DateUtil.now();
        PageData<ErpOriginalPlatformOrderEntity> pageData = erpECommerceOrderRepository.selectOriginalOrderByPage(new QueryModel<>(pageIndex, pageSize, erpOrderInfoSearchVO));

        if (CollUtil.isEmpty(pageData.getPageContents())) {
            return Collections.emptyList();
        }
        return CommonStreamUtil.transList(pageData.getPageContents(), (originalOrder) -> {
            XhsOrderAggregate xhsOrderAggregate = JsonUtils.parseObject(originalOrder.getContent(), XhsOrderAggregate.class);
            xhsOrderAggregate.setEcommerceOrderId(originalOrder.getExtOrderId());
            xhsOrderAggregate.setPlatform(getPlatform());
            xhsOrderAggregate.setPlatformOrderId(originalOrder.getId());
            xhsOrderAggregate.setEnterpriseId(shopInfo.getEnterpriseId());
            xhsOrderAggregate.setShopId(shopInfo.getShopId());
            xhsOrderAggregate.setSyncTime(now);
            return xhsOrderAggregate;
        });
    }

    @Override
    public KeyValue<String, List<XhsOrderAggregate>> getOrderPageByCursor(String cursor, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        return new KeyValue<>(cursor, List.of());
    }

    @Override
    public EComLinkPlatformOrderEntity parsePlatformOrder(XhsOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        if (ecommerceOrder == null) {
            return null;
        }

        Date createdTime = ecommerceOrder.getCreatedTime() != null ? new Date(ecommerceOrder.getCreatedTime()) : null;
        Date paidTime = ecommerceOrder.getPaidTime() != null ? new Date(ecommerceOrder.getPaidTime()) : null;
        Date updateTime = ecommerceOrder.getUpdateTime() != null ? new Date(ecommerceOrder.getUpdateTime()) : null;
        Date deliveryTime = ecommerceOrder.getDeliveryTime() != null ? new Date(ecommerceOrder.getDeliveryTime()) : null;
        Date finishTime = ecommerceOrder.getFinishTime() != null ? new Date(ecommerceOrder.getFinishTime()) : null;

        return EComLinkPlatformOrderEntity.builder()
                .platformOrderId(ecommerceOrder.getPlatformOrderId())
                .platform(ecommerceOrder.getPlatform())
                .enterpriseId(ecommerceOrder.getEnterpriseId())
                .shopId(ecommerceOrder.getShopId())
                .extOrderId(ecommerceOrder.getOrderId())
                .exShopId(ecommerceOrder.getPlatformShopId()) // 平台店铺ID
                .buyerId(ecommerceOrder.getUserId()) // 买家ID
                .payType(String.valueOf(ecommerceOrder.getPaymentType())) // 支付类型
                .paymentTradeId(ecommerceOrder.getOutTradeNo()) // 支付交易号
                .paidFee(Long.valueOf(ecommerceOrder.getTotalPayAmount()))
                .totalFee(Long.valueOf(ecommerceOrder.getTotalPayAmount())) // 订单总金额
                .postFee(Long.valueOf(ecommerceOrder.getTotalShippingFree())) // 运费
                .adjustFee(Long.valueOf(ecommerceOrder.getTotalChangePriceAmount())) // 调价金额
                .discountFee((long) (ecommerceOrder.getTotalRedDiscount() + ecommerceOrder.getTotalMerchantDiscount() + ecommerceOrder.getOutPromotionAmount())) // 总优惠金额
                .createTime(createdTime) // 创建时间
                .payTime(paidTime) // 支付时间
                .updateTime(updateTime) // 更新时间
                .deliveryTime(deliveryTime) // 发货时间
                .doneTime(finishTime) // 完成时间
                .orderStatus(this.parseOrderStatus(ecommerceOrder).getValue()) // 订单状态
                .warrantyStatus(this.parseWarrantyStatus(ecommerceOrder).getValue()) // 售后状态
                .shippingType(this.parseShippingType(ecommerceOrder).getValue()) // 物流类型
                .build();
    }

    /**
     * 解析订单物流类型
     */
    private PlatformOrderShippingTypeEnum parseShippingType(XhsOrderAggregate ecommerceOrder) {
        if (ecommerceOrder.getSkuList() != null && !ecommerceOrder.getSkuList().isEmpty()) {
            // 检查是否有无物流发货的SKU
            for (XhsOrderAggregate.SkuInfo skuInfo : ecommerceOrder.getSkuList()) {
                if (skuInfo.getDeliveryMode() != null && skuInfo.getDeliveryMode() == 1) {
                    return PlatformOrderShippingTypeEnum.virtual;
                }
            }
        }
        // 默认返回物流发货
        return PlatformOrderShippingTypeEnum.express;
    }

    /**
     * 售后状态
     */
    private PlatformOrderWarrantyStatusEnum parseWarrantyStatus(XhsOrderAggregate ecommerceOrder) {
        if (ecommerceOrder.getOrderAfterSalesStatus() == null) {
            return PlatformOrderWarrantyStatusEnum.NONE;
        }

        switch (ecommerceOrder.getOrderAfterSalesStatus()) {
            case 2: // 售后处理中
            case 6: // 平台介入中
                return PlatformOrderWarrantyStatusEnum.REFUNDING;
            case 3: // 售后完成
                return PlatformOrderWarrantyStatusEnum.REFUND_SUCCESS;
            case 4: // 售后拒绝
            case 5: // 售后关闭
                return PlatformOrderWarrantyStatusEnum.REFUND_CANCEL;
            default:
                return PlatformOrderWarrantyStatusEnum.NONE;
        }
    }

    /**
     * 订单状态
     */
    private PlatformOrderStatusEnum parseOrderStatus(XhsOrderAggregate ecommerceOrder) {
        if (ecommerceOrder.getOrderStatus() == null) {
            return PlatformOrderStatusEnum.PENDING;
        }

        switch (ecommerceOrder.getOrderStatus()) {
            case 2: // 已支付处理中
            case 3: // 清关中
                return PlatformOrderStatusEnum.PAID;
            case 4: // 待发货
            case 5: // 部分发货
                return PlatformOrderStatusEnum.TOBESHIPPED;
            case 6: // 待收货
                return PlatformOrderStatusEnum.DELIVERY;
            case 7: // 已完成
                return PlatformOrderStatusEnum.SIGNED;
            case 8: // 已关闭
            case 9: // 已取消
                return PlatformOrderStatusEnum.CANCEL;
            default:
                return PlatformOrderStatusEnum.PENDING;
        }
    }

    /**
     * 解析单个商品售后状态
     */
    private PlatformOrderWarrantyStatusEnum parseItemWarrantyStatus(XhsOrderAggregate.SkuInfo skuInfo) {
        if (skuInfo.getSkuAfterSaleStatus() == null) {
            return PlatformOrderWarrantyStatusEnum.NONE;
        }

        switch (skuInfo.getSkuAfterSaleStatus()) {
            case 2: // 售后处理中
            case 6: // 平台介入中
                return PlatformOrderWarrantyStatusEnum.REFUNDING;
            case 3: // 售后完成
                return PlatformOrderWarrantyStatusEnum.REFUND_SUCCESS;
            case 4: // 售后拒绝
            case 5: // 售后关闭
                return PlatformOrderWarrantyStatusEnum.REFUND_CANCEL;
            default:
                return PlatformOrderWarrantyStatusEnum.NONE;
        }
    }

    @Override
    public List<EComLinkPlatformOrderItemEntity> parsePlatformOrderItems(XhsOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        if (ecommerceOrder == null || CollUtil.isEmpty(ecommerceOrder.getSkuList())) {
            return Collections.emptyList();
        }

        List<EComLinkPlatformOrderItemEntity> itemEntityList = new ArrayList<>();

        for (int i = 0; i < ecommerceOrder.getSkuList().size(); i++) {
            XhsOrderAggregate.SkuInfo skuInfo = ecommerceOrder.getSkuList().get(i);

            // 创建时间
            Date createTime = ecommerceOrder.getCreatedTime() != null ? new Date(ecommerceOrder.getCreatedTime()) : null;
            Date doneTime = ecommerceOrder.getFinishTime() != null ? new Date(ecommerceOrder.getFinishTime()) : null;

            EComLinkPlatformOrderItemEntity itemEntity = EComLinkPlatformOrderItemEntity.builder()
                    .extSkuTitle(skuInfo.getSkuName())
                    .extNumIid(skuInfo.getSkuId())
                    .extSkuId(skuInfo.getSkuId())
                    .num(skuInfo.getSkuQuantity())
                    .price(Long.valueOf(skuInfo.getTotalPaidAmount()))
                    .skuSpecChars(skuInfo.getSkuSpec())
                    .imageUrl(skuInfo.getSkuImage())
                    .platformOrderId(ecommerceOrder.getPlatformOrderId())
                    .extOrderId(ecommerceOrder.getOrderId())
                    .platform(ecommerceOrder.getPlatform())
                    .enterpriseId(ecommerceOrder.getEnterpriseId())
                    .totalFee(Long.valueOf(skuInfo.getTotalPaidAmount()))
                    .discountFee((long) (skuInfo.getTotalMerchantDiscount() + skuInfo.getTotalRedDiscount()))
                    .paidFee(Long.valueOf(skuInfo.getTotalPaidAmount()))
                    .itemStatus(this.parseOrderStatus(ecommerceOrder).getValue())
                    .warrantyStatus(this.parseItemWarrantyStatus(skuInfo).getValue())
                    .createTime(createTime)
                    .doneTime(doneTime)
                    .build();

            List<XhsOrderAggregate.SkuDetail> skuDetailList = skuInfo.getSkuDetailList();
            if (CollUtil.isNotEmpty(skuDetailList)) {
                for (XhsOrderAggregate.SkuDetail skuDetail : skuDetailList) {
                    if (!skuDetail.getSkuId().equals(itemEntity.getExtNumIid())) {
                        break;
                    } else {
                        itemEntity.setPrice(Long.valueOf(skuDetail.getPricePerSku()));
                        itemEntity.setExtOrderId(skuDetail.getBarcode());
                    }
                }
            }

            itemEntity.setExtItemId(String.format("%s_%s_%d",
                    itemEntity.getExtNumIid(),
                    itemEntity.getExtSkuId(),
                    i
            ));
            itemEntityList.add(itemEntity);
        }


        return itemEntityList;
    }

    @Override
    public List<EComLinkPlatformOrderLogisticsEntity> parsePlatformOrderLogistics(XhsOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        if (ecommerceOrder == null || CollUtil.isEmpty(ecommerceOrder.getSimpleDeliveryOrderList())) {
            return Collections.emptyList();
        }

        List<EComLinkPlatformOrderLogisticsEntity> logisticsList = new ArrayList<>();

        Date deliveryTime = ecommerceOrder.getDeliveryTime() != null ? new Date(ecommerceOrder.getDeliveryTime()) : null;
        Date doneTime = ecommerceOrder.getFinishTime() != null ? new Date(ecommerceOrder.getFinishTime()) : null;

        for (XhsOrderAggregate.SimpleDeliveryOrder deliveryOrder : ecommerceOrder.getSimpleDeliveryOrderList()) {
            if (StringUtils.isBlank(deliveryOrder.getExpressTrackingNo())) {
                continue;
            }

            // 创建物流记录
            EComLinkPlatformOrderLogisticsEntity logisticsEntity = EComLinkPlatformOrderLogisticsEntity.builder()
                    .enterpriseId(ecommerceOrder.getEnterpriseId())
                    .platformOrderId(ecommerceOrder.getPlatformOrderId())
                    .companyCode(deliveryOrder.getExpressCompanyCode())
                    .deliveryNo(deliveryOrder.getExpressTrackingNo())
                    .deliveryTime(deliveryTime)
                    .doneTime(doneTime)
                    .state(1) // 记录状态: 1-有效
                    .build();

            logisticsList.add(logisticsEntity);
        }
        return logisticsList;
    }

    @Override
    public EComLinkPlatformOrderMemoEntity parsePlatformOrderMemo(XhsOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return new EComLinkPlatformOrderMemoEntity();
    }

    @Override
    public EComLinkPlatformOrderConsigneeEntity parsePlatformOrderConsignee(XhsOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return null;
    }
}
