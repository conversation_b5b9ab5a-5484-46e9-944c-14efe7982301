package com.yaotown.ecommate.infra.module.controller.supplier;

import cn.hutool.core.collection.CollUtil;
import com.anji.captcha.service.CaptchaService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yaotown.common.base.core.entity.R;
import com.yaotown.ecommate.common.core.entity.CurrentAccount;
import com.yaotown.ecommate.common.core.enums.AccountTypeEnum;
import com.yaotown.ecommate.common.core.enums.BooleanEnum;
import com.yaotown.ecommate.common.core.enums.PlatformTypeEnum;
import com.yaotown.ecommate.common.security.core.util.SecurityUtils;
import com.yaotown.ecommate.common.web.apilog.core.annotation.ApiAccessLog;
import com.yaotown.ecommate.infra.module.convert.AuthConvert;
import com.yaotown.ecommate.infra.module.enums.EnterpriseAccountTypeEnum;
import com.yaotown.ecommate.infra.module.mapper.RoleMapper;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SmsCodeCheckDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SupplierSmsCodeRequestDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.enterprise.account.LoginReqDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.enterprise.account.LoginRespDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.enterprise.account.RegisterReqDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.enterprise.account.SupplierRegisterReqDTO;
import com.yaotown.ecommate.infra.module.pojo.entity.EnterprisePO;
import com.yaotown.ecommate.infra.module.pojo.entity.MenuPO;
import com.yaotown.ecommate.infra.module.pojo.entity.RolePO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.AccountEnterprisePO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.AccountPO;
import com.yaotown.ecommate.infra.module.pojo.vo.AuthPermissionInfoRespVO;
import com.yaotown.ecommate.infra.module.service.*;
import com.yaotown.ecommate.infra.module.service.message.SmsCodeSendService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 供应商控制器
 * @apiFolder 供应商端/基础支撑服务/供应商管理
 * @classPrefixPath /v1/supplier/
 */
@RestController
@RequestMapping("/infra/supplier")
@RequiredArgsConstructor
@Slf4j
public class SupplierController {

    private final AccountService accountService;
    private final MenuService menuService;
    private final EnterpriseService enterpriseService;
    private final EnterpriseVersionService enterpriseVersionService;
    private final VersionService versionService;
    private final AccountEnterpriseService accountEnterpriseService;
    private final PermissionService permissionService;
    private final RoleMapper roleMapper;
    private final SmsCodeSendService smsCodeSendService;
    @Resource
    private CaptchaService captchaService;
    /**
     * 供应商注册
     *
     * @param supplierRegisterReqDTO 注册信息
     * @return 注册结果
     */
    @PostMapping("/register")
    @ApiAccessLog(sanitizeKeys = {"loginPassword", "accessToken"})
    public R<LoginRespDTO> register(@Validated @RequestBody SupplierRegisterReqDTO supplierRegisterReqDTO) {
        //
        RegisterReqDTO registerReqDTO =new RegisterReqDTO();
        registerReqDTO.setAccount(supplierRegisterReqDTO.getAccount());
        registerReqDTO.setEnterprise(supplierRegisterReqDTO.getEnterprise());
        //验证码
        registerReqDTO.setCode("123456");
        registerReqDTO.setSmsId("CAPTCHA_VERIFIED");
        
        // 执行注册
        LoginRespDTO loginRespDTO = versionService.register(registerReqDTO);
        
        // 注册成功后，绑定供应商管理员角色
        if (loginRespDTO != null && loginRespDTO.getId() != null) {
            // 1. 查询供应商管理员角色
            QueryWrapper<RolePO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", "supplier_admin")
                      .eq("platform_type", PlatformTypeEnum.SUPPLIER_PLATFORM.getId());
            RolePO adminRole = roleMapper.selectOne(queryWrapper);
            
            // 2. 如果找到角色，则分配给用户
            if (adminRole != null) {
                Set<Long> roleIds = new HashSet<>();
                roleIds.add(adminRole.getId());
                permissionService.assignUserRole(loginRespDTO.getId(), roleIds);
                log.info("成功为用户{}分配供应商管理员角色", loginRespDTO.getId());
            } else {
                log.warn("未找到供应商管理员角色(supplier_admin)，无法为新用户分配角色");
            }
        }
        
        return R.success(loginRespDTO);
    }

    /**
     * 供应商登录
     *
     * @param loginReqDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    @ApiAccessLog(sanitizeKeys = {"loginPassword", "accessToken"})
    public R<LoginRespDTO> login(@RequestBody @Validated LoginReqDTO loginReqDTO) {
        // 执行登录
        LoginRespDTO loginRespDTO = accountService.login(loginReqDTO, AccountTypeEnum.SUPPLIER_ACCOUNT.getId());
        
        // 检查账户是否需要验证手机号(供应商子账户第一次登录时)
        if (loginRespDTO != null && loginRespDTO.getId() != null) {
            Boolean needVerify = accountService.needMobileVerify(loginRespDTO.getId());
            if (Boolean.TRUE.equals(needVerify)) {
                // 如果需要验证，在返回对象中设置标志位
                loginRespDTO.setNeedMobileVerify(BooleanEnum.TRUE.getId());
            }
        }
        
        return R.success(loginRespDTO);
    }
    /**
     * 短信验证码发送
     *
     * @param req
     * @return
     */
    @PostMapping("/send")
    @ApiAccessLog
    public R<String> sendSmsCodeMessage(@RequestBody @Validated SupplierSmsCodeRequestDTO req, HttpServletRequest request) {
        return R.success(smsCodeSendService.supplierSmsCodeSend(req));
    }

    /**
     * sms code 验证 （供应商）
     *
     * @param smsCodeCheck
     * @return
     */
    @PostMapping("/code_verify")
    @ApiAccessLog
    public R<Boolean> verifySmsCode(@RequestBody @Validated SmsCodeCheckDTO smsCodeCheck) {
        smsCodeSendService.codeVerify(smsCodeCheck, false);
        return R.success();
    }
    
    /**
     * 供应商登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    public R<Boolean> logout() {
        accountService.logout();
        return R.success();
    }
    
    /**
     * 获得登录供应商的权限信息
     * @return 权限信息响应数据
     */
    @GetMapping("/get-permission-info")
    public R<AuthPermissionInfoRespVO> getPermissionInfo() {
        // 1.1 获得用户信息
        CurrentAccount account = SecurityUtils.getLoginAccount();
        if (account == null || account.getAccountId() == null) {
            return R.success(new AuthPermissionInfoRespVO());
        }
        
        AccountPO user = accountService.getUser(account.getAccountId());
        if (user == null) {
            return R.success(new AuthPermissionInfoRespVO());
        }
        
        // 1.2 获取企业信息
        EnterprisePO enterprise = enterpriseService.getTenant(account.getEnterpriseId());
        if (enterprise == null) {
            return R.success(new AuthPermissionInfoRespVO());
        }
        //如果该企业是子账户  获取该账户对应的角色
        AccountEnterprisePO accountEnterprisePO = accountEnterpriseService.getAccountEnterpriseAccountId(account.getAccountId(), account.getEnterpriseId());
        if (accountEnterprisePO.getEnterpriseAccountType().equals(EnterpriseAccountTypeEnum.ENTERPRISE_SUB_ACCOUNT.getCode())){
            Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(accountEnterprisePO.getAccountId());
            if (CollUtil.isEmpty(roleIds)) {
                return R.success(new AuthPermissionInfoRespVO());
            }
            // 根据角色ID获取菜单列表
            Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(roleIds, PlatformTypeEnum.SUPPLIER_PLATFORM.getId());
            // 1.4 获取菜单列表
            List<MenuPO> menuList = menuService.getMenuList(menuIds);
            menuList = menuService.filterDisableMenus(menuList);
            return R.success(AuthConvert.INSTANCE.convert(user, Collections.emptyList(), menuList));
        }
        // 1.3 直接从企业菜单表获取菜单IDS
        List<Long> menuIdList = enterpriseVersionService.getEnterpriseMenuIds(account.getEnterpriseId());
        if (menuIdList.isEmpty()) {
            return R.success(new AuthPermissionInfoRespVO());
        }
        
        // 转换为Set类型
        Set<Long> menuIds = new HashSet<>(menuIdList);
        
        // 1.4 获取菜单列表
        List<MenuPO> menuList = menuService.getMenuList(menuIds);
        menuList = menuService.filterDisableMenus(menuList);

        // 2. 拼接结果返回（不包含角色信息）
        return R.success(AuthConvert.INSTANCE.convert(user, Collections.emptyList(), menuList));
    }
} 