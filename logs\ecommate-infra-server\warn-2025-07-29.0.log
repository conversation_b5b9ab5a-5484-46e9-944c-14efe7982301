2025-07-29 16:13:34.289 [main] WARN  [ecommate-infra-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-infra-server] & group[DEFAULT_GROUP]
2025-07-29 16:13:34.296 [main] WARN  [ecommate-infra-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-infra-server.yaml] & group[DEFAULT_GROUP]
2025-07-29 16:13:36.467 [main] WARN  [ecommate-infra-server] org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yaotown.ecommate.infra.web]' package. Please check your configuration.
2025-07-29 16:13:37.231 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration' of type [com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [environmentSetup] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-29 16:13:37.240 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.243 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.244 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$897/0x0000018a4c7eecc8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.252 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.257 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq.enhance-com.yaotown.sdk.mq.properties.RocketEnhanceProperties' of type [com.yaotown.sdk.mq.properties.RocketEnhanceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.266 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.271 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.474 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.482 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.487 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.558 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-29 16:13:37.561 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:37.860 [main] WARN  [ecommate-infra-server] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-29 16:13:39.265 [main] WARN  [ecommate-infra-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.infra.module.pojo.entity.message.RecordMessagePO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-29 16:13:39.396 [main] WARN  [ecommate-infra-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.infra.module.pojo.entity.supplier.AddressCodePO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-29 16:13:43.683 [main] WARN  [ecommate-infra-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.infra.module.pojo.entity.supplier.BankPO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-29 16:13:53.235 [com.alibaba.nacos.client.naming.grpc.redo.0] WARN  [ecommate-infra-server] com.alibaba.nacos.client.naming - Grpc Connection is disconnect, skip current redo task
2025-07-29 16:43:32.705 [Thread-5] WARN  [ecommate-infra-server] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-29 16:43:32.706 [Thread-3] WARN  [ecommate-infra-server] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-29 16:43:32.706 [Thread-5] WARN  [ecommate-infra-server] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-29 16:43:32.706 [Thread-3] WARN  [ecommate-infra-server] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] WARN  [ecommate-infra-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] WARN  [ecommate-infra-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] WARN  [ecommate-infra-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] WARN  [ecommate-infra-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
