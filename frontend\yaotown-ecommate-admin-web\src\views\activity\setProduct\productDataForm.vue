<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" :align-center="true" custom-class="relation-dialog" width="70%">
    <ContentWrap :bottom="false" :border="false">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="queryParams.productName" placeholder="请输入商品名称" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" />搜索</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" />重置</el-button>
          <el-button type="primary" :disabled="!multipleSelection.length" @click="handleRelation"><Icon icon="ep:plus" class="mr-5px" />添加</el-button>
        </el-form-item>
      </el-form>
      <el-table stripe border style="width: 100%" height="463" v-loading="loading" :data="tableData" @selection-change="selectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="商品图片" width="80" prop="imgUrl" class-name="!p-0">
          <template #default="{ row }">
            <el-image :src="row.imgUrl" loading="lazy" class="h-30px align-bottom" fit="contain" @click="imagePreview(row.imgUrl)" />
          </template>
        </el-table-column>
        <el-table-column label="商品名称" prop="productName" min-width="150" show-overflow-tooltip />
        <el-table-column label="商品id" align="center" prop="productId" width="100" show-overflow-tooltip />
        <el-table-column label="商品价格" align="center" prop="normalPrice" width="100" :formatter="fenToYuanFormat" />
        <el-table-column label="商品库存" align="center" prop="productStock" width="100" />
        <el-table-column label="秒杀价" prop="activityPrice" width="150">
          <template #default="scope">
            <el-input-number v-model="scope.row.activityPrice" :min="0" :step="1" step-strictly size="small" />
          </template>
        </el-table-column>
        <el-table-column label="秒杀数量" prop="activityStock" width="150">
          <template #default="scope">
            <el-input-number v-model="scope.row.activityStock" :min="0" :step="1" step-strictly size="small" />
          </template>
        </el-table-column>
        <el-table-column label="活动库存预警值" prop="stockWarningValue" width="150">
          <template #default="scope">
            <el-input-number v-model="scope.row.stockWarningValue" :min="0" :step="1" step-strictly size="small" />
          </template>
        </el-table-column>
        <el-table-column label="限购数量" prop="purchaseLimit" width="150">
          <template #default="scope">
            <el-input-number v-model="scope.row.purchaseLimit" :min="0" :step="1" step-strictly size="small" />
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="150">
          <template #default="scope">
            <el-input-number v-model="scope.row.sort" :min="0" :step="1" step-strictly size="small" />
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        :total="paginationData.pageTotal"
        v-model:page="paginationData.pageNum"
        v-model:limit="paginationData.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
  </Dialog>
</template>
<script lang="ts" setup>
import * as SeckillActivityApi from '@/api/activity/seckillActivity'
import { fenToYuanFormat } from '@/utils/formatter'
import { createImageViewer } from '@/components/ImageViewer'

defineOptions({ name: 'SystemProductForm' })

const route = useRoute() // 路由

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('添加商品') // 弹窗的标题

const queryFormRef = ref() // 表单 Ref
const tableData = ref([])

const queryParams = reactive({
  activityId: route.params.activityId,
  timeSlotId: route.params.id,
  productName: ''
})

const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  pageTotal: 0
})

const loading = ref(false)
const getList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: paginationData.pageNum,
      pageSize: paginationData.pageSize,
      param: {
        activityId: queryParams.activityId,
        timeSlotId: queryParams.timeSlotId,
        productName: queryParams.productName
      }
    }
    const data = await SeckillActivityApi.unassociatedProduct(params)
    tableData.value = (data.pageContents || []).map(item => {
      return {
        productName: item.title,
        productId: item.itemId,
        normalPrice: item.purchasePrice,
        productStock: item.stockNum,
        activityPrice: item.purchasePrice / 100, // 秒杀价格
        activityStock: item.stockNum, // 秒杀数量
        stockWarningValue: 0, // 活动库存预警值
        purchaseLimit: undefined, // 限购数量
        imgUrl: item.imgUrl,
        sort: undefined, // 排序
      }
    })
    paginationData.pageTotal = data.pageTotal
  } finally {
    loading.value = false
  }
}

/** 广告封面预览 */
const imagePreview = (imgUrl: string) => {
  createImageViewer({
    urlList: [imgUrl]
  })
}

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  queryParams.productName = ''
  getList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

/** 搜索按钮操作 */
const handleQuery = () => {
  paginationData.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
const handleRelation = () => {
  console.log(139, multipleSelection.value)
  const activityProductList = multipleSelection.value.map(el => {
    return {
      ...el,
      activityId: queryParams.activityId,
      timeSlotId: queryParams.timeSlotId,
      activityPrice: el.activityPrice * 100, // 秒杀价格
    }
  })
  console.log(activityProductList)
  SeckillActivityApi.createActivityProduct({ activityProductList: activityProductList }).then(() => {
    emit('success')
    handleQuery()
  }).catch(err => {
    console.log(err)
  })
}


const multipleSelection = ref([])
const selectionChange = (val:[]) => {
  console.log(123, 'val', val)
  multipleSelection.value = val
}

</script>
<style lang="scss">
.divider{
  width: 100%;
  height: 1px;
  margin: 10px 0;
  background: #dcdfe6;
}
.el-overlay {
  .el-overlay-dialog{
    .relation-dialog{
      .el-dialog__body{
        padding: 0 !important;
      }
    }
  }
}
</style>
