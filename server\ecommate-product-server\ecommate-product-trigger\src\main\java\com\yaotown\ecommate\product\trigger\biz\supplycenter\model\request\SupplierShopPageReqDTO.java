package com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商店铺分页查询参数
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@Builder
public class SupplierShopPageReqDTO implements Serializable {
    /**
     * 企业ID
     */
    private Long enterpriseId;
    private String shopName;

    private Integer shopType;

    private Integer authStatus;

    private String contactsName;

    private Boolean authTimeSort;

    private Boolean expiredDateSort;
    /**
     * 平台
     */
    private List<String> platforms;
} 