<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yaotown</groupId>
    <artifactId>ecommate-common-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>基础 bom 文件，管理整个项目的依赖版本</description>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <java.version>17</java.version>
        <!-- 构建相关 -->
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <lombok.version>1.18.34</lombok.version>
        <dockerfile-maven-plugin.version>1.4.13</dockerfile-maven-plugin.version>
        <docker-maven-plugin.version>1.2.0</docker-maven-plugin.version>
        <!--公司公共组件-->
        <yaotown-common-base-core.version>1.0.1-jdk17</yaotown-common-base-core.version>
        <!-- 统一依赖管理 -->
        <spring.cloud.version>2023.0.3</spring.cloud.version>
        <alibaba.cloud.version>2023.0.1.2</alibaba.cloud.version>
        <spring.boot.version>3.3.1</spring.boot.version>


        <!-- DB 相关 -->
        <mysql.version>8.0.26</mysql.version>
        <druid.version>1.2.4</druid.version>
        <shardingsphere.starter.version>5.2.1</shardingsphere.starter.version>
        <shardingsphere.jdbc.version>5.5.2</shardingsphere.jdbc.version>
        <dynamic.datasource.version>4.3.1</dynamic.datasource.version>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <redisson.version>3.32.0</redisson.version>
        <mybatis.plus.version>3.5.7</mybatis.plus.version>
<!--        <elasticsearch.version>4.2.4</elasticsearch.version>-->
        <easy-es.version>2.0.0</easy-es.version>
        <es-rest-high-level-client.version>7.14.0</es-rest-high-level-client.version>
        <es.version>7.14.0</es.version>
        <jedis.version>3.3.0</jedis.version>
        <table-builder.version>1.0.2</table-builder.version>
        <lettuce.version>6.3.2.RELEASE</lettuce.version>
        <mongodb.version>3.1.3</mongodb.version>
        <caffeine.version>2.9.3</caffeine.version>
        <!-- 消息队列 -->
        <rocketmq.version>2.2.3</rocketmq.version>
        <netty-all.version>4.1.90.Final</netty-all.version>
        <!-- Job 定时任务相关 -->
        <xxl-job.version>2.4.0</xxl-job.version>
        <powerjob.version>3.4.7</powerjob.version>
        <shedlock.version>6.5.0</shedlock.version>
        <!-- Test 测试相关 -->
        <mockito-inline.version>5.2.0</mockito-inline.version>
        <!-- Bpm 工作流相关 -->
        <flowable.version>7.0.1</flowable.version>
        <liteflow.version>2.13.2</liteflow.version>
        <!-- 工具类相关 -->
        <hutool.version>5.8.26</hutool.version>
        <easypoi.project.version>4.4.2.3-stable</easypoi.project.version>
        <mica.auto.version>1.1.0</mica.auto.version>
        <guava.version>29.0-jre</guava.version>
        <zxing.core.version>3.5.1</zxing.core.version>
        <zxing.javase.version>3.5.1</zxing.javase.version>
        <html2image.version>0.1.0</html2image.version>
        <ttl.version>2.12.3</ttl.version>
        <jsoup.version>1.18.1</jsoup.version>
        <fastjson.version>1.2.76</fastjson.version>
        <fastjson2.version>2.0.51</fastjson2.version>
        <commons.io.version>2.15.0</commons.io.version>
        <commons.fileupload.version>1.3.3</commons.fileupload.version>
        <common-lang3.version>3.8.1</common-lang3.version>
        <common-pool.version>2.6.2</common-pool.version>
        <java.email.version>1.4.7</java.email.version>
        <easy-captcha.version>1.6.2</easy-captcha.version>
        <anji-plus-captcha.version>1.4.0</anji-plus-captcha.version>
        <!-- 三方云服务相关 -->
        <dingtalk-version>2.0.0</dingtalk-version>
        <wx-java.version>4.6.0</wx-java.version>
        <okhttp.version>4.9.3</okhttp.version>
        <curator-framework.version>4.2.0</curator-framework.version>
        <curator-recipes.version>4.0.1</curator-recipes.version>
        <curator-x-discovery.version>5.1.0</curator-x-discovery.version>
        <aliyun-sdk-oss.version>3.5.0</aliyun-sdk-oss.version>
        <aliyun-java-sdk-core.version>4.5.1</aliyun-java-sdk-core.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--cloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${alibaba.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!-- 公共组件 -->
            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>yaotown-common-base-core</artifactId>
                <version>${yaotown-common-base-core.version}</version>
            </dependency>

            <!-- 业务组件 begin-->
            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-apidoc</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-protection</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-rpc</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-es</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.yaotown</groupId>
                <artifactId>ecommate-common-id</artifactId>
                <version>${revision}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.yaotown</groupId>-->
<!--                <artifactId>ecommate-common-easypoi</artifactId>-->
<!--                <version>${revision}</version>-->
<!--            </dependency>-->
            <!-- 业务组件 end-->

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension-spring6</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!--mysql-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!--JDBC druid-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
                <version>${shardingsphere.starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc</artifactId>
                <version>${shardingsphere.jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic.datasource.version}</version>
            </dependency>

            <!--apache commons lang3-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${common-lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>${lettuce.version}</version>
            </dependency>

            <dependency>
                <groupId>net.dreamlu</groupId>
                <artifactId>mica-auto</artifactId>
                <version>${mica.auto.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- guava 工具类-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!--io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!--文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!--            &lt;!&ndash;apache commons pool2&ndash;&gt;-->
            <!--            <dependency>-->
            <!--                <groupId>org.apache.commons</groupId>-->
            <!--                <artifactId>commons-pool2</artifactId>-->
            <!--                <version>${common-pool.version}</version>-->
            <!--            </dependency>-->

            <!--aliyun oss-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>

            <!--aliyun sdk-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
            </dependency>

            <!-- easypoi -->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>${easypoi.project.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/javax.mail/mail -->
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${java.email.version}</version>
            </dependency>

            <!-- elasticsearch -->
            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${es-rest-high-level-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${es.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-mongodb</artifactId>
                <version>${mongodb.version}</version>
            </dependency>

            <!-- Job 定时任务相关 -->
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-spring</artifactId>
                <version>${shedlock.version}</version>
            </dependency>

            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-provider-redis-spring</artifactId>
                <version>${shedlock.version}</version>
            </dependency>

<!--            &lt;!&ndash;aspose&ndash;&gt;-->
<!--            <dependency>-->
<!--                <groupId>com.aspose</groupId>-->
<!--                <artifactId>aspose-cells</artifactId>-->
<!--                <version>21.11</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.aspose</groupId>-->
<!--                <artifactId>aspose-words</artifactId>-->
<!--                <version>14.9.0-jdk16</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.google.zxing</groupId>-->
<!--                <artifactId>core</artifactId>-->
<!--                <version>${zxing.core.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.github.xuwei-k</groupId>-->
<!--                <artifactId>html2image</artifactId>-->
<!--                <version>${html2image.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${zxing.javase.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>${dingtalk-version}</version>
            </dependency>

            <!--TTL-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${ttl.version}</version>
            </dependency>

            <!-- flowable https://www.cnblogs.com/xwbz/p/9858174.html-->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-rule-nacos</artifactId>
                <version>${liteflow.version}</version>
            </dependency>

            <!-- rocketmq -->
            <!-- https://mvnrepository.com/artifact/org.apache.rocketmq/rocketmq-spring-boot-starter -->
            <!--            <dependency>-->
            <!--                <groupId>org.apache.rocketmq</groupId>-->
            <!--                <artifactId>rocketmq-spring-boot-starter</artifactId>-->
            <!--                <version>${rocketmq.version}</version>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${wx-java.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator-recipes.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.curator/curator-x-discovery -->
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-discovery</artifactId>
                <version>${curator-x-discovery.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>${easy-captcha.version}</version>
            </dependency>

            <dependency>
                <groupId>com.anji-plus</groupId>
                <artifactId>captcha-spring-boot-starter</artifactId>
                <version>${anji-plus-captcha.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version> <!-- 支持 Mockito 的 final 类与 static 方法的 mock -->
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- maven-surefire-plugin 插件，用于运行单元测试。 -->
                <!-- 注意，需要使用 3.0.X+，因为要支持 Junit 5 版本 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <!-- maven-compiler-plugin 插件，解决 spring-boot-configuration-processor + Lombok + MapStruct 组合 -->
                <!-- https://stackoverflow.com/questions/33483697/re-run-spring-boot-configuration-annotation-processor-to-update-generated-metada -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring.boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                </plugin>
            </plugins>
        </pluginManagement>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
            <!--deploy后可下载源码-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <executions>
                    <!-- 在 install 阶段生成源代码 JAR -->
                    <execution>
                        <id>attach-sources-on-install</id>
                        <phase>install</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                    <!-- 在 deploy 阶段生成源代码 JAR -->
                    <execution>
                        <id>attach-sources-on-deploy</id>
                        <phase>deploy</phase>
                        <goals>
                            <!-- 使用 jar-no-fork 目标以避免重复编译 -->
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>${dockerfile-maven-plugin.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>2495628-release-SNVIz1</id>
            <url>https://packages.aliyun.com/66f94feb63cd360abfe4a4aa/maven/2495628-release-snviz1</url>
        </repository>
        <snapshotRepository>
            <id>2495628-snapshot-xGQoVQ</id>
            <url>https://packages.aliyun.com/66f94feb63cd360abfe4a4aa/maven/2495628-snapshot-xgqovq</url>
        </snapshotRepository>
    </distributionManagement>

</project>