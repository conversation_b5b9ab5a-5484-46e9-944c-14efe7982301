-- supplier
CREATE TABLE `yt_supplier_product`
(
    `id`                        BIGINT       NOT NULL COMMENT '主键',
    `enterprise_id`             BIGINT       NOT NULL COMMENT '所属企业id',
    `origin`                    tinyint      NULL     DEFAULT NULL COMMENT '来源，0用户添加、1商城同步',
    `ori_product_id`            varchar(128) NULL     DEFAULT NULL COMMENT '商品原始id',
    `product_outer_id`          varchar(128) NULL     DEFAULT NULL COMMENT '商家自编码',
    `platform`                  varchar(32)  NULL     DEFAULT NULL COMMENT '平台类型',
    `product_type`              tinyint      NULL     DEFAULT NULL COMMENT '类型，0实物,1虚拟,',
    `product_name`              varchar(200) NULL     DEFAULT NULL COMMENT '简称',
    `product_full_name`         varchar(200) NULL     DEFAULT NULL COMMENT '名称',
    `product_image_url`         varchar(256) NULL     DEFAULT NULL COMMENT '封面图url',
    `category_id`               varchar(64)  NULL     DEFAULT NULL COMMENT '一级类目ID',
    `category_leaf_id`          varchar(64)  NULL     DEFAULT NULL COMMENT '最后一级类目ID',
    `category_full_name`        varchar(128) NULL     DEFAULT NULL COMMENT '目录名称（从根目录节点开始到当前叶子目录节点，用“/”连接）',
    `supplier_categoryId_one`   bigint                DEFAULT NULL COMMENT '供应商分类-一级',
    `supplier_categoryId_two`   bigint                DEFAULT NULL COMMENT '供应商分类-二级',
    `supplier_categoryId_three` bigint                DEFAULT NULL COMMENT '供应商分类-三级',
    `listing_platform`          varchar(255)          DEFAULT NULL COMMENT '可供货平台',
    `main_image_video_url`      varchar(255)          DEFAULT NULL COMMENT '主图视频URL',
    `delivery_method`           tinyint               DEFAULT NULL COMMENT '配送方式 1：快递发货 2:同城配送 3:上门自提',
    `freight_template_id`       BIGINT                DEFAULT NULL COMMENT '运费模板id',
    `draft_status`              tinyint      NULL     DEFAULT NULL COMMENT '草稿状态(0:草稿 1:已发布)',
    `distributor_status`        tinyint      NULL     DEFAULT NULL COMMENT '分销状态(0:否 1:是)',
    `sale_state`                tinyint      NULL     DEFAULT NULL COMMENT '上下架状态（0上架，1下架）',
    `sale_state_time`           datetime              DEFAULT NULL COMMENT '上下架状态更新时间，上下架时间',
    `audit_status`              TINYINT      NOT NULL DEFAULT 0 COMMENT '审核状态(0:待审核 1-审核通过 2-审核不通过)',
    `audit_time`                DATETIME     NULL COMMENT '审核时间',
    `audit_remark`              VARCHAR(128)          DEFAULT '' COMMENT '审核备注',
    `description`               mediumtext   NULL COMMENT '描述/详情',
    `del_status`                int                   DEFAULT '1' COMMENT '商家删除状态(-2：商家永久删除 -1：商家删除 1：默认未删除)',
    `delete_flag`               TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`                BIGINT       NOT NULL COMMENT '创建人',
    `creator_name`              VARCHAR(32)  NOT NULL COMMENT '创建人名字',
    `modifier_id`               BIGINT       NOT NULL COMMENT '修改操作人',
    `modifier_name`             VARCHAR(32)  NOT NULL COMMENT '修改操作人名字',
    `created`                   DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                   DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '供应商产品表';

CREATE TABLE `yt_supplier_platform_product`
(
    `id`                          BIGINT       NOT NULL DEFAULT '0' COMMENT '主键ID',
    `enterprise_id`               BIGINT       NOT NULL COMMENT '所属企业id',
    `origin`                      tinyint      NULL     DEFAULT NULL COMMENT '来源，0用户添加、1商城同步',
    `ori_product_id`              varchar(128) NULL     DEFAULT NULL COMMENT '商品原始id',
    `product_id`                  BIGINT       NOT NULL DEFAULT '0' COMMENT '产品ID',
    `platform`                    varchar(32)  NULL     DEFAULT NULL COMMENT '平台类型',
    `platform_category_id`        varchar(64)  NULL     DEFAULT NULL COMMENT '一级类目ID',
    `platform_category_leaf_id`   varchar(64)  NULL     DEFAULT NULL COMMENT '最后一级类目ID',
    `platform_category_full_name` varchar(128) NULL     DEFAULT NULL COMMENT '目录名称（从根目录节点开始到当前叶子目录节点，用“/”连接）',
    `content`                     text         NOT NULL COMMENT '平台商品信息json',
    `delete_flag`                 TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`                  BIGINT       NOT NULL COMMENT '创建人',
    `creator_name`                VARCHAR(32)  NOT NULL COMMENT '创建人名字',
    `modifier_id`                 BIGINT       NOT NULL COMMENT '修改操作人',
    `modifier_name`               VARCHAR(32)  NOT NULL COMMENT '修改操作人名字',
    `created`                     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='供应商平台产品表';

CREATE TABLE `yt_supplier_product_image`
(
    `id`            BIGINT       NOT NULL COMMENT '主键',
    `product_id`    BIGINT       NOT NULL COMMENT '产品id',
    `enterprise_id` BIGINT       NOT NULL COMMENT '所属企业id',
    `image_type`    tinyint      NOT NULL COMMENT '配图类别(1:主图 2: 3:4主图 3:详情图)',
    `relation_type` tinyint      NOT NULL COMMENT '配图关联类别，1商品配图，2sku配图',
    `relation_id`   BIGINT       NOT NULL COMMENT '图片用途id(skuId或产品id)',
    `url`           varchar(512) NOT NULL COMMENT '图片url',
    `delete_flag`   TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`    BIGINT       NOT NULL COMMENT '创建人',
    `creator_name`  VARCHAR(32)  NOT NULL COMMENT '创建人名字',
    `modifier_id`   BIGINT       NOT NULL COMMENT '修改操作人',
    `modifier_name` VARCHAR(32)  NOT NULL COMMENT '修改操作人名字',
    `created`       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_relation_id_type` (`relation_id`, `relation_type`) USING BTREE
) ENGINE = InnoDB COMMENT = '供应商产品图片表';

CREATE TABLE `yt_supplier_product_sku`
(
    `id`                       BIGINT       NOT NULL COMMENT '主键',
    `enterprise_id`            BIGINT       NOT NULL COMMENT '所属企业id',
    `product_id`               BIGINT       NOT NULL COMMENT '供应商产品id',
    `ori_sku_id`               varchar(128) NULL     DEFAULT NULL COMMENT 'sku原始id',
    `product_sku_bind`         int                   DEFAULT 0 COMMENT '是组合商品sku，0不是、1组合',
    `product_sku_props`        text         NOT NULL COMMENT 'sku规格',
    `image_url`                varchar(256) NULL     DEFAULT NULL COMMENT 'sku主图',
    `show_opt`                 int          NOT NULL COMMENT 'sku显示设置:1显示、2隐藏',
    `sale_price`               BIGINT       NOT NULL COMMENT '销货价（单位为分）',
    `product_sku_cost`         BIGINT       NULL     DEFAULT NULL COMMENT '成本价（单位为分）',
    `product_sku_supply_price` BIGINT       NULL     DEFAULT NULL COMMENT '供货价（单位为分）',
    `min_retail_price`         BIGINT                DEFAULT NULL COMMENT '最低建议零售价',
    `max_retail_price`         BIGINT                DEFAULT NULL COMMENT '最高建议零售价',
    `stock`                    int          NOT NULL COMMENT '库存数',
    `product_sku_outer_id`     varchar(200) NOT NULL COMMENT '商品sku自编码',
    `product_sku_bar_code`     varchar(200) NOT NULL COMMENT 'sku条形码',
    `volume`                   decimal(12, 3)        DEFAULT NULL COMMENT '物流体积',
    `weight`                   decimal(12, 3)        DEFAULT NULL COMMENT '物流重量(千克)',
    `delete_flag`              TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`               BIGINT       NOT NULL COMMENT '创建人',
    `creator_name`             VARCHAR(32)  NOT NULL COMMENT '创建人名字',
    `modifier_id`              BIGINT       NOT NULL COMMENT '修改操作人',
    `modifier_name`            VARCHAR(32)  NOT NULL COMMENT '修改操作人名字',
    `created`                  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '供应商产品sku表';

CREATE TABLE `yt_supplier_product_extra`
(
    `id`            BIGINT      NOT NULL COMMENT '主键',
    `product_id`    BIGINT      NOT NULL COMMENT '产品id',
    `sku_id`        BIGINT               DEFAULT NULL COMMENT '产品sku id',
    `extra_code`    varchar(255)         DEFAULT NULL COMMENT '扩展code',
    `extra_value`   text                 DEFAULT NULL COMMENT '扩展值',
    `delete_flag`   TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`    BIGINT      NOT NULL COMMENT '创建人',
    `creator_name`  VARCHAR(32) NOT NULL COMMENT '创建人名字',
    `modifier_id`   BIGINT      NOT NULL COMMENT '修改操作人',
    `modifier_name` VARCHAR(32) NOT NULL COMMENT '修改操作人名字',
    `created`       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `product_id_idx` (`product_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '供应商产品扩展属性表';

CREATE TABLE `yt_supplier_product_category_attribute`
(
    `id`                        BIGINT      NOT NULL COMMENT '主键',
    `enterprise_id`             BIGINT      NOT NULL COMMENT '渠道商企业id',
    `product_id`                BIGINT      NOT NULL COMMENT '产品id',
    `platform`                  VARCHAR(32) NOT NULL COMMENT '平台类型',
    `category_leaf_id`          varchar(64) NULL     DEFAULT NULL COMMENT '最后一级类目ID',
    `platform_category_leaf_id` varchar(64) NULL     DEFAULT NULL COMMENT '平台最后一级类目ID',
    `attribute_id`              varchar(64) NULL     DEFAULT NULL COMMENT '类目属性ID',
    `platform_attribute_id`     varchar(64) NULL     DEFAULT NULL COMMENT '平台类目属性ID',
    `attribute_value`           text                 DEFAULT NULL COMMENT '类目属性值',
    `delete_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`                BIGINT      NOT NULL COMMENT '创建人',
    `creator_name`              VARCHAR(32) NOT NULL COMMENT '创建人名字',
    `modifier_id`               BIGINT      NOT NULL COMMENT '修改操作人',
    `modifier_name`             VARCHAR(32) NOT NULL COMMENT '修改操作人名字',
    `created`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `product_id_idx` (`product_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '供应商产品类目属性表';
#
# CREATE TABLE `yt_product_snapshot`
# (
#     `id`                 BIGINT       NOT NULL COMMENT '主键',
#     `product_id`         BIGINT       NOT NULL COMMENT '产品id',
#     `enterprise_id`      BIGINT       NOT NULL COMMENT '所属企业id',
#     `origin`             tinyint      NULL     DEFAULT NULL COMMENT '来源，0用户添加、1商城同步、2私域选品',
#     `ori_product_id`     varchar(128) NULL     DEFAULT NULL COMMENT '商品原始id',
#     `product_outer_id`   varchar(200) NULL     DEFAULT NULL COMMENT '商家自编码',
#     `platform`           varchar(32)  NULL     DEFAULT NULL COMMENT '平台类型',
#     `product_type`       tinyint      NULL     DEFAULT NULL COMMENT '类型，0实物,1虚拟,',
#     `product_name`       varchar(200) NULL     DEFAULT NULL COMMENT '简称',
#     `product_full_name`  varchar(200) NULL     DEFAULT NULL COMMENT '名称',
#     `product_image_url`  varchar(256) NULL     DEFAULT NULL COMMENT '封面图url',
#     `category_id`        varchar(64)  NULL     DEFAULT NULL COMMENT '一级类目ID',
#     `category_leaf_id`   varchar(64)  NULL     DEFAULT NULL COMMENT '最后一级类目ID',
#     `category_full_name` varchar(128) NULL     DEFAULT NULL COMMENT '目录名称（从根目录节点开始到当前叶子目录节点，用“/”连接）',
#     `sale_state`         tinyint      NULL     DEFAULT NULL COMMENT '上下架状态（0上架，1下架）',
#     `sale_state_time`    datetime              DEFAULT NULL COMMENT '上下架状态更新时间，上下架时间',
#     `description`        mediumtext   NULL COMMENT '描述/详情',
#     `delete_flag`        TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
#     `creator_id`         BIGINT       NOT NULL COMMENT '创建人',
#     `creator_name`       VARCHAR(32)  NOT NULL COMMENT '创建人名字',
#     `modifier_id`        BIGINT       NOT NULL COMMENT '修改操作人',
#     `modifier_name`      VARCHAR(32)  NOT NULL COMMENT '修改操作人名字',
#     `created`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
#     `updated`            DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
#     PRIMARY KEY (`id`) USING BTREE
# ) ENGINE = InnoDB COMMENT = '选品中心商品快照表';
#
# CREATE TABLE `yt_product_image_snapshot`
# (
#     `id`                        BIGINT       NOT NULL COMMENT '主键',
#     `image_id`                  BIGINT       NOT NULL COMMENT '主键',
#     `enterprise_id`             BIGINT       NOT NULL COMMENT '所属企业id',
#     `origin`                    tinyint      NULL     DEFAULT NULL COMMENT '来源(1:铺货商品 2:铺货单)',
#     `image_type`                tinyint      NOT NULL COMMENT '配图类别，1商品配图，2sku配图',
#     `image_from_id`             BIGINT       NOT NULL COMMENT '图片用途id(skuId或产品id)',
#     `image_from_snapshot_id`    BIGINT       NOT NULL COMMENT '图片用途快照id(skuId或产品id)',
#     `image_product_id`          BIGINT       NOT NULL COMMENT '图片产品id',
#     `image_product_snapshot_id` BIGINT       NOT NULL COMMENT '图片快照产品id',
#     `url`                       varchar(512) NOT NULL COMMENT '图片url',
#     `delete_flag`               TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
#     `creator_id`                BIGINT       NOT NULL COMMENT '创建人',
#     `creator_name`              VARCHAR(32)  NOT NULL COMMENT '创建人名字',
#     `modifier_id`               BIGINT       NOT NULL COMMENT '修改操作人',
#     `modifier_name`             VARCHAR(32)  NOT NULL COMMENT '修改操作人名字',
#     `created`                   DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
#     `updated`                   DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
#     PRIMARY KEY (`id`) USING BTREE,
#     KEY `idx_image_origin_product_id` (`image_product_id`, `origin`) USING BTREE
# ) ENGINE = InnoDB COMMENT = '选品中心商品图片快照表';
#
# CREATE TABLE `yt_product_sku_snapshot`
# (
#     `id`                       BIGINT       NOT NULL COMMENT '主键',
#     `sku_id`                   BIGINT       NOT NULL COMMENT '主键',
#     `enterprise_id`            BIGINT       NOT NULL COMMENT '所属企业id',
#     `product_id`               BIGINT       NOT NULL COMMENT '所属商品id',
#     `product_snapshot_id`      BIGINT       NOT NULL COMMENT '所属商品快照id',
#     `ori_sku_id`               varchar(128) NULL     DEFAULT NULL COMMENT 'sku原始id',
#     `product_sku_bind`         int                   DEFAULT 0 COMMENT '是组合商品sku，0不是、1组合',
#     `product_sku_props`        text         NOT NULL COMMENT 'sku规格',
#     `show_opt`                 int          NOT NULL COMMENT 'sku显示设置:1显示、2隐藏',
#     `sale_price`               BIGINT       NOT NULL COMMENT '销货价（单位为分）',
#     `product_sku_cost`         BIGINT       NULL     DEFAULT NULL COMMENT '成本价（单位为分）',
#     `product_sku_supply_price` BIGINT       NULL     DEFAULT NULL COMMENT '供货价（单位为分）',
#     `min_retail_price`         BIGINT                DEFAULT NULL COMMENT '最低建议零售价',
#     `max_retail_price`         BIGINT                DEFAULT NULL COMMENT '最高建议零售价',
#     `stock`                    int          NOT NULL COMMENT '库存数',
#     `product_sku_outer_id`     varchar(200) NOT NULL COMMENT '商品sku自编码',
#     `product_sku_bar_code`     varchar(200) NOT NULL COMMENT 'sku条形码',
#     `delete_flag`              TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
#     `creator_id`               BIGINT       NOT NULL COMMENT '创建人',
#     `creator_name`             VARCHAR(32)  NOT NULL COMMENT '创建人名字',
#     `modifier_id`              BIGINT       NOT NULL COMMENT '修改操作人',
#     `modifier_name`            VARCHAR(32)  NOT NULL COMMENT '修改操作人名字',
#     `created`                  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
#     `updated`                  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
#     PRIMARY KEY (`id`) USING BTREE
# ) ENGINE = InnoDB COMMENT = '选品中心商品sku快照表';
#
# CREATE TABLE `yt_product_extra_snapshot`
# (
#     `id`                  BIGINT      NOT NULL COMMENT '主键',
#     `extra_id`            BIGINT      NOT NULL COMMENT '主键',
#     `product_id`          BIGINT      NOT NULL COMMENT '产品id',
#     `product_snapshot_id` BIGINT      NOT NULL COMMENT '产品快照id',
#     `sku_id`              BIGINT               DEFAULT NULL COMMENT '产品sku id',
#     `sku_snapshot_id`     BIGINT               DEFAULT NULL COMMENT '产品sku快照id',
#     `extra_code`          varchar(255)         DEFAULT NULL COMMENT '扩展code',
#     `extra_value`         text                 DEFAULT NULL COMMENT '扩展值',
#     `delete_flag`         TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
#     `creator_id`          BIGINT      NOT NULL COMMENT '创建人',
#     `creator_name`        VARCHAR(32) NOT NULL COMMENT '创建人名字',
#     `modifier_id`         BIGINT      NOT NULL COMMENT '修改操作人',
#     `modifier_name`       VARCHAR(32) NOT NULL COMMENT '修改操作人名字',
#     `created`             DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
#     `updated`             DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
#     PRIMARY KEY (`id`) USING BTREE,
#     KEY `product_id_idx` (`product_id`) USING BTREE,
#     KEY `sku_id_idx` (`sku_id`) USING BTREE
# ) ENGINE = InnoDB COMMENT = '选品中心商品扩展属性快照表';
#
# CREATE TABLE `yt_product_category_attribute_snapshot`
# (
#     `id`                        BIGINT      NOT NULL COMMENT '主键',
#     `enterprise_id`             BIGINT      NOT NULL COMMENT '渠道商企业id',
#     `product_id`                BIGINT      NOT NULL COMMENT '产品id',
#     `product_snapshot_id`       BIGINT      NOT NULL COMMENT '产品快照id',
#     `platform`                  VARCHAR(32) NOT NULL COMMENT '平台类型',
#     `category_leaf_id`          varchar(64) NULL     DEFAULT NULL COMMENT '最后一级类目ID',
#     `platform_category_leaf_id` varchar(64) NULL     DEFAULT NULL COMMENT '平台最后一级类目ID',
#     `attribute_id`              varchar(64) NULL     DEFAULT NULL COMMENT '类目属性ID',
#     `platform_attribute_id`     varchar(64) NULL     DEFAULT NULL COMMENT '平台类目属性ID',
#     `attribute_value`           text                 DEFAULT NULL COMMENT '类目属性值',
#     `delete_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
#     `creator_id`                BIGINT      NOT NULL COMMENT '创建人',
#     `creator_name`              VARCHAR(32) NOT NULL COMMENT '创建人名字',
#     `modifier_id`               BIGINT      NOT NULL COMMENT '修改操作人',
#     `modifier_name`             VARCHAR(32) NOT NULL COMMENT '修改操作人名字',
#     `created`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
#     `updated`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
#     PRIMARY KEY (`id`) USING BTREE,
#     KEY `product_id_idx` (`product_id`) USING BTREE
# ) ENGINE = InnoDB COMMENT = '选品中心商品类目属性快照表';

ALTER TABLE yt_product
#     ADD COLUMN `last_snapshot_id` BIGINT NOT NULL COMMENT '最新快照id' AFTER `id`,
    ADD COLUMN `buys`          int NOT NULL DEFAULT '0' COMMENT '已销售数量' AFTER `description`,
    ADD COLUMN `comments`      int NOT NULL DEFAULT '0' COMMENT '评论数' AFTER `buys`,
    ADD COLUMN `review_scores` int DEFAULT '0' COMMENT '评论得分' AFTER `comments`;
# ALTER TABLE yt_product_image
#     ADD COLUMN `last_snapshot_id` BIGINT NOT NULL COMMENT '最新快照id' AFTER `id`;
# ALTER TABLE yt_product_sku
#     ADD COLUMN `last_snapshot_id` BIGINT NOT NULL COMMENT '最新快照id' AFTER `id`;
# ALTER TABLE yt_product_extra
#     ADD COLUMN `last_snapshot_id` BIGINT NOT NULL COMMENT '最新快照id' AFTER `id`;

CREATE TABLE `yt_product_category_attribute`
(
    `id`                        BIGINT      NOT NULL COMMENT '主键',
#     `last_snapshot_id`          BIGINT      NOT NULL COMMENT '最新快照id',
    `enterprise_id`             BIGINT      NOT NULL COMMENT '渠道商企业id',
    `product_id`                BIGINT      NOT NULL COMMENT '产品id',
    `platform`                  VARCHAR(32) NOT NULL COMMENT '平台类型',
    `category_leaf_id`          varchar(64) NULL     DEFAULT NULL COMMENT '最后一级类目ID',
    `platform_category_leaf_id` varchar(64) NULL     DEFAULT NULL COMMENT '平台最后一级类目ID',
    `attribute_id`              varchar(64) NULL     DEFAULT NULL COMMENT '类目属性ID',
    `platform_attribute_id`     varchar(64) NULL     DEFAULT NULL COMMENT '平台类目属性ID',
    `attribute_value`           text                 DEFAULT NULL COMMENT '类目属性值',
    `delete_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`                BIGINT      NOT NULL COMMENT '创建人',
    `creator_name`              VARCHAR(32) NOT NULL COMMENT '创建人名字',
    `modifier_id`               BIGINT      NOT NULL COMMENT '修改操作人',
    `modifier_name`             VARCHAR(32) NOT NULL COMMENT '修改操作人名字',
    `created`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `product_id_idx` (`product_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '选品中心产品类目属性表';

ALTER TABLE yt_product
    COMMENT '选品中心产品表';
ALTER TABLE yt_product_image
    COMMENT '选品中心产品图片表';
ALTER TABLE yt_product_sku
    COMMENT '选品中心产品sku表';
ALTER TABLE yt_product_extra
    COMMENT '选品中心产品扩展属性表';

# ALTER TABLE yt_product_listing
#     ADD COLUMN `product_snapshot_id` BIGINT NOT NULL COMMENT '产品快照id' AFTER `product_id`,
#     CHANGE COLUMN `product_image_id` `product_image_snapshot_id` BIGINT DEFAULT NULL COMMENT '主图图片快照id';
# ALTER TABLE yt_product_listing_sku
#     ADD COLUMN `product_snapshot_id` BIGINT NOT NULL COMMENT '产品快照id' AFTER `product_id`,
#     ADD COLUMN `sku_snapshot_id`     BIGINT NOT NULL COMMENT '产品sku快照id' AFTER `sku_id`;


CREATE TABLE `yt_supplier_distributor_relation`
(
    `id`                        BIGINT      NOT NULL COMMENT '主键',
    `supplier_enterprise_id`    BIGINT      NOT NULL COMMENT '供应商企业ID',
    `supplier_name`             VARCHAR(64)          DEFAULT NULL COMMENT '供应商企业名称',
    `distributor_enterprise_id` BIGINT      NOT NULL COMMENT '分销商企业ID',
    `distributor_name`          VARCHAR(64)          DEFAULT NULL COMMENT '分销商企业名称',
    `relation_status`           TINYINT     NOT NULL DEFAULT 0 COMMENT '合作状态(0:申请中 1:已合作 2:已拒绝 3:已终止)',
    `apply_time`                DATETIME             DEFAULT NULL COMMENT '申请时间',
    `approve_time`              DATETIME             DEFAULT NULL COMMENT '审核通过时间',
    `rejected_time`             DATETIME             DEFAULT NULL COMMENT '拒绝时间',
    `terminate_time`            DATETIME             DEFAULT NULL COMMENT '终止时间',
    `contract_start_time`       DATETIME             DEFAULT NULL COMMENT '合同开始时间',
    `contract_end_time`         DATETIME             DEFAULT NULL COMMENT '合同结束时间',
    `commission_rate`           DECIMAL(5, 2)        DEFAULT NULL COMMENT '默认分佣比例(%)',
    `settlement_cycle`          TINYINT              DEFAULT NULL COMMENT '结算周期(1:日结 2:周结 3:月结)',
    `auth_type`                 TINYINT              DEFAULT 0 COMMENT '授权方式(0:全部产品 1:部分产品)',
    `remark`                    VARCHAR(512)         DEFAULT NULL COMMENT '备注说明',
    `reject_reason`             VARCHAR(512)         DEFAULT NULL COMMENT '拒绝原因',
    `terminate_reason`          VARCHAR(512)         DEFAULT NULL COMMENT '终止原因',
    `delete_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`                BIGINT      NOT NULL COMMENT '创建人',
    `creator_name`              VARCHAR(32) NOT NULL COMMENT '创建人名字',
    `modifier_id`               BIGINT      NOT NULL COMMENT '修改操作人',
    `modifier_name`             VARCHAR(32) NOT NULL COMMENT '修改操作人名字',
    `created`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_supplier_distributor` (`supplier_enterprise_id`, `distributor_enterprise_id`) USING BTREE,
    KEY `idx_supplier` (`supplier_enterprise_id`) USING BTREE,
    KEY `idx_distributor` (`distributor_enterprise_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '供应商分销商合作关系表';

# -- 供应商授权分销商的产品表
# CREATE TABLE `yt_supplier_distributor_product`
# (
#     `id`                        BIGINT      NOT NULL COMMENT '主键',
#     `distributor_relation_id`               BIGINT      NOT NULL COMMENT '合作关系ID',
#     `supplier_enterprise_id`    BIGINT      NOT NULL COMMENT '供应商企业ID',
#     `distributor_enterprise_id` BIGINT      NOT NULL COMMENT '分销商企业ID',
#     `product_id`                BIGINT      NOT NULL COMMENT '产品ID',
#     `product_snapshot_id`       BIGINT               DEFAULT NULL COMMENT '产品快照ID',
#     `auth_status`               TINYINT     NOT NULL DEFAULT 1 COMMENT '授权状态(0:未授权 1:已授权)',
#     `commission_rate`           DECIMAL(5, 2)        DEFAULT NULL COMMENT '产品特定分佣比例(%)',
#     `min_sell_price`            BIGINT               DEFAULT NULL COMMENT '最低销售价(分)',
#     `max_sell_price`            BIGINT               DEFAULT NULL COMMENT '最高销售价(分)',
#     `delete_flag`               TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
#     `creator_id`                BIGINT      NOT NULL COMMENT '创建人',
#     `creator_name`              VARCHAR(32) NOT NULL COMMENT '创建人名字',
#     `modifier_id`               BIGINT      NOT NULL COMMENT '修改操作人',
#     `modifier_name`             VARCHAR(32) NOT NULL COMMENT '修改操作人名字',
#     `created`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
#     `updated`                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
#     PRIMARY KEY (`id`) USING BTREE,
#     UNIQUE KEY `uk_relation_product` (`distributor_relation_id`, `product_id`) USING BTREE,
#     KEY `idx_relation` (`distributor_relation_id`) USING BTREE,
#     KEY `idx_product` (`product_id`) USING BTREE,
#     KEY `idx_supplier_distributor` (`supplier_enterprise_id`, `distributor_enterprise_id`) USING BTREE
# ) ENGINE = InnoDB COMMENT = '供应商授权分销商产品表';

CREATE TABLE `yt_product_platform_product`
(
    `id`                          BIGINT       NOT NULL DEFAULT '0' COMMENT '主键ID',
    `enterprise_id`               BIGINT       NOT NULL COMMENT '所属企业id',
    `origin`                      tinyint      NULL     DEFAULT NULL COMMENT '来源，0用户添加、1商城同步',
    `ori_product_id`              varchar(128) NULL     DEFAULT NULL COMMENT '商品原始id',
    `product_id`                  BIGINT       NOT NULL DEFAULT '0' COMMENT '产品ID',
    `platform`                    varchar(32)  NULL     DEFAULT NULL COMMENT '平台类型',
    `platform_category_id`        varchar(64)  NULL     DEFAULT NULL COMMENT '一级类目ID',
    `platform_category_leaf_id`   varchar(64)  NULL     DEFAULT NULL COMMENT '最后一级类目ID',
    `platform_category_full_name` varchar(128) NULL     DEFAULT NULL COMMENT '目录名称（从根目录节点开始到当前叶子目录节点，用“/”连接）',
    `content`                     text         NOT NULL COMMENT '平台商品信息json',
    `delete_flag`                 TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`                  BIGINT       NOT NULL COMMENT '创建人',
    `creator_name`                VARCHAR(32)  NOT NULL COMMENT '创建人名字',
    `modifier_id`                 BIGINT       NOT NULL COMMENT '修改操作人',
    `modifier_name`               VARCHAR(32)  NOT NULL COMMENT '修改操作人名字',
    `created`                     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='选品中心平台产品表';

CREATE TABLE `yt_product_change_log`
(
    `id`             bigint      NOT NULL COMMENT '日志ID',
    `product_id`     bigint      NOT NULL COMMENT '商品ID',
    `enterprise_id`  bigint      NOT NULL COMMENT '所属企业ID',
    `operation_type` tinyint     NOT NULL COMMENT '操作类型(1:创建,2:编辑,3:上架,4:下架)',
    `changed_fields` json                 DEFAULT NULL COMMENT '变更的字段及其前后值，JSON格式',
    `creator_id`     bigint      NOT NULL COMMENT '操作人ID',
    `creator_name`   varchar(32) NOT NULL COMMENT '操作人名称',
    `created`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `remark`         varchar(500)         DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='商品变更日志表';

CREATE TABLE `yt_supplier_product_tag`
(
    `id`            bigint      NOT NULL COMMENT '主键ID',
    `enterprise_id` bigint      NOT NULL COMMENT '所属企业id',
    `tag_name`      varchar(50) NOT NULL COMMENT '供应商商品标签名称',
    `tag_count`     bigint               DEFAULT 0 COMMENT '使用该标签的供应商商品数量',
    `delete_flag`   tinyint     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`    bigint      NOT NULL COMMENT '创建人',
    `creator_name`  varchar(32) NOT NULL COMMENT '创建人名字',
    `modifier_id`   bigint      NOT NULL COMMENT '修改操作人',
    `modifier_name` varchar(32) NOT NULL COMMENT '修改操作人名字',
    `created`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='供应商商品标签表';

CREATE TABLE `yt_supplier_product_tag_rel`
(
    `id`            bigint NOT NULL COMMENT '主键ID',
    `product_id`    bigint NOT NULL COMMENT '供应商商品ID',
    `tag_id`        bigint NOT NULL COMMENT '标签ID',
    `enterprise_id` bigint NOT NULL COMMENT '所属企业id',
    `creator_id`    bigint COMMENT '创建人',
    `creator_name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '创建人名字',
    `modifier_id`   bigint COMMENT '修改操作人',
    `modifier_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '修改操作人名字',
    `created`       datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`       datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_product_id` (`product_id`) USING BTREE,
    KEY `idx_tag_id` (`tag_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='供应商商品标签关联表';


CREATE TABLE `yt_supplier_product_category`
(
    `id`            BIGINT      NOT NULL COMMENT '主键ID',
    `enterprise_id` BIGINT      NOT NULL COMMENT '企业ID',
    `parent_id`     BIGINT      NOT NULL DEFAULT 0 COMMENT '父分类ID，顶级分类为0',
    `category_name` VARCHAR(50) NOT NULL COMMENT '分类名称',
    `weight`        INT                  DEFAULT 0 COMMENT '分类权重，用于排序，数值越大越靠前',
    `product_count` BIGINT               DEFAULT 0 COMMENT '该分类下的商品数量',
    `rate`          DECIMAL(10, 4)       DEFAULT 0 COMMENT '分类费率，可用于计算佣金等',
    `level`         TINYINT     NOT NULL COMMENT '分类层级：1-一级，2-二级，3-三级',
    `status`        TINYINT     NOT NULL COMMENT '状态：1-显示，0-隐藏',
    `delete_flag`   TINYINT              DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    `creator_id`    BIGINT               DEFAULT NULL COMMENT '创建人ID',
    `creator_name`  VARCHAR(64)          DEFAULT NULL COMMENT '创建人名称',
    `modifier_id`   BIGINT               DEFAULT NULL COMMENT '修改人ID',
    `modifier_name` VARCHAR(64)          DEFAULT NULL COMMENT '修改人名称',
    `created`       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_enterprise_id` (`enterprise_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_delete_flag` (`delete_flag`)
) ENGINE = InnoDB COMMENT ='供应商商品分类表';

CREATE TABLE `yt_file_folder`
(
    `id`            BIGINT   NOT NULL,
    `enterprise_id` BIGINT   NOT NULL COMMENT '企业ID',
    `folder_name`   VARCHAR(200)      DEFAULT NULL COMMENT '文件夹名字',
    `parent_id`     BIGINT   NOT NULL DEFAULT 0 COMMENT '父级id',
    `level`         TINYINT  NOT NULL DEFAULT 0 COMMENT '目录等级(0代表根节点)',
    `leaf`          TINYINT  NOT NULL DEFAULT 0 COMMENT '是否叶子节点(0:否 1:是)',
    `platform_type` TINYINT           DEFAULT NULL COMMENT '平台类型(0:渠道商端,1:平台端,2:供应商端)',
    `delete_flag`   TINYINT  NOT NULL DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    `creator_id`    BIGINT            DEFAULT NULL COMMENT '创建人ID',
    `creator_name`  VARCHAR(64)       DEFAULT NULL COMMENT '创建人名称',
    `modifier_id`   BIGINT            DEFAULT NULL COMMENT '修改人ID',
    `modifier_name` VARCHAR(64)       DEFAULT NULL COMMENT '修改人名称',
    `created`       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`       DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='文件路径表';

CREATE TABLE `yt_file_folder_relation`
(
    `id`               BIGINT NOT NULL COMMENT '唯一主键',
    `file_id`          BIGINT NOT NULL COMMENT '文件id',
    `file_folder_id`   BIGINT NOT NULL COMMENT '文件夹id',
    `file_folder_path` VARCHAR(200) DEFAULT NULL COMMENT '文件路径（文件夹id/文件名称）',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='文件路径关联表';

CREATE TABLE `yt_freight_template`
(
    `id`                  bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `enterprise_id`       bigint(20)   NOT NULL COMMENT '所属企业id',
    `template_name`       varchar(64)  NOT NULL COMMENT '模板名称',
    `region_name`         varchar(500) NOT NULL COMMENT '地区名称',
    `region_codes`        varchar(500)          DEFAULT NULL COMMENT '地区编码，多个用逗号分隔',
    `first_item`          int(11)      NOT NULL COMMENT '首件(个)',
    `first_item_fee`      bigint(20)   NOT NULL COMMENT '首件运费(分)',
    `additional_item`     int(11) COMMENT '续件(个)',
    `additional_item_fee` bigint(20) COMMENT '续件运费(分)',
    `status`              tinyint(1)   NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `delete_flag`         tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否已删除(0:否, 1:是)',
    `creator_id`          bigint(20)            DEFAULT NULL COMMENT '创建人',
    `creator_name`        varchar(64)           DEFAULT NULL COMMENT '创建人名字',
    `modifier_id`         bigint(20)            DEFAULT NULL COMMENT '修改操作人',
    `modifier_name`       varchar(64)           DEFAULT NULL COMMENT '修改操作人名字',
    `created`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_enterprise_id` (`enterprise_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='运费模板表';

ALTER TABLE yt_file
    ADD COLUMN platform_type TINYINT NOT NULL DEFAULT 0 COMMENT '平台类型(0:渠道商端,1:平台端,2:供应商端)' AFTER enterprise_id;
UPDATE yt_file
SET bus_type = 0
WHERE bus_type IS NULL;
ALTER TABLE yt_file
    MODIFY COLUMN bus_type TINYINT NOT NULL DEFAULT 0 COMMENT '业务类型(0:其他 1:头像 2:产品 3:导出任务)',
    MODIFY COLUMN file_type tinyint null comment '文件类型(0:其他 1:图片 2:视频 3:语音)';