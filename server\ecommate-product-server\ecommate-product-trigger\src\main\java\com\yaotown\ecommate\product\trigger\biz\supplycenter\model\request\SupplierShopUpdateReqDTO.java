package com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 供应商店铺更新请求DTO
 */
@Data
public class SupplierShopUpdateReqDTO {

    /**
     * 店铺id（可选，创建时不需要传）
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    @NotBlank(message = "店铺名称不能为空")
    private String shopName;

    /**
     * 平台类型
     */
    @NotBlank(message = "平台类型不能为空")
    private String platformType;

    /**
     * 联系人
     */
    private String contactsName;

    /**
     * 联系电话
     */
    private String contactsMobile;

    /**
     * 店铺网址
     */
    private String shopUrl;

    /**
     * APP Key (人工授权方式)
     */
    private String appKey;

    /**
     * 授权码 (人工授权方式)
     */
    private String appSecret;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 店铺logo
     */
    private String logoUrl;
} 