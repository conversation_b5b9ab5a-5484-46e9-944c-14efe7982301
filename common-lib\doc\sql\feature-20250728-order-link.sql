CREATE TABLE `yt_original_platform_order0`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_original_platform_order1`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_original_platform_order2`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_original_platform_order3`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_original_platform_order4`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_original_platform_order5`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_original_platform_order6`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_original_platform_order7`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_original_platform_order8`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_original_platform_order9`
(
    `id`           BIGINT      NOT NULL COMMENT '唯一主键',
    `ext_order_id` VARCHAR(64) NOT NULL COMMENT '外部订单id',
    `platform`     varchar(32) NOT NULL COMMENT '平台类型',
    `xyt_shop_id`  BIGINT      NOT NULL COMMENT '小亚通店铺Id',
    `content`      text        NOT NULL COMMENT '平台订单信息json',
    `delete_flag`  TINYINT     NOT NULL DEFAULT 0 COMMENT '是否已删除(0:否, 1:是)',
    `created`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_ext_order_id_platform` (`ext_order_id`, `platform`) USING BTREE
) ENGINE = InnoDB COMMENT ='原始平台订单表';

CREATE TABLE `yt_shop_auth`
(
    `id`                      BIGINT      NOT NULL COMMENT '主键ID',
    `ext_shop_id`             VARCHAR(64)          DEFAULT NULL COMMENT '外部店铺Id',
    `platform`                VARCHAR(32)          DEFAULT NULL COMMENT '平台类型',
    `ext_shop_name`           VARCHAR(64) NOT NULL COMMENT '外部店铺名称',
    `auth_mode`               TINYINT              DEFAULT NULL COMMENT '授权方式(1:人工授权方式 4:api授权方式)',
    `auth_status`             INT                  DEFAULT NULL COMMENT '授权状态(1:已授权 4:未授权)',
    `auth_time`               DATETIME             DEFAULT NULL COMMENT '授权时间',
    `expired_date`            DATETIME             DEFAULT NULL COMMENT '店铺到期时间',
    `app_key`                 VARCHAR(32)          DEFAULT NULL COMMENT 'app key(人工授权方式)',
    `app_secret`              VARCHAR(100)         DEFAULT NULL COMMENT '授权码(人工授权方式)',
    `access_token`            VARCHAR(2048)        DEFAULT NULL COMMENT 'api访问token(api授权方式)',
    `refresh_token`           VARCHAR(2048)        DEFAULT NULL COMMENT '刷新token(api授权方式)',
    `next_refresh_token_time` DATETIME             DEFAULT NULL COMMENT 'null，表示不需要刷新，根据个平台机制写入正确的刷新时间。下次刷新token时间(api授权方式)：不同的平台，要求的重新刷新的时间不同，则每次刷新时，自动记录下次需要刷新的时间。注意要比平台要求的时间缩短24小时，避免意外过期。',
    `delete_flag`             tinyint     NOT NULL DEFAULT '0' COMMENT '是否已删除(0:否, 1:是)',
    `created`                 DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`                 DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `uk_ext_shop_id_platform` (`ext_shop_id`, `platform`) USING BTREE,
    KEY `idx_auth_mode` (`auth_mode`) USING BTREE,
    KEY `idx_auth_status` (`auth_status`) USING BTREE,
    KEY `idx_app_key` (`app_key`) USING BTREE
) ENGINE = InnoDB COMMENT ='店铺授权表';

CREATE TABLE `yt_shop_auth_relation`
(
    `id`          BIGINT      NOT NULL COMMENT '主键ID',
    `yt_shop_id`  BIGINT      NOT NULL COMMENT '源汇通店铺Id',
    `xyt_shop_id` BIGINT      NOT NULL DEFAULT 0 COMMENT '小亚通店铺Id',
    `ext_shop_id` VARCHAR(64) NOT NULL COMMENT '外部店铺Id',
    `platform`    VARCHAR(32) NOT NULL COMMENT '平台类型',
    `delete_flag` TINYINT     NOT NULL DEFAULT '0' COMMENT '是否已删除(0:否, 1:是)',
    `created`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT ='店铺授权关联表';