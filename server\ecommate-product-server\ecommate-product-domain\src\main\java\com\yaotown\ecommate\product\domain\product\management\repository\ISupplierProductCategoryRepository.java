package com.yaotown.ecommate.product.domain.product.management.repository;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierProductCategoryEntity;
import com.yaotown.ecommate.product.domain.product.management.model.valobj.CategoryQueryVO;

import java.util.List;

/**
 * 供应商商品分类仓储接口
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
public interface ISupplierProductCategoryRepository {

    /**
     * 添加分类
     *
     * @param categoryEntity 分类实体
     * @return 分类ID
     */
    Long addCategory(SupplierProductCategoryEntity categoryEntity);

    /**
     * 更新分类
     *
     * @param categoryEntity 分类实体
     * @return 是否成功
     */
    boolean updateCategory(SupplierProductCategoryEntity categoryEntity);

    /**
     * 删除分类
     *
     * @param categoryId 分类ID
     * @param enterpriseId 企业ID
     * @return 是否成功
     */
    boolean deleteCategory(Long categoryId, Long enterpriseId);

    /**
     * 更新分类状态
     *
     * @param categoryId 分类ID
     * @param status 状态：1-显示，0-隐藏
     * @param enterpriseId 企业ID
     * @return 是否成功
     */
    boolean updateStatus(Long categoryId, Integer status, Long enterpriseId);

    /**
     * 根据ID获取分类
     *
     * @param categoryId 分类ID
     * @param enterpriseId 企业ID
     * @return 分类实体
     */
    SupplierProductCategoryEntity getCategory(Long categoryId, Long enterpriseId);

    /**
     * 获取分类列表
     *
     * @param enterpriseId 企业ID
     * @return 分类列表
     */
    List<SupplierProductCategoryEntity> listCategories(Long enterpriseId);

    /**
     * 获取显示状态的分类列表
     *
     * @param enterpriseId 企业ID
     * @return 分类列表
     */
    List<SupplierProductCategoryEntity> listVisibleCategories(Long enterpriseId);

    /**
     * 根据父分类ID获取子分类列表
     *
     * @param parentId 父分类ID
     * @param enterpriseId 企业ID
     * @return 子分类列表
     */
    List<SupplierProductCategoryEntity> listSubCategories(Long parentId, Long enterpriseId);

    /**
     * 根据父分类ID获取显示状态的子分类列表
     *
     * @param parentId 父分类ID
     * @param enterpriseId 企业ID
     * @return 子分类列表
     */
    List<SupplierProductCategoryEntity> listVisibleSubCategories(Long parentId, Long enterpriseId);


    List<SupplierProductCategoryEntity> selectVisibleByLevel(Long enterpriseId,Integer lever);
    /**
     * 分页查询分类
     *
     * @param queryModel 查询条件
     * @return 分页数据
     */
    PageData<SupplierProductCategoryEntity> pageQueryCategories(QueryModel<Long> queryModel);

    /**
     * 获取分类树形结构
     *
     * @param enterpriseId 企业ID
     * @return 分类树
     */
    List<SupplierProductCategoryEntity> getCategoryTree(Long enterpriseId);

    /**
     * 获取显示状态的分类树形结构
     *
     * @param enterpriseId 企业ID
     * @return 分类树
     */
    List<SupplierProductCategoryEntity> getVisibleCategoryTree(Long enterpriseId);

    /**
     * 更新分类商品数量
     *
     * @param categoryId 分类ID
     * @param count 变化的数量，正数为增加，负数为减少
     * @return 是否成功
     */
    boolean updateProductCount(Long categoryId, long count);

    /**
     * 检查分类名称是否存在
     *
     * @param categoryName 分类名称
     * @param parentId 父分类ID
     * @param enterpriseId 企业ID
     * @return 是否存在
     */
    boolean checkCategoryNameExists(String categoryName, Long parentId, Long enterpriseId);

    /**
     * 根据分类名称查询分类信息
     *
     * @param categoryName 分类名称
     * @param enterpriseId 企业ID
     * @return 分类实体
     */
    SupplierProductCategoryEntity getCategoryByName(String categoryName, Long enterpriseId);
    
    /**
     * 根据分类ID查询完整的分类路径（包含父级分类）
     *
     * @param categoryId 分类ID
     * @param enterpriseId 企业ID
     * @return 分类路径列表，按层级排序（1级在前，3级在后）
     */
    List<SupplierProductCategoryEntity> getCategoryPath(Long categoryId, Long enterpriseId);
} 