package com.yaotown.ecommate.trade.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 平台订单备注表
 */
@Data
@TableName(value = "yt_platform_order_memo")
public class PlatformOrderMemoPO {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 平台订单id
     */
    @TableField(value = "platform_order_id")
    private Long platformOrderId;

    /**
     * 外部订单ID
     */
    @TableField(value = "ext_order_id")
    private String extOrderId;

    /**
     * 订单来源平台
     */
    @TableField(value = "platform")
    private String platform;

    /**
     * 订单所属企业id
     */
    @TableField(value = "enterprise_id")
    private Long enterpriseId;

    /**
     * 订单供应商企业id
     */
    @TableField(value = "supplier_enterprise_id")
    private Long supplierEnterpriseId;

    /**
     * 订单当前企业id(分销拆单时，当前企业id为拆分后的企业id)
     */
    @TableField(value = "current_enterprise_id")
    private Long currentEnterpriseId;

    /**
     * 订单备注
     */
    @TableField(value = "order_memo")
    private String orderMemo;

    /**
     * 买家留言
     */
    @TableField(value = "buyer_message")
    private String buyerMessage;

    /**
     * 买家备注
     */
    @TableField(value = "buyer_memo")
    private String buyerMemo;

    /**
     * 卖家留言
     */
    @TableField(value = "seller_message")
    private String sellerMessage;

    /**
     * 卖家备注
     */
    @TableField(value = "seller_memo")
    private String sellerMemo;

    /**
     * 卖家备注旗帜,1、2、3、4、5分别对应红、黄、绿、蓝、紫
     */
    @TableField(value = "seller_flag")
    private Integer sellerFlag;

    /**
     * 是否已删除(0:否, 1:是)
     */
    @TableField(value = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建人
     */
    @TableField(value = "creator_id")
    private Long creatorId;

    /**
     * 创建人名字
     */
    @TableField(value = "creator_name")
    private String creatorName;

    /**
     * 修改操作人
     */
    @TableField(value = "modifier_id")
    private Long modifierId;

    /**
     * 修改操作人名字
     */
    @TableField(value = "modifier_name")
    private String modifierName;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Date created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Date updated;
}