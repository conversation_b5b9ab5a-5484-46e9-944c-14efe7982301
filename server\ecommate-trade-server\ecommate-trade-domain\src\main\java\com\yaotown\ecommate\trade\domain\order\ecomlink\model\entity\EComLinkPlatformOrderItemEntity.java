package com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EComLinkPlatformOrderItemEntity {
    /**
     * 平台订单项id
     */
    private Long platformOrderItemId;

    /**
     * 平台订单id
     */
    private Long platformOrderId;

    /**
     * 订单ID
     */
    private Long xytOrderId;

    /**
     * 小亚通订单商品ID
     */
    private Long xytOrderItemId;

    /**
     * 外部订单商品ID
     */
    private String extItemId;

    /**
     * 外部订单商品标题
     */
    private String extSkuTitle;

    /**
     * 外部商品ID
     */
    private String extNumIid;

    /**
     * 外部订单商品SKU ID
     */
    private String extSkuId;

    /**
     * 外部订单商品外部ID
     */
    private String extOuterId;

    /**
     * 外部订单ID
     */
    private String extOrderId;

    /**
     * 订单来源平台
     */
    private String platform;

    /**
     * 订单所属企业id
     */
    private Long enterpriseId;

    /**
     * 订单供应商企业id
     */
    private Long supplierEnterpriseId;

    /**
     * 订单当前企业id(分销拆单时，当前企业id为拆分后的企业id)
     */
    private Long currentEnterpriseId;

    /**
     * 小亚通租户ID
     */
    private Long xytTenantId;

    /**
     * 小亚通供应商租户ID
     */
    private Long xytSupplierTenantId;

    /**
     * 小亚通品牌租户ID
     */
    private Long xytBrandTenantId;

    /**
     * 小亚通当前租户ID
     */
    private Long xytCurrentTenantId;

    /**
     * 是否拆单(0:否 1:是)
     */
    private Integer isSplit;

    /**
     * 关联到物流信息表的单号
     */
    private Long xytLogisticsId;

    /**
     * 关联产品表的SKU_ID
     */
    private Long xytSkuId;

    /**
     * 铺货单id
     */
    private Long productListingId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * 铺货单sku id
     */
    private Long listingSkuId;

    /**
     * 关联产品表的outer_id
     */
    private String xytOuterId;

    /**
     * 关联到仓库商品表的goods_id
     */
    private Long xytWmsGoodsId;

    /**
     * 商品标题
     */
    private String skuTitle;

    /**
     * SKU规格 (颜色:红色;尺寸:32GB)
     */
    private String skuSpecChars;

    /**
     * 分类性质 0实物 1虚拟 2服务 3积分
     */
    private Integer catalogNature;

    /**
     * 商品图片
     */
    private String imageUrl;

    /**
     * 当SKU是虚拟/电子物品时，如饭票/影票/门票时，需要记录相应的票码等
     */
    private String ticketKeys;

    /**
     * 购买数量
     */
    private Integer num;

    /**
     * 记录预占库存的工单ID。如果没有预占或释放掉则设置为空，如果兑换成实际库存，则与原有预占工单ID相同。
     */
    private Long xytReserveStockId;

    /**
     * 单价
     */
    private Long price;

    /**
     * 商品积分
     */
    private Integer skuPoints;

    /**
     * 应付金额是指：购买数量的总应付金额
     */
    private Long totalFee;

    /**
     * 订单优惠金额
     */
    private Long discountFee;

    /**
     * 调整金额
     */
    private Long adjustFee;

    /**
     * 实收金额为所有购买数量的金额
     */
    private Long paidFee;

    /**
     * 退款金额
     */
    private Long refundFee;

    /**
     * 实付积分
     */
    private Integer paidPoints;

    /**
     * 运费
     */
    private Long postFee;

    /**
     * 订单商品状态(0:待支付 5:已支付 10:待成团 20:待发货 25:商城待发货 30:预售中 40:待审批 50:发货中 60:已发货 70:已签收 80:已拒收 90:已核销 999:已作废 9999:回收站中)
     */
    private Integer itemStatus;

    /**
     * 售后状态(0:无售后 1:退款中 2:退款关闭 3:退款成功 4:退货中 5:退货关闭 6:退货成功 7:其它售后中 8:部分退款成功)
     */
    private Integer warrantyStatus;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 完成时间
     */
    private Date doneTime;

    /**
     * 操作版本(1:默认 4:记录换货操作 5:组合商品原记录)
     */
    private Long doneVersion;

}
