package com.yaotown.ecommate.product.trigger.biz.management.model.request;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 运费计算请求DTO
 */
@Data
public class FreightCalculateReqDTO {

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /**
     * 地区编码
     */
    @NotBlank(message = "地区编码不能为空")
    private String regionCode;

    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不能为空")
    @Min(value = 1, message = "商品数量必须大于0")
    private Integer quantity;
} 