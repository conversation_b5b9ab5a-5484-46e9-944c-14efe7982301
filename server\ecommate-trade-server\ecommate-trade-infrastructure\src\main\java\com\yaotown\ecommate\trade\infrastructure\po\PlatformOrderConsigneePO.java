package com.yaotown.ecommate.trade.infrastructure.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 平台订单收件人表
 */
@Data
@TableName(value = "yt_platform_order_consignee")
public class PlatformOrderConsigneePO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 平台订单id
     */
    @TableField(value = "platform_order_id")
    private Long platformOrderId;

    /**
     * 外部订单ID
     */
    @TableField(value = "ext_order_id")
    private String extOrderId;

    /**
     * 订单来源平台
     */
    @TableField(value = "platform")
    private String platform;

    /**
     * 订单所属企业id
     */
    @TableField(value = "enterprise_id")
    private Long enterpriseId;

    /**
     * 订单供应商企业id
     */
    @TableField(value = "supplier_enterprise_id")
    private Long supplierEnterpriseId;

    /**
     * 订单当前企业id(分销拆单时，当前企业id为拆分后的企业id)
     */
    @TableField(value = "current_enterprise_id")
    private Long currentEnterpriseId;

    /**
     * 收件人姓名
     */
    @TableField(value = "receiver_name")
    private String receiverName;

    /**
     * 收件人国家
     */
    @TableField(value = "receiver_country")
    private String receiverCountry;

    /**
     * 收件人州/省
     */
    @TableField(value = "receiver_state")
    private String receiverState;

    /**
     * 收件人市
     */
    @TableField(value = "receiver_city")
    private String receiverCity;

    /**
     * 收件人区，县
     */
    @TableField(value = "receiver_district")
    private String receiverDistrict;

    /**
     * 收件人街道，乡镇
     */
    @TableField(value = "receiver_town")
    private String receiverTown;

    /**
     * 收件人详细地址
     */
    @TableField(value = "receiver_address")
    private String receiverAddress;

    /**
     * 收件人邮政编码
     */
    @TableField(value = "receiver_zip")
    private String receiverZip;

    /**
     * 收件人手机号
     */
    @TableField(value = "receiver_mobile")
    private String receiverMobile;

    /**
     * 收件人手机号
     */
    @TableField(value = "receiver_phone")
    private String receiverPhone;

    /**
     * 完成时间
     */
    @TableField(value = "done_time")
    private Date doneTime;

    /**
     * 操作版本(1:默认 4:记录换货操作 5:组合商品原记录)
     */
    @TableField(value = "done_version")
    private Long doneVersion;

    /**
     * 是否已删除(0:否, 1:是)
     */
    @TableField(value = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Date created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Date updated;
}