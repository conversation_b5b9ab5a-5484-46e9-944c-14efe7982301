package com.yaotown.ecommate.product.infrastructure.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.common.base.core.util.CommonStreamUtil;
import com.yaotown.ecommate.common.id.util.IdUtils;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopPlatformTypeEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.valobj.ShopInfoSearchVO;
import com.yaotown.ecommate.product.domain.enterprise.repository.IShopInfoRepository;
import com.yaotown.ecommate.product.domain.product.erplink.model.entity.ErpShopInfoEntity;
import com.yaotown.ecommate.product.infrastructure.convert.ShopInfoConvert;
import com.yaotown.ecommate.product.infrastructure.mapper.ShopInfoMapper;
import com.yaotown.ecommate.product.infrastructure.mapper.ThirdAccountInfoMapper;
import com.yaotown.ecommate.product.infrastructure.po.ShopInfoPO;
import com.yaotown.ecommate.product.infrastructure.po.ThirdAccountInfoPO;
import com.yaotown.ecommate.product.types.enums.shop.ThirdSystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Slf4j
@AllArgsConstructor
public class ShopInfoRepository implements IShopInfoRepository {

    private final ShopInfoMapper shopInfoMapper;
    private final ThirdAccountInfoMapper thirdAccountInfoMapper;

    @Override
    public List<ShopInfoEntity> selectShopInfoList(Long accountId) {
        List<ShopInfoPO> shopInfo = shopInfoMapper.selectShopInfoList(accountId, null);
        return CommonStreamUtil.transList(shopInfo, ShopInfoConvert.INSTANCE::shopInfoEntity);
    }

    @Override
    public ShopInfoEntity findShopInfo(Long shopId) {
        return ShopInfoConvert.INSTANCE.shopInfoEntity(shopInfoMapper.selectById(shopId));
    }

    @Override
    public List<ShopInfoEntity> selectShopInfoList(Long accountId, List<Long> shopIds) {
        return CommonStreamUtil.transList(shopInfoMapper.selectShopInfoList(accountId, shopIds), ShopInfoConvert.INSTANCE::shopInfoEntity);
    }

    /**
     * @param list
     * @param enterpriseId 企业id
     */
    @Override
    public void syncErpShopInfo(List<ErpShopInfoEntity> list, Long enterpriseId, Long accountId) {

        List<ShopInfoPO> shopInfoPOS = shopInfoMapper.selectShopInfo(accountId, CommonStreamUtil.transList(list, ErpShopInfoEntity::getShopId));
        ThirdAccountInfoPO thirdAccountInfo = thirdAccountInfoMapper.findThirdAccountInfoByAccountId(accountId, ThirdSystemTypeEnum.XYT.getId());
        Map<String, ShopInfoPO> shopInfoPOMap = CommonStreamUtil.toMap(shopInfoPOS, ShopInfoPO::getXytShopId);
        List<ShopInfoPO> insertList = new ArrayList<>();
        List<ShopInfoPO> updateList = new ArrayList<>();
        list.forEach(data -> {
            //店铺去重
            if (shopInfoPOMap.containsKey(data.getShopId())) {
                ShopInfoPO shopInfoPO = shopInfoPOMap.get(data.getShopId());
                ShopInfoConvert.INSTANCE.toErpShopInfo(data, shopInfoPO);
                updateList.add(shopInfoPO);
            } else {
                ShopInfoPO info = ShopInfoConvert.INSTANCE.toErpShopInfo(data);
                info.setShopId(IdUtils.nextId());
                info.setEnterpriseId(enterpriseId);
                info.setAccountId(accountId);
                info.setXytShopId(data.getShopId());
                if (Objects.nonNull(thirdAccountInfo) && StringUtils.isNotBlank(thirdAccountInfo.getThirdSystemTenantId())) {
                    info.setXytTenantId(Long.valueOf(thirdAccountInfo.getThirdSystemTenantId()));
                }
                info.setCreated(data.getCreateTime());
                insertList.add(info);
            }
        });

        if (CollUtil.isNotEmpty(insertList)) {
            shopInfoMapper.insert(insertList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            shopInfoMapper.updateById(updateList);
        }

    }

    @Override
    public PageData<ShopInfoEntity> selectShopInfoLists(QueryModel<Void> queryModel, String platformType, Long accountId) {
        return queryModel.queryPageData(() -> shopInfoMapper.selectAllShopInfo(platformType, accountId), ShopInfoConvert.INSTANCE::shopInfoEntity);
    }

    @Override
    public List<ShopInfoEntity> selectShopList(Long accountId) {
        List<ShopInfoPO> shopInfo = shopInfoMapper.selectManyShop(accountId);
        return CommonStreamUtil.transList(shopInfo, ShopInfoConvert.INSTANCE::shopInfoEntity);
    }

    @Override
    public PageData<ShopInfoEntity> selectShopInfoList(QueryModel<ShopInfoSearchVO> queryModel) {
        ShopInfoSearchVO shopInfoSearchVO = queryModel.getCheckParam().orElse(new ShopInfoSearchVO());
        return queryModel.queryPageData(() -> shopInfoMapper.selectShopInfoBySearchVO(shopInfoSearchVO), ShopInfoConvert.INSTANCE::shopInfoEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccessToken(Long enterpriseId, Long shopId, String accessToken, String refreshToken, Integer expiresInSeconds) {
        Date expiredDate = DateUtil.offsetSecond(new Date(), expiresInSeconds);
        int updateCount = shopInfoMapper.updateAccessTokenInfo(shopId, accessToken, refreshToken, expiredDate);
        if (updateCount == 0) {
            log.warn("更新店铺访问令牌失败，店铺ID: {}", shopId);
        }
    }

    @Override
    public List<ShopInfoEntity> selectByPlatformType(List<String> platformTypes, Long accountId, boolean filterAuthorized) {
        List<ShopInfoPO> shopInfo = shopInfoMapper.selectByPlatformType(platformTypes, accountId, filterAuthorized);
        return CommonStreamUtil.transList(shopInfo, ShopInfoConvert.INSTANCE::shopInfoEntity);
    }

    @Override
    public List<ShopPlatformTypeEntity> selectShopType(Long accountId) {
        List<ShopPlatformTypeEntity> typeEntities = shopInfoMapper.selectShopType(accountId);
        return typeEntities;
    }

    @Override
    public Long saveShopInfo(ShopInfoEntity shopInfo) {
        ShopInfoPO shopInfoPO = ShopInfoConvert.INSTANCE.toShopInfoPO(shopInfo);

        // 如果是新增店铺，生成ID
        if (shopInfoPO.getShopId() == null) {
            shopInfoPO.setShopId(IdUtils.nextId());
        }

        // 设置创建和修改时间
        if (shopInfoPO.getCreated() == null) {
            shopInfoPO.setCreated(new Date());
        }
        shopInfoPO.setUpdated(new Date());

        // 默认设置为有效状态，如果没有指定
        if (shopInfoPO.getState() == null) {
            shopInfoPO.setState(1); // 1代表有效
        }

        // 设置删除标记为未删除
        shopInfoPO.setDeleteFlag(0);

        // 保存到数据库
        shopInfoMapper.insert(shopInfoPO);

        return shopInfoPO.getShopId();
    }

    /**
     * 更新店铺信息
     * @param shopInfo 店铺信息
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateShopInfo(ShopInfoEntity shopInfo) {
        if (shopInfo == null || shopInfo.getShopId() == null) {
            log.error("更新店铺信息失败，参数异常: {}", shopInfo);
            return false;
        }
        
        try {
            // 转换为PO
            ShopInfoPO shopInfoPO = ShopInfoConvert.INSTANCE.toShopInfoPO(shopInfo);
            // 执行更新操作
            int rows = shopInfoMapper.updateById(shopInfoPO);
            return rows > 0;
        } catch (Exception e) {
            log.error("更新店铺信息异常", e);
            return false;
        }
    }
}
