package com.yaotown.ecommate.product.infrastructure.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 小亚通店铺信息实时表实体
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
@Data
@TableName("ods_xyt_shop_info_realtime")
public class OdsXytShopInfoRealtimePO {
    
    /**
     * 店铺id
     */
    @TableId
    private Long shopId;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date modifiedTime;
    
    /**
     * 状态:参考枚举类型UseState
     */
    private Integer state;
    
    /**
     * 外部店铺Id
     */
    private String exShopId;
    
    /**
     * 店铺到期时间
     */
    private Date expiredDate;
    
    /**
     * 租户id
     */
    private Long tenantId;
    
    /**
     * 店铺类型：参考枚举类型ShopType
     */
    private Integer shopType;
    
    /**
     * 名称
     */
    private String shopName;
    
    /**
     * 平台类型：参考枚举类型PlatformType
     */
    private String platformType;
    
    /**
     * 联系人
     */
    private String contactsName;
    
    /**
     * 联系电话,手机/固话
     */
    private String contactsMobile;
    
    /**
     * 店铺网址
     */
    private String shopUrl;
    
    /**
     * 授权方式：参考枚举类型AuthMode
     */
    private Integer authMode;
    
    /**
     * 授权状态：参考枚举类型AuthStatus
     */
    private Integer authStatus;
    
    /**
     * 授权时间
     */
    private Date authTime;
    
    /**
     * app key(人工授权方式)
     */
    private String appKey;
    
    /**
     * 授权码(人工授权方式)
     */
    private String appSecret;
    
    /**
     * api访问token(api授权方式)
     */
    private String accessToken;
    
    /**
     * 刷新token(api授权方式)
     */
    private String refreshToken;
    
    /**
     * 下次刷新token时间
     */
    private Date nextRefreshTokenTime;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * 店铺logo（shop_type=收单通）
     */
    private String logoUrl;
    
    /**
     * 账号id（shop_type=收单通）
     */
    private Long accountId;
}
