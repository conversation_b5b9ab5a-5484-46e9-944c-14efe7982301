2025-08-01 14:34:34.763 [main] WARN  [ecommate-product-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-product-server] & group[DEFAULT_GROUP]
2025-08-01 14:34:34.769 [main] WARN  [ecommate-product-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-product-server.yaml] & group[DEFAULT_GROUP]
2025-08-01 14:34:38.950 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:38.971 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:38.973 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$946/0x000001415586bea8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:38.979 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:39.053 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration' of type [com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [environmentSetup] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-01 14:34:39.060 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq.enhance-com.yaotown.sdk.mq.properties.RocketEnhanceProperties' of type [com.yaotown.sdk.mq.properties.RocketEnhanceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:39.070 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:39.074 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:39.316 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:39.321 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:39.325 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:39.365 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-01 14:34:39.369 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:39.706 [main] WARN  [ecommate-product-server] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 14:34:43.753 [main] WARN  [ecommate-product-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.product.infrastructure.po.listing.ProductCollectionPO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 14:36:35.113 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 113189
2025-08-01 14:36:35.129 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 113427
2025-08-01 14:36:35.145 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 113605
2025-08-01 14:36:35.161 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 113781
2025-08-01 14:36:35.180 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 113994
2025-08-01 14:40:17.809 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 221654
2025-08-01 14:43:41.933 [Thread-3] WARN  [ecommate-product-server] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 14:43:41.933 [Thread-5] WARN  [ecommate-product-server] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-01 14:43:41.934 [Thread-5] WARN  [ecommate-product-server] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-01 14:43:41.936 [Thread-3] WARN  [ecommate-product-server] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-08-01 14:43:41.970 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-08-01 14:43:55.174 [main] WARN  [ecommate-product-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-product-server] & group[DEFAULT_GROUP]
2025-08-01 14:43:55.180 [main] WARN  [ecommate-product-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-product-server.yaml] & group[DEFAULT_GROUP]
2025-08-01 14:43:59.446 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.450 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.452 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$946/0x00000216da866f20] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.457 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.529 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration' of type [com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [environmentSetup] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-01 14:43:59.534 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq.enhance-com.yaotown.sdk.mq.properties.RocketEnhanceProperties' of type [com.yaotown.sdk.mq.properties.RocketEnhanceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.539 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.543 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.777 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.782 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.785 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:43:59.829 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-01 14:43:59.834 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:44:00.202 [main] WARN  [ecommate-product-server] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 14:44:04.344 [main] WARN  [ecommate-product-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.product.infrastructure.po.listing.ProductCollectionPO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 14:45:20.108 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 77724
2025-08-01 14:45:20.124 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 77928
2025-08-01 14:45:20.142 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 78111
2025-08-01 14:45:20.160 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 78312
2025-08-01 14:45:20.175 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 78504
2025-08-01 14:46:53.860 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 69513
2025-08-01 14:46:53.863 [XNIO-1 task-3] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 67443
2025-08-01 14:48:54.556 [XNIO-1 task-3] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 120414
2025-08-01 14:48:54.573 [XNIO-1 task-3] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 120408
2025-08-01 14:51:51.261 [XNIO-1 task-3] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 164119
2025-08-01 14:58:37.244 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 405743
2025-08-01 15:06:10.237 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 406527
2025-08-01 15:09:32.777 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 201669
