package com.yaotown.ecommate.product.infrastructure.convert.management;

import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierFreightTemplateEntity;
import com.yaotown.ecommate.product.infrastructure.po.management.SupplierFreightTemplatePO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T14:34:21+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.14 (Alibaba)"
)
public class SupplierFreightTemplateConvertImpl implements SupplierFreightTemplateConvert {

    @Override
    public SupplierFreightTemplatePO convertToPO(SupplierFreightTemplateEntity entity) {
        if ( entity == null ) {
            return null;
        }

        SupplierFreightTemplatePO supplierFreightTemplatePO = new SupplierFreightTemplatePO();

        supplierFreightTemplatePO.setId( entity.getId() );
        supplierFreightTemplatePO.setEnterpriseId( entity.getEnterpriseId() );
        supplierFreightTemplatePO.setTemplateName( entity.getTemplateName() );
        supplierFreightTemplatePO.setRegionName( entity.getRegionName() );
        supplierFreightTemplatePO.setRegionCodes( entity.getRegionCodes() );
        supplierFreightTemplatePO.setFirstItem( entity.getFirstItem() );
        supplierFreightTemplatePO.setFirstItemFee( entity.getFirstItemFee() );
        supplierFreightTemplatePO.setAdditionalItem( entity.getAdditionalItem() );
        supplierFreightTemplatePO.setAdditionalItemFee( entity.getAdditionalItemFee() );
        supplierFreightTemplatePO.setStatus( entity.getStatus() );
        supplierFreightTemplatePO.setCreatorId( entity.getCreatorId() );
        supplierFreightTemplatePO.setCreatorName( entity.getCreatorName() );
        supplierFreightTemplatePO.setModifierId( entity.getModifierId() );
        supplierFreightTemplatePO.setModifierName( entity.getModifierName() );
        supplierFreightTemplatePO.setCreated( entity.getCreated() );
        supplierFreightTemplatePO.setUpdated( entity.getUpdated() );

        return supplierFreightTemplatePO;
    }

    @Override
    public SupplierFreightTemplateEntity convertToEntity(SupplierFreightTemplatePO po) {
        if ( po == null ) {
            return null;
        }

        SupplierFreightTemplateEntity supplierFreightTemplateEntity = new SupplierFreightTemplateEntity();

        supplierFreightTemplateEntity.setId( po.getId() );
        supplierFreightTemplateEntity.setEnterpriseId( po.getEnterpriseId() );
        supplierFreightTemplateEntity.setTemplateName( po.getTemplateName() );
        supplierFreightTemplateEntity.setRegionName( po.getRegionName() );
        supplierFreightTemplateEntity.setRegionCodes( po.getRegionCodes() );
        supplierFreightTemplateEntity.setFirstItem( po.getFirstItem() );
        supplierFreightTemplateEntity.setFirstItemFee( po.getFirstItemFee() );
        supplierFreightTemplateEntity.setAdditionalItem( po.getAdditionalItem() );
        supplierFreightTemplateEntity.setAdditionalItemFee( po.getAdditionalItemFee() );
        supplierFreightTemplateEntity.setStatus( po.getStatus() );
        supplierFreightTemplateEntity.setCreatorId( po.getCreatorId() );
        supplierFreightTemplateEntity.setCreatorName( po.getCreatorName() );
        supplierFreightTemplateEntity.setModifierId( po.getModifierId() );
        supplierFreightTemplateEntity.setModifierName( po.getModifierName() );
        supplierFreightTemplateEntity.setCreated( po.getCreated() );
        supplierFreightTemplateEntity.setUpdated( po.getUpdated() );

        return supplierFreightTemplateEntity;
    }

    @Override
    public List<SupplierFreightTemplateEntity> convertToEntityList(List<SupplierFreightTemplatePO> po) {
        if ( po == null ) {
            return null;
        }

        List<SupplierFreightTemplateEntity> list = new ArrayList<SupplierFreightTemplateEntity>( po.size() );
        for ( SupplierFreightTemplatePO supplierFreightTemplatePO : po ) {
            list.add( convertToEntity( supplierFreightTemplatePO ) );
        }

        return list;
    }
}
