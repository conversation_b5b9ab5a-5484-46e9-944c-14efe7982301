package com.yaotown.ecommate.product.domain.enterprise.repository;

import java.util.Map;

/**
 * 小亚通店铺信息仓库接口
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
public interface IXytShopInfoRepository {

    /**
     * 根据AppId和平台类型查询小亚通店铺信息
     *
     * @param appId 应用ID
     * @param secret 应用密钥
     * @param platformType 平台类型
     * @return 店铺信息
     */
    Map<String, Object> findShopInfoByAppIdAndPlatformType(String appId,  String secret,String platformType);
} 