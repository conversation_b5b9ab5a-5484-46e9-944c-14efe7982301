package com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.common.base.core.util.CommonStreamUtil;
import com.yaotown.ecommate.common.core.entity.KeyValue;
import com.yaotown.ecommate.common.core.enums.DeleteFlagEnum;
import com.yaotown.ecommate.trade.domain.common.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.adapter.port.IECommerceLinkDataLinkPort;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.ECommerceOrderModel;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.ErpOrderAggregate;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.*;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.ErpOrderInfoSearchVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.PlatformParamVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.repository.IErpECommerceOrderRepository;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.AbstractChannelConnectorDecorator;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.IChannelConnector;
import com.yaotown.ecommate.trade.types.enums.ExpressServiceProviderEnum;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * erp平台连接器
 *
 * <AUTHOR>
 * @date 2025/6/12
 */
@RequiredArgsConstructor
@Slf4j
public class ErpChannelConnectorDecorator<T extends ECommerceOrderModel> extends AbstractChannelConnectorDecorator<ErpOrderAggregate, T> {

    private final static IChannelConnector.GetType[] getTypes = Arrays.stream(GetTypeEnum.values())
            .map(getType -> IChannelConnector.GetType.builder()
                    .value(getType.value)
                    .name(getType.name)
                    .build())
            .toArray(IChannelConnector.GetType[]::new);

    private final IECommerceLinkDataLinkPort ecommerceLinkDataLinkPort;

    @Override
    public PlatformParamVO getPlatformParam() {
        return super.getPlatformParam()
                .setGetTypes(getTypes)
                .setDefaultPageSize(100)
                .setIntervalSeconds(8 * 3600)
                .setAheadSeconds(300)
                ;
    }

    @Override
    public Integer getOrderCount(Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        ErpOrderInfoSearchVO erpOrderInfoSearchVO = new ErpOrderInfoSearchVO()
                .setXytTenantId(shopInfo.getXytTenantId())
                .setXytShopId(shopInfo.getXytShopId())
                .setStartTime(beginDate)
                .setEndTime(endDate);
        PageData<ErpOrderInfoEntity> pageData = erpECommerceOrderRepository.selectOrderByPage(new QueryModel<>(1, 1, erpOrderInfoSearchVO));
        return (int) pageData.getPageTotal();
    }

    @Override
    public KeyValue<String, List<ErpOrderAggregate>> getOrderPageByCursor(String cursor, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        return new KeyValue<>(cursor, List.of());
    }

    @Override
    protected List<ErpOrderAggregate> doGetOrderPage(int pageIndex, int pageSize, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        ErpOrderInfoSearchVO erpOrderInfoSearchVO = new ErpOrderInfoSearchVO()
                .setXytTenantId(shopInfo.getXytTenantId())
                .setXytShopId(shopInfo.getXytShopId())
                .setStartTime(beginDate)
                .setEndTime(endDate);
        String now = DateUtil.now();
        PageData<ErpOrderInfoEntity> pageData = erpECommerceOrderRepository.selectOrderByPage(new QueryModel<>(pageIndex, pageSize, erpOrderInfoSearchVO));
        List<Long> erpOrderInfoIds = CommonStreamUtil.transList(pageData.getPageContents(), ErpOrderInfoEntity::getOrderId);

        // 查询订单项
        List<ErpOrderItemEntity> erpOrderItemEntities = ecommerceLinkDataLinkPort.queryOrderItems(shopInfo.getXytTenantId(), erpOrderInfoIds);
        //获取订单快递信息（面单信息）
        List<ErpOrderLogisticsEntity> orderLogisticsEntities = ecommerceLinkDataLinkPort.queryOrderLogistics(shopInfo.getXytTenantId(), erpOrderInfoIds);
        Map<Long, List<ErpOrderLogisticsEntity>> orderLogisticsGroup = CommonStreamUtil.group(orderLogisticsEntities, ErpOrderLogisticsEntity::getOrderId);
        Map<Long, List<ErpOrderItemEntity>> erpOrderItemGroup = CommonStreamUtil.group(erpOrderItemEntities, ErpOrderItemEntity::getOrderId);
        List<ErpOrderAggregate> erpOrderAggregates = CommonStreamUtil.transList(pageData.getPageContents(), erpOrderInfoEntity -> {
            ErpOrderAggregate erpOrderAggregate = new ErpOrderAggregate();
            erpOrderAggregate.setEnterpriseId(shopInfo.getEnterpriseId());
            erpOrderAggregate.setPlatform(shopInfo.getPlatformType());
            erpOrderAggregate.setShopId(shopInfo.getShopId());
            erpOrderAggregate.setEcommerceOrderId(erpOrderInfoEntity.getExtOrderId());
            erpOrderAggregate.setSyncTime(now);
            erpOrderAggregate.setErpOrderInfo(erpOrderInfoEntity);
            erpOrderAggregate.setErpOrderItems(erpOrderItemGroup.getOrDefault(erpOrderInfoEntity.getOrderId(), List.of()));
            erpOrderAggregate.setErpOrderLogistics(orderLogisticsGroup.getOrDefault(erpOrderInfoEntity.getOrderId(), List.of()));
            return erpOrderAggregate;
        });
        return erpOrderAggregates;
    }

    @Override
    public EComLinkPlatformOrderEntity parsePlatformOrder(ErpOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        ErpOrderInfoEntity erpOrderInfo = ecommerceOrder.getErpOrderInfo();
        EComLinkPlatformOrderEntity ecomLinkPlatformOrderEntity = EComLinkPlatformOrderEntity.builder()
                .platformOrderId(null)
                .xytOrderId(erpOrderInfo.getOrderId())
                .enterpriseId(shopInfo.getEnterpriseId())
                // TODO 供应商企业还未在本系统中，暂时用小亚通供应商租户id代替
                .supplierEnterpriseId(erpOrderInfo.getSupplierTenantId())
                .currentEnterpriseId(shopInfo.getEnterpriseId())
                .xytTenantId(erpOrderInfo.getTenantId())
                .xytSupplierTenantId(erpOrderInfo.getSupplierTenantId())
                .xytBrandTenantId(erpOrderInfo.getBrandTenantId())
                .xytCurrentTenantId(erpOrderInfo.getCurrentTenantId())
                .extOrderId(erpOrderInfo.getExtOrderId())
                .wmsOrderId(erpOrderInfo.getWmsOrderId())
                .orderKind(erpOrderInfo.getOrderKind())
                .extAppId(erpOrderInfo.getExtAppId())
                .payTime(erpOrderInfo.getPayTime())
                .createTime(erpOrderInfo.getCreateTime())
                .updateTime(erpOrderInfo.getUpdateTime())
                .wmsCreateTime(erpOrderInfo.getWmsCreateTime())
                .payType(erpOrderInfo.getPayType())
                .totalFee(erpOrderInfo.getTotalFee())
                .discountFee(erpOrderInfo.getDiscountFee())
                .postFee(erpOrderInfo.getPostFee())
                .paidFee(erpOrderInfo.getPaidFee())
                .paidPoints(erpOrderInfo.getPaidPoints())
                .paymentTradeId(erpOrderInfo.getPaymentTradeId())
                .adjustFee(erpOrderInfo.getAdjustFee())
                .orderStatus(erpOrderInfo.getOrderStatus())
                .warrantyStatus(erpOrderInfo.getWarrantyStatus())
                .revokeStatus(erpOrderInfo.getRevokeStatus())
                .orderStatusReason(erpOrderInfo.getOrderStatusReason())
                .platform(shopInfo.getPlatformType())
                .shopId(shopInfo.getShopId())
                .exShopId(shopInfo.getExShopId())
                .xytShopId(shopInfo.getXytShopId())
                .buyerNick(erpOrderInfo.getBuyerNick())
                .buyerId(erpOrderInfo.getBuyerId())
                .refereeId(erpOrderInfo.getRefereeId())
                .refereeNick(erpOrderInfo.getRefereeNick())
                .shippingType(erpOrderInfo.getShippingType())
                .skuNum(erpOrderInfo.getSkuNum())
                .skuBrief(erpOrderInfo.getSkuBrief())
                .wmsId(erpOrderInfo.getWmsId())
                .xytCreateOp(erpOrderInfo.getCreateOp())
                .syncTime(erpOrderInfo.getSyncTime())
                .doneTime(erpOrderInfo.getDoneTime())
                .deliveryTime(erpOrderInfo.getDeliveryTime())
                .doneVersion(erpOrderInfo.getDoneVersion())
                .xytReserveStockId(erpOrderInfo.getReserveStockId())
                .build();
        return ecomLinkPlatformOrderEntity;
    }

    @Override
    public List<EComLinkPlatformOrderItemEntity> parsePlatformOrderItems(ErpOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        ErpOrderInfoEntity erpOrderInfo = ecommerceOrder.getErpOrderInfo();
        if (CollUtil.isEmpty(ecommerceOrder.getErpOrderItems())) {
            return Collections.emptyList();
        }
        return CommonStreamUtil.transList(ecommerceOrder.getErpOrderItems(), erpOrderItemEntity ->
                EComLinkPlatformOrderItemEntity.builder()
                        .platformOrderItemId(null)
                        .platformOrderId(null)
                        .xytOrderId(erpOrderItemEntity.getOrderId())
                        .xytOrderItemId(erpOrderItemEntity.getOrderItemId())
                        .extItemId(erpOrderItemEntity.getExtItemId())
                        .extSkuTitle(erpOrderItemEntity.getExtSkuTitle())
                        .extNumIid(erpOrderItemEntity.getExtNumIid())
                        .extSkuId(erpOrderItemEntity.getExtSkuId())
                        .extOuterId(erpOrderItemEntity.getExtOuterId())
                        .extOrderId(erpOrderItemEntity.getExtOrderId())
                        .platform(shopInfo.getPlatformType())
                        .enterpriseId(shopInfo.getEnterpriseId())
                        .supplierEnterpriseId(erpOrderInfo.getSupplierTenantId())
                        .currentEnterpriseId(shopInfo.getEnterpriseId())
                        .xytTenantId(erpOrderItemEntity.getTenantId())
                        .xytSupplierTenantId(erpOrderInfo.getSupplierTenantId())
                        .xytBrandTenantId(erpOrderInfo.getBrandTenantId())
                        .xytCurrentTenantId(erpOrderItemEntity.getCurrentTenantId())
                        .isSplit(erpOrderItemEntity.getIsSplit())
                        .xytLogisticsId(erpOrderItemEntity.getLogisticsId())
                        .xytSkuId(erpOrderItemEntity.getSkuId())
                        // TODO 暂无数据，使用小亚通产品代替
                        .productListingId(0L)
                        .productId(0L)
                        .skuId(erpOrderItemEntity.getSkuId())
                        .listingSkuId(0L)
                        .xytOuterId(erpOrderItemEntity.getOuterId())
                        .xytWmsGoodsId(erpOrderItemEntity.getWmsGoodsId())
                        .skuTitle(erpOrderItemEntity.getSkuTitle())
                        .skuSpecChars(erpOrderItemEntity.getSkuSpecChars())
                        .catalogNature(erpOrderItemEntity.getCatalogNature())
                        .imageUrl(erpOrderItemEntity.getImageUrl())
                        .ticketKeys(erpOrderItemEntity.getTicketKeys())
                        .num(erpOrderItemEntity.getNum())
                        .xytReserveStockId(erpOrderItemEntity.getReserveStockId())
                        .price(erpOrderItemEntity.getPrice())
                        .skuPoints(erpOrderItemEntity.getSkuPoints())
                        .totalFee(erpOrderItemEntity.getTotalFee())
                        .discountFee(erpOrderItemEntity.getDiscountFee())
                        .adjustFee(erpOrderItemEntity.getAdjustFee())
                        .paidFee(erpOrderItemEntity.getPaidFee())
                        .refundFee(erpOrderItemEntity.getRefundFee())
                        .paidPoints(erpOrderItemEntity.getPaidPoints())
                        .postFee(erpOrderItemEntity.getPostFee())
                        .itemStatus(erpOrderItemEntity.getItemStatus())
                        .warrantyStatus(erpOrderItemEntity.getWarrantyStatus())
                        .refundTime(erpOrderItemEntity.getRefundTime())
                        .createTime(erpOrderItemEntity.getCreateTime())
                        .doneTime(erpOrderItemEntity.getDoneTime())
                        .doneVersion(erpOrderItemEntity.getDoneVersion().longValue())
                        .build());
    }

    @Override
    public List<EComLinkPlatformOrderLogisticsEntity> parsePlatformOrderLogistics(ErpOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        ErpOrderInfoEntity erpOrderInfo = ecommerceOrder.getErpOrderInfo();
        if (CollUtil.isEmpty(ecommerceOrder.getErpOrderLogistics())) {
            return Collections.emptyList();
        }
        return CommonStreamUtil.transList(ecommerceOrder.getErpOrderLogistics(), erpOrderLogisticsEntity ->
                 EComLinkPlatformOrderLogisticsEntity.builder()
                         .platformOrderLogisticsId( null)
                          .xytOrderLogisticsId(erpOrderLogisticsEntity.getLogisticsId())
                         .xytTenantId(erpOrderLogisticsEntity.getTenantId())
                         .xytCurrentTenantId(erpOrderLogisticsEntity.getCurrentTenantId())
                         .xytOrderId(erpOrderLogisticsEntity.getOrderId())
                         .platformOrderId(ecommerceOrder.getPlatformOrderId())
                         .expressServiceProvider(ExpressServiceProviderEnum.KUAI_DI_100.getCode())
                         .companyName(erpOrderLogisticsEntity.getCompanyName())
                         .companyCode(erpOrderLogisticsEntity.getCompanyCode())
                         .deliveryNo(erpOrderLogisticsEntity.getDeliveryNo())
                         .deliveryStatus(erpOrderLogisticsEntity.getDeliveryStatus())
                         .state(erpOrderLogisticsEntity.getState())
                         .deleteFlag(DeleteFlagEnum.UN_DELETE.getId())
                          .deliveryTime(erpOrderLogisticsEntity.getDeliveryTime())
                          .shippingTime(erpOrderLogisticsEntity.getShippingTime())
                         .signTime(erpOrderLogisticsEntity.getSignTime())
                         .doneTime(erpOrderLogisticsEntity.getDoneTime())
                         .shippingMemo(erpOrderLogisticsEntity.getShippingMemo())
                         .signRecipient(erpOrderLogisticsEntity.getSignRecipient())
                         .build());
    }

    @Override
    public EComLinkPlatformOrderMemoEntity parsePlatformOrderMemo(ErpOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return null;
    }

    @Override
    public EComLinkPlatformOrderConsigneeEntity parsePlatformOrderConsignee(ErpOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        return null;
    }


    enum GetTypeEnum {
        createAndUpdate(0, "创建时间与更新时间"),
        ;
        private final int value;
        private final String name;

        GetTypeEnum(int value, String name) {
            this.value = value;
            this.name = name;
        }

        static GetTypeEnum toEnum(int value) {
            for (GetTypeEnum getType : values()) {
                if (getType.value == value)
                    return getType;
            }
            return createAndUpdate;
        }
    }

}
