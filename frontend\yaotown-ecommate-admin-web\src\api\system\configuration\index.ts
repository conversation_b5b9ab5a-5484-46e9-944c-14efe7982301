import request from '@/config/axios'

export type DictTypeVO = {
  id: number | undefined
  category: string // 参数分组
  type: number // 参数类型
  name: string // 参数名称
  configKey: string // 参数键名
  value: string // 参数键值
  visible: number // 是否可见
  remark: string // 备注

}

// 查询字典列表
export const getSystemConfigPage = (data) => {
  return request.post({ url: '/v1/platform/infra/system-config/page', data })
}

// 查询字典详情
export const getSystemConfig = (id: number) => {
  return request.get({ url: '/v1/platform/infra/system-config/get?id=' + id })
}

// 新增字典
export const createSystemConfig = (data: DictTypeVO) => {
  return request.post({ url: '/v1/platform/infra/system-config/create', data })
}

// 修改字典
export const updateSystemConfig = (data: DictTypeVO) => {
  return request.post({ url: '/v1/platform/infra/system-config/update', data })
}

// 删除字典
export const deleteSystemConfig = (id: number) => {
  return request.post({ url: '/v1/platform/infra/system-config/delete?id=' + id })
}

// 导出字典类型
export const exportSystemConfig = (params) => {
  return request.download({ url: '/v1/platform/infra/dict-type/export', params })
}
