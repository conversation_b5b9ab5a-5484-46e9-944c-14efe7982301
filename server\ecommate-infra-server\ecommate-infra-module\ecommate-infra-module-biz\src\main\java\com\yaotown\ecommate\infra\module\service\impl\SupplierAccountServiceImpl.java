package com.yaotown.ecommate.infra.module.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.common.core.entity.CurrentAccount;
import com.yaotown.ecommate.common.core.enums.AccountTypeEnum;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.security.core.util.SecurityUtils;
import com.yaotown.ecommate.infra.module.convert.accont.AccountConvert;
import com.yaotown.ecommate.infra.module.enums.AccountStatusEnum;
import com.yaotown.ecommate.infra.module.enums.EnterpriseAccountTypeEnum;
import com.yaotown.ecommate.infra.module.enums.oauth2.OAuth2ClientConstants;
import com.yaotown.ecommate.infra.module.mapper.account.AccountEnterpriseMapper;
import com.yaotown.ecommate.infra.module.mapper.account.AccountMapper;
import com.yaotown.ecommate.infra.module.pojo.dto.enterprise.account.LoginRespDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.platform.account.SupplierAccountCreateReqDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.platform.account.SupplierAccountPageReqDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.platform.account.SupplierAccountUpdateReqDTO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.AccountEnterprisePO;
import com.yaotown.ecommate.infra.module.pojo.entity.account.AccountPO;
import com.yaotown.ecommate.infra.module.service.PermissionService;
import com.yaotown.ecommate.infra.module.service.SupplierAccountService;
import com.yaotown.ecommate.infra.module.service.oauth2.OAuth2TokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Set;

/**
 * 供应商账户管理Service实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SupplierAccountServiceImpl implements SupplierAccountService {

    private final AccountMapper accountMapper;
    private final AccountEnterpriseMapper accountEnterpriseMapper;
    private final PermissionService permissionService;
    private final OAuth2TokenService oAuth2TokenService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginRespDTO createSupplierAccount(SupplierAccountCreateReqDTO createReqDTO, Long roleId, Long enterpriseId) {
        
        // 2. 校验手机号是否已存在
        if (accountMapper.selectByMobile(createReqDTO.getMobile()) != null) {
            throw new BusinessException("手机号已存在");
        }
        
        // 3. 创建账户
        AccountPO accountPO = new AccountPO();
        accountPO.setUsername(createReqDTO.getUsername());
        accountPO.setNickname(createReqDTO.getNickname());
        accountPO.setMobile(createReqDTO.getMobile());
        accountPO.setEmail(createReqDTO.getEmail());
        accountPO.setSex(createReqDTO.getSex());
        accountPO.setRemark(createReqDTO.getRemark());
        accountPO.setStatus(createReqDTO.getStatus() != null ? createReqDTO.getStatus() : AccountStatusEnum.NORMAL.getId());
        accountPO.setDeptId(createReqDTO.getDeptId());
        accountPO.setAccountType(AccountTypeEnum.SUPPLIER_ACCOUNT.getId()); // 供应商账号类型
        
        // 设置需要校验手机号 (供应商子账户第一次登录需要校验手机号)
        accountPO.setNeedMobileVerify(1);
        
        // 4. 设置密码（加盐处理）
        accountPO.setHashSalt(UUID.randomUUID().toString(true));
        accountPO.setLoginPassword(SecureUtil.md5(createReqDTO.getPassword() + accountPO.getHashSalt()));
        
        // 5. 设置过期时间（10年后过期）
        accountPO.setExpiredAt(DateUtil.offset(DateUtil.date(), DateField.YEAR, 10));
        
        // 6. 保存账户信息
        accountMapper.insert(accountPO);
        
        // 7. 创建账号与企业的关联（企业子账户）
        AccountEnterprisePO accountEnterprisePO = new AccountEnterprisePO();
        accountEnterprisePO.setAccountId(accountPO.getId());
        accountEnterprisePO.setEnterpriseId(enterpriseId);
        accountEnterprisePO.setEnterpriseAccountType(EnterpriseAccountTypeEnum.ENTERPRISE_SUB_ACCOUNT.getCode()); // 企业子账户
        
        // 8. 保存关联信息
        accountEnterpriseMapper.insert(accountEnterprisePO);
        
        // 9. 分配角色（必须绑定一个角色）
        if (roleId != null) {
            Set<Long> roleIds = new HashSet<>();
            roleIds.add(roleId);
            permissionService.assignUserRole(accountPO.getId(), roleIds);
        }
        
        // 10. 生成登录令牌返回
        return getLoginRespDTO(enterpriseId, accountPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSupplierAccount(SupplierAccountUpdateReqDTO updateReqDTO) {
        // 1. 校验账户是否存在
        AccountPO accountPO = accountMapper.selectById(updateReqDTO.getId());
        if (accountPO == null) {
            throw new BusinessException("账户不存在");
        }
        
        // 3. 校验手机号唯一性
        AccountPO existingAccount = accountMapper.selectByMobile(updateReqDTO.getMobile());
        if (existingAccount != null && !existingAccount.getId().equals(updateReqDTO.getId())) {
            throw new BusinessException("手机号已存在");
        }
        
        // 4. 更新账户信息
        accountPO.setUsername(updateReqDTO.getUsername());
        accountPO.setNickname(updateReqDTO.getNickname());
        accountPO.setMobile(updateReqDTO.getMobile());
        accountPO.setEmail(updateReqDTO.getEmail());
        accountPO.setSex(updateReqDTO.getSex());
        accountPO.setRemark(updateReqDTO.getRemark());
        accountPO.setStatus(updateReqDTO.getStatus());
        accountPO.setDeptId(updateReqDTO.getDeptId());
        
        // 5. 更新账户
        boolean updateResult = accountMapper.updateById(accountPO) > 0;
        
        // 6. 更新角色关联（如果提供了角色ID）
        if (updateReqDTO.getRoleId() != null) {
            Set<Long> roleIds = new HashSet<>();
            roleIds.add(updateReqDTO.getRoleId());
            permissionService.assignUserRole(updateReqDTO.getId(), roleIds);
        }
        
        return updateResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSupplierAccount(Long id) {
        // 1. 校验账户是否存在
        AccountPO accountPO = accountMapper.selectById(id);
        if (accountPO == null) {
            throw new BusinessException("账户不存在");
        }
        
        // 2. 删除角色关联
        permissionService.processUserDeleted(id);
        
        // 3. 删除企业关联
        LambdaQueryWrapper<AccountEnterprisePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountEnterprisePO::getAccountId, id);
        accountEnterpriseMapper.delete(queryWrapper);
        
        // 4. 删除账户
        return accountMapper.deleteById(id) > 0;
    }

    @Override
    public AccountPO getSupplierAccount(Long id) {
        return accountMapper.selectById(id);
    }

    @Override
    public PageData<AccountPO> getSupplierAccountPage(QueryModel<SupplierAccountPageReqDTO> queryModel) {
        // 获取当前登录用户的企业ID
        CurrentAccount loginAccount = SecurityUtils.getLoginAccount();

        SupplierAccountPageReqDTO param = queryModel.getParam();
        param.setEnterpriseId(loginAccount.getEnterpriseId());
        // 执行分页查询，在此实现分页查询逻辑
        return queryModel.queryPageData(() -> accountMapper.selectSupplierAccountPage(param));
    }
    
    /**
     * 生成登录令牌
     *
     * @param enterpriseId 企业ID
     * @param accountPO 账户信息
     * @return 登录响应
     */
    private LoginRespDTO getLoginRespDTO(Long enterpriseId, AccountPO accountPO) {
        // 生成访问令牌
        var accessToken = oAuth2TokenService.createAccessToken(
                enterpriseId,
                accountPO.getId(),
                accountPO.getAccountType(),
                OAuth2ClientConstants.CLIENT_ID_DEFAULT,
                null
        );
        
        // 转换为登录响应对象
        LoginRespDTO loginRespDTO = AccountConvert.INSTANCE.toLoginRespDTO(accountPO);
        loginRespDTO.setEnterpriseId(enterpriseId);
        loginRespDTO.setAccessToken(accessToken.getAccessToken());
        
        return loginRespDTO;
    }
} 