package com.yaotown.ecommate.infra.module.pojo.dto.base.sms;

import com.yaotown.ecommate.infra.module.enums.SmsCodeBizTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
@Data
public class SupplierSmsCodeRequestDTO {
    /**
     * 手机号
     */
    @Length(min = 11, max = 11, message = "请输入正确的11位手机号")
    @NotNull(message = "请输入正确的11位手机号")
    private String phone;


    /**
     * 短信验证码业务类型
     *
     * @see SmsCodeBizTypeEnum
     */
    @NotNull(message = "业务类型不为空")
    private Integer bizType;

}
