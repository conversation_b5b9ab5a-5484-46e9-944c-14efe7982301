package com.yaotown.ecommate.product.infrastructure.mapper.supply.category;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yaotown.ecommate.common.core.enums.BooleanEnum;
import com.yaotown.ecommate.product.infrastructure.po.supply.category.CategoryPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Mapper
public interface CategoryInfoMapper extends BaseMapper<CategoryPO> {

    default List<CategoryPO> selectListByParentIdAndPlatform(String parentId, String platform, Integer showStatus) {
        return selectListByParentIdsAndPlatform(Collections.singletonList(parentId), platform, showStatus);
    }

    default List<CategoryPO> selectListByParentIdsAndPlatform(List<String> parentIds, String platform, Integer showStatus) {
        if (StrUtil.isBlank(platform)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(parentIds), CategoryPO::getParentId, parentIds);
        queryWrapper.eq(CategoryPO::getPlatform, platform);
        queryWrapper.eq(Objects.nonNull(showStatus), CategoryPO::getShowStatus, showStatus);
        return selectList(queryWrapper);
    }

    default CategoryPO selectByIdAndPlatform(String id, String platform) {
        LambdaQueryWrapper<CategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryPO::getId, id);
        queryWrapper.eq(CategoryPO::getPlatform, platform);
        return selectOne(queryWrapper);
    }

    /**
     * 查询指定平台的叶子节点类目
     *
     * @param platform   平台标识
     * @param showStatus 显示状态，传null则不限制
     * @return 叶子节点类目列表
     */
    default List<CategoryPO> selectLeafListByPlatform(String name,String platform, Integer showStatus) {
        if (StrUtil.isBlank(platform)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryPO::getPlatform, platform);
        if (name != null){
            queryWrapper.eq(CategoryPO::getName, name);
        }
        // 只查询叶子节点
        queryWrapper.eq(CategoryPO::getLeaf, BooleanEnum.TRUE.getId());
        // 只查询未删除的
        queryWrapper.eq(CategoryPO::getDeleteFlag, BooleanEnum.FALSE.getId());
        // 按显示状态过滤
        queryWrapper.eq(Objects.nonNull(showStatus), CategoryPO::getShowStatus, showStatus);
        return selectList(queryWrapper);
    }

    default int updateShowStatusByIds(List<String> ids, String platform, Integer showStatus) {
        if (CollUtil.isEmpty(ids)) {
            return 0;
        }
        LambdaUpdateWrapper<CategoryPO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.in(CategoryPO::getId, ids);
        queryWrapper.eq(CategoryPO::getPlatform, platform);
        queryWrapper.set(CategoryPO::getShowStatus, showStatus);
        return update(queryWrapper);
    }

    /**
     * 查询指定平台的未关联标签的叶子节点类目
     *
     * @param name            类目名称关键字，可选
     * @param platform        平台标识
     * @param showStatus      显示状态，传null则不限制
     * @param excludeIds      要排除的类目ID列表
     * @return 未关联标签的叶子节点类目列表
     */
    default List<CategoryPO> selectUntaggedCategories(String name, String platform, Integer showStatus, List<String> excludeIds) {
        if (StrUtil.isBlank(platform)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryPO::getPlatform, platform);
        if (name != null && name.trim().length() > 0) {
            queryWrapper.like(CategoryPO::getName, "%" + name + "%");
        }
        // 只查询叶子节点
        queryWrapper.eq(CategoryPO::getLeaf, BooleanEnum.TRUE.getId());
        // 按显示状态过滤
        queryWrapper.eq(Objects.nonNull(showStatus), CategoryPO::getShowStatus, showStatus);
        // 排除已关联标签的类目ID
        if (CollUtil.isNotEmpty(excludeIds)) {
            queryWrapper.notIn(CategoryPO::getId, excludeIds);
        }
        return selectList(queryWrapper);
    }

   default int deleteByIdAndPlatform(String id, String platform){
        LambdaUpdateWrapper<CategoryPO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(CategoryPO::getId, id);
        queryWrapper.eq(CategoryPO::getPlatform, platform);
        return delete(queryWrapper);
   }

    default int updateByIdAndPlatform(CategoryPO categoryPO) {
        LambdaUpdateWrapper<CategoryPO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(CategoryPO::getId, categoryPO.getId());
        queryWrapper.eq(CategoryPO::getPlatform, categoryPO.getPlatform());
        return update(categoryPO, queryWrapper);
    }
}