package com.yaotown.ecommate.product.domain.product.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierFreightTemplateEntity;
import com.yaotown.ecommate.product.domain.product.management.repository.ISupplierFreightTemplateRepository;
import com.yaotown.ecommate.product.domain.product.management.service.ISupplierFreightTemplateService;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 供应商运费模板服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class SupplierFreightTemplateServiceImpl implements ISupplierFreightTemplateService {

    private final ISupplierFreightTemplateRepository supplierFreightTemplateRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTemplate(SupplierFreightTemplateEntity entity) {
        return supplierFreightTemplateRepository.save(entity);
    }

    @Override
    public boolean updateTemplate(SupplierFreightTemplateEntity entity) {
        if (entity == null || entity.getId() == null) {
            return false;
        }
        return supplierFreightTemplateRepository.update(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(Long id) {
        return supplierFreightTemplateRepository.deleteById(id);
    }

    @Override
    public SupplierFreightTemplateEntity getTemplateById(Long id, Long enterpriseId) {
        if (id == null || enterpriseId == null) {
            return null;
        }
        return supplierFreightTemplateRepository.getById(id, enterpriseId);
    }

    @Override
    public List<SupplierFreightTemplateEntity> getTemplateByName(String templateName, Long enterpriseId) {
        if (StringUtils.isBlank(templateName) || enterpriseId == null) {
            return CollUtil.newArrayList();
        }
        return supplierFreightTemplateRepository.listByTemplateName(templateName, enterpriseId);
    }

    @Override
    public List<SupplierFreightTemplateEntity> listActiveTemplate(Long enterpriseId) {
        return supplierFreightTemplateRepository.listActiveTemplate(enterpriseId);
    }

    @Override
    public PageData<SupplierFreightTemplateEntity> pageTemplate(QueryModel<String> queryModel, Long enterpriseId) {
        return supplierFreightTemplateRepository.page(queryModel, enterpriseId);
    }

    @Override
    public List<String> listTemplateNames(Long enterpriseId) {
        if (enterpriseId == null) {
            return CollUtil.newArrayList();
        }
        return supplierFreightTemplateRepository.listTemplateNames(enterpriseId);
    }

    @Override
    public Long calculateFreight(String templateName, String regionCode, Integer quantity, Long enterpriseId) {
        if (StringUtils.isBlank(templateName) || StringUtils.isBlank(regionCode) || quantity == null || quantity <= 0 || enterpriseId == null) {
            throw new BusinessException("计算运费参数错误");
        }
        
        // 获取模板规则列表
        List<SupplierFreightTemplateEntity> templateList = supplierFreightTemplateRepository.listByTemplateName(templateName, enterpriseId);
        if (CollUtil.isEmpty(templateList)) {
            throw new BusinessException("未找到运费模板");
        }
        
        // 查找匹配的区域规则
        Optional<SupplierFreightTemplateEntity> matchTemplate = templateList.stream()
                .filter(t -> StringUtils.contains(t.getRegionCodes(), regionCode))
                .findFirst();
        
        // 如果没有匹配的区域，寻找默认规则（通常是"其他地区"或regionCodes为"ALL"的规则）
        if (!matchTemplate.isPresent()) {
            matchTemplate = templateList.stream()
                    .filter(t -> "ALL".equals(t.getRegionCodes()) || t.getRegionName().contains("其他") || t.getRegionName().contains("全国"))
                    .findFirst();
        }
        
        // 如果仍然没有找到匹配规则，使用第一个规则
        SupplierFreightTemplateEntity template = matchTemplate.orElse(templateList.get(0));
        
        // 计算运费 = 首件费用 + (件数-首件数) ÷ 续件数 × 续件费用
        Long firstFee = template.getFirstItemFee();
        int firstItem = template.getFirstItem();
        int additionalItem = template.getAdditionalItem();
        Long additionalFee = template.getAdditionalItemFee();
        
        if (quantity <= firstItem) {
            return firstFee;
        } else {
            int additionalQuantity = quantity - firstItem;
            int additionalTimes = (additionalQuantity + additionalItem - 1) / additionalItem;  // 向上取整
            return firstFee + additionalTimes * additionalFee;
        }
    }
} 