package com.yaotown.ecommate.product.trigger.http.supplier;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.common.base.core.entity.R;
import com.yaotown.ecommate.common.security.core.util.SecurityUtils;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierFreightTemplateEntity;
import com.yaotown.ecommate.product.domain.product.management.service.ISupplierFreightTemplateService;
import com.yaotown.ecommate.product.trigger.biz.convert.FreightTemplateConvert;
import com.yaotown.ecommate.product.trigger.biz.management.model.request.FreightTemplateReqDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 运费模板控制器
 *
 * @apiFolder 供应商端/产品中心服务/运费模板
 * @classPrefixPath /v1/supplier/
 */
@RestController
@RequestMapping("/product/freight-template")
@Slf4j
@AllArgsConstructor
public class FreightTemplateController {

    private final ISupplierFreightTemplateService supplierFreightTemplateService;

    /**
     * 分页查询运费模板列表
     *
     * @param queryModel 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    public R<PageData<SupplierFreightTemplateEntity>> pageFreightTemplate(@RequestBody QueryModel<String> queryModel) {
        Long enterpriseId = SecurityUtils.getLoginEnterpriseId();
        // 调用领域服务查询数据
        PageData<SupplierFreightTemplateEntity> pageData = supplierFreightTemplateService.pageTemplate(queryModel, enterpriseId);
        return R.success(pageData);
    }

    /**
     * 查询启用的运费模板列表
     *
     * @return 分页结果
     */
    @PostMapping("/list")
    public R<List<SupplierFreightTemplateEntity>> list() {
        Long enterpriseId = SecurityUtils.getLoginEnterpriseId();
        return R.success(supplierFreightTemplateService.listActiveTemplate(enterpriseId));
    }


    /**
     * 获取单个模板项详情
     *
     * @param id 模板项ID
     * @return 模板项详情
     */
    @GetMapping("/item/{id}")
    public R<SupplierFreightTemplateEntity> getTemplateItem(@PathVariable Long id) {
        Long enterpriseId = SecurityUtils.getLoginEnterpriseId();
        return R.success(supplierFreightTemplateService.getTemplateById(id, enterpriseId));
    }


    /**
     * 新增运费模板
     *
     * @param reqDTO 请求参数
     * @return 操作结果
     */
    @PostMapping("/add")
    public R<Boolean> addFreightTemplate(@RequestBody @Validated FreightTemplateReqDTO reqDTO) {

        Long enterpriseId = SecurityUtils.getLoginEnterpriseId();
        SupplierFreightTemplateEntity supplierFreightTemplateEntity = FreightTemplateConvert.INSTANCE.convertToEntity(reqDTO);
        supplierFreightTemplateEntity.setEnterpriseId(enterpriseId);
        return R.success(supplierFreightTemplateService.saveTemplate(supplierFreightTemplateEntity));

    }

    /**
     * 更新运费模板项
     *
     * @param reqDTO 请求参数
     * @return 操作结果
     */
    @PostMapping("/update")
    public R<Boolean> updateFreightTemplate(@RequestBody @Validated FreightTemplateReqDTO reqDTO) {
        Long enterpriseId = SecurityUtils.getLoginEnterpriseId();
        SupplierFreightTemplateEntity entity = FreightTemplateConvert.INSTANCE.convertToEntity(reqDTO);
        entity.setEnterpriseId(enterpriseId);
        return R.success(supplierFreightTemplateService.updateTemplate(entity));

    }

    /**
     * 删除运费模板
     *
     * @param id 模板id
     * @return 操作结果
     */
    @PostMapping("/delete/{id}")
    public R<Boolean> deleteFreightTemplate(@PathVariable Long id) {
        return R.success(supplierFreightTemplateService.deleteTemplate(id));
    }
}