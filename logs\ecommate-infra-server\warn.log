2025-08-01 14:34:14.428 [main] WARN  [ecommate-infra-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-infra-server] & group[DEFAULT_GROUP]
2025-08-01 14:34:14.434 [main] WARN  [ecommate-infra-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-infra-server.yaml] & group[DEFAULT_GROUP]
2025-08-01 14:34:16.877 [main] WARN  [ecommate-infra-server] org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.yaotown.ecommate.infra.web]' package. Please check your configuration.
2025-08-01 14:34:18.018 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration' of type [com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [environmentSetup] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-01 14:34:18.029 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.033 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.034 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$897/0x0000025d547f1170] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.045 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.050 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq.enhance-com.yaotown.sdk.mq.properties.RocketEnhanceProperties' of type [com.yaotown.sdk.mq.properties.RocketEnhanceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.062 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.069 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.388 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.398 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.405 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.477 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-01 14:34:18.481 [main] WARN  [ecommate-infra-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-08-01 14:34:18.855 [main] WARN  [ecommate-infra-server] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-01 14:34:20.851 [main] WARN  [ecommate-infra-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.infra.module.pojo.entity.message.RecordMessagePO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 14:34:21.008 [main] WARN  [ecommate-infra-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.infra.module.pojo.entity.supplier.AddressCodePO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 14:34:27.026 [main] WARN  [ecommate-infra-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.infra.module.pojo.entity.supplier.BankPO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
