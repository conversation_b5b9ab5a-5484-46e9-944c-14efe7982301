package com.yaotown.ecommate.product.trigger.biz.convert;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.ecommate.product.api.model.response.ShopInfoRespDTO;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopPlatformTypeEntity;
import com.yaotown.ecommate.product.trigger.biz.enterprise.model.response.ShopInfoDTO;
import com.yaotown.ecommate.product.trigger.biz.enterprise.model.response.ShopPlatformTypeDTO;
import com.yaotown.ecommate.product.types.util.MapStructConvertUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = MapStructConvertUtil.class)
public interface ShopInfoConvert {
    ShopInfoConvert INSTANCE = Mappers.getMapper(ShopInfoConvert.class);
    ShopInfoEntity toShopInfoEntity(ShopInfoDTO shopInfoDTO);

    @Mapping(target = "pageContents", source = "pageContents")
    PageData<ShopInfoDTO> toShopInfoEntityList(PageData<ShopInfoEntity> shopInfoEntities);

    @Mapping(target = "pageContents", source = "pageContents")
    PageData<ShopInfoRespDTO> toShopInfoRespDTOPageData(PageData<ShopInfoEntity> shopInfoEntities);

    List<ShopInfoEntity> toShopInfoVOList(List<ShopInfoDTO> shopInfo);
    List<ShopPlatformTypeDTO> toShopShopPlatformType(List<ShopPlatformTypeEntity> shopPlatformTypeEntities);
    List<ShopInfoDTO> toShopInfo(List<ShopInfoEntity> shopInfo);
    
    /**
     * 将ShopInfoEntity转换为ShopInfoRespDTO
     * 
     * @param entity ShopInfoEntity
     * @return ShopInfoRespDTO
     */
    ShopInfoRespDTO toShopInfoRespDTO(ShopInfoEntity entity);
}
