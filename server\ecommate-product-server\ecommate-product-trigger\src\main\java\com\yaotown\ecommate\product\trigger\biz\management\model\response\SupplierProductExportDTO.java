package com.yaotown.ecommate.product.trigger.biz.management.model.response;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 供应商商品导出DTO
 */
@Data
public class SupplierProductExportDTO {
    @ExcelProperty("商品名称")
    private String productName;

    @ExcelProperty("商品自编码")
    private String productCode;
    
    @ExcelProperty("SKU自编码")
    private String skuCode;

    @ExcelProperty("规格")
    private String specs;

    @ExcelProperty("销售价格")
    private Double salePrice;

    @ExcelProperty("成本价格")
    private Double costPrice;

    @ExcelProperty("可售数量")
    private Integer stock;
    
    @ExcelProperty("SKU条形码")
    private String barcode;
    
    @ExcelProperty("上架状态")
    private String saleStateDesc;
} 