package com.yaotown.ecommate.infra.module.api.message.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yaotown.ecommate.common.apidoc.annotation.ApiDocFieldIgnore;
import com.yaotown.ecommate.common.core.enums.EnableFlagEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/5/8
 */
@Data
public class QueryMsgReqDTO {

    /**
     * 企业id
     */
    @ApiDocFieldIgnore
    @JsonIgnore
    private Long enterpriseId;
    /**
     * 接收人id
     */
    @ApiDocFieldIgnore
    @JsonIgnore
    private Long receiverId;

    /**
     * 是否阅读
     * @see EnableFlagEnum
     */
    private Integer readFlag;

    /**
     * 消息类型
     */
    private String businessType;
}
