package com.yaotown.ecommate.product.domain.product.management.service;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierFreightTemplateEntity;

import java.util.List;

/**
 * 供应商运费模板服务接口
 */
public interface ISupplierFreightTemplateService {

    /**
     * 保存运费模板
     *
     * @param entity 运费模板实体
     * @return 是否成功
     */
    boolean saveTemplate(SupplierFreightTemplateEntity entity);

    /**
     * 更新运费模板
     *
     * @param entity 运费模板实体
     * @return 是否成功
     */
    boolean updateTemplate(SupplierFreightTemplateEntity entity);

    /**
     * 删除运费模板
     *
     * @param id 模板ID
     * @return 是否成功
     */
    boolean deleteTemplate(Long id);

    /**
     * 根据ID查询运费模板
     *
     * @param id 主键
     * @param enterpriseId 企业ID
     * @return 运费模板实体
     */
    SupplierFreightTemplateEntity getTemplateById(Long id, Long enterpriseId);

    /**
     * 根据模板名称查询运费模板
     *
     * @param templateName 模板名称
     * @param enterpriseId 企业ID
     * @return 运费模板实体列表
     */
    List<SupplierFreightTemplateEntity> getTemplateByName(String templateName, Long enterpriseId);

    /**
     *  获取启用的模板
     * @param enterpriseId
     * @return
     */
    List<SupplierFreightTemplateEntity> listActiveTemplate(Long enterpriseId);
    /**
     * 分页查询运费模板
     *
     * @param queryModel 查询条件
     * @param enterpriseId 企业ID
     * @return 分页结果
     */
    PageData<SupplierFreightTemplateEntity> pageTemplate(QueryModel<String> queryModel, Long enterpriseId);
    
    /**
     * 获取企业的所有运费模板名称
     * 
     * @param enterpriseId 企业ID
     * @return 模板名称列表
     */
    List<String> listTemplateNames(Long enterpriseId);
    
    /**
     * 计算运费
     * 
     * @param templateName 模板名称
     * @param regionCode 地区编码
     * @param quantity 商品数量
     * @param enterpriseId 企业ID
     * @return 运费（分）
     */
    Long calculateFreight(String templateName, String regionCode, Integer quantity, Long enterpriseId);
} 