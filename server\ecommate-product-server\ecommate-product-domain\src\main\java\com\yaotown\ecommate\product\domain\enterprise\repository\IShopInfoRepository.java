package com.yaotown.ecommate.product.domain.enterprise.repository;

import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopPlatformTypeEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.valobj.ShopInfoSearchVO;
import com.yaotown.ecommate.product.domain.product.erplink.model.entity.ErpShopInfoEntity;

import java.util.List;

public interface IShopInfoRepository {

    List<ShopInfoEntity> selectShopInfoList(Long accountId);

    ShopInfoEntity findShopInfo(Long shopId);

    List<ShopInfoEntity> selectShopInfoList(Long accountId, List<Long> shopIds);

    default List<ShopInfoEntity> selectByPlatformType(String platformType, Long accountId) {
        return selectByPlatformType(List.of(platformType), accountId, false);
    }

    List<ShopInfoEntity> selectByPlatformType(List<String> platformTypes, Long accountId, boolean filterAuthorized);

    List<ShopPlatformTypeEntity> selectShopType(Long accountId);

    void syncErpShopInfo(List<ErpShopInfoEntity> list, Long enterpriseId, Long accountId);

    PageData<ShopInfoEntity> selectShopInfoLists(QueryModel<Void> queryModel, String platformType, Long accountId);

    List<ShopInfoEntity> selectShopList(Long accountId);

    PageData<ShopInfoEntity> selectShopInfoList(QueryModel<ShopInfoSearchVO> queryModel);

    /**
     * 保存店铺信息
     * @param shopInfo 店铺信息
     * @return 店铺ID
     */
    Long saveShopInfo(ShopInfoEntity shopInfo);

    /**
     * 更新店铺信息
     * @param shopInfo 店铺信息
     * @return 是否更新成功
     */
    boolean updateShopInfo(ShopInfoEntity shopInfo);

    void updateAccessToken(Long enterpriseId, Long shopId, String accessToken, String refreshToken, Integer expiresInSeconds);
}
