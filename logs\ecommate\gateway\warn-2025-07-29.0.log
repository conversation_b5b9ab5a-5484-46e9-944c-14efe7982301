2025-07-29 16:13:31.307 [main] WARN  [ecommate-gateway,,,] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-gateway-local.yaml] & group[DEFAULT_GROUP]
2025-07-29 16:13:31.314 [main] WARN  [ecommate-gateway,,,] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-gateway] & group[DEFAULT_GROUP]
2025-07-29 16:13:31.319 [main] WARN  [ecommate-gateway,,,] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-gateway.yaml] & group[DEFAULT_GROUP]
2025-07-29 16:13:31.503 [main] WARN  [ecommate-gateway,,,] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-gateway-local.yaml] & group[DEFAULT_GROUP]
2025-07-29 16:13:32.937 [main] WARN  [ecommate-gateway,,,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-29 16:13:32.940 [main] WARN  [ecommate-gateway,,,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:32.942 [main] WARN  [ecommate-gateway,,,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-29 16:13:32.943 [main] WARN  [ecommate-gateway,,,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:32.945 [main] WARN  [ecommate-gateway,,,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:38.750 [main] WARN  [ecommate-gateway,,,] o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-29 16:13:41.283 [boundedElastic-4] WARN  [ecommate-gateway,,,] com.alibaba.nacos.client.utils.ConcurrentDiskUtil - lock DEFAULT_GROUP%40%40ecommate-gateway conflict;retry time: 1
2025-07-29 16:43:33.120 [Thread-5] WARN  [ecommate-gateway,,,] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-29 16:43:33.120 [Thread-3] WARN  [ecommate-gateway,,,] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-29 16:43:33.120 [Thread-5] WARN  [ecommate-gateway,,,] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-29 16:43:33.121 [Thread-3] WARN  [ecommate-gateway,,,] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] WARN  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] WARN  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] WARN  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] WARN  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
