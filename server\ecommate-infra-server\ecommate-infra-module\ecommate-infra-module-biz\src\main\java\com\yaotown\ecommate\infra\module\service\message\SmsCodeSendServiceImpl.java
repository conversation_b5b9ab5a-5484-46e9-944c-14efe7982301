package com.yaotown.ecommate.infra.module.service.message;

import cn.hutool.core.util.IdUtil;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.core.util.spring.SpringUtils;
import com.yaotown.ecommate.common.redis.util.RedisUtil;
import com.yaotown.ecommate.infra.module.api.message.dto.SmsCodeSendRequestDTO;
import com.yaotown.ecommate.infra.module.enums.ErrorCodeConstants;
import com.yaotown.ecommate.infra.module.pojo.dto.base.CaptchaReqDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.base.message.MessageParamReqDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SmsCodeCheckDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SmsCodeRedisParam;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SmsCodeRequestDTO;
import com.yaotown.ecommate.infra.module.pojo.dto.base.sms.SupplierSmsCodeRequestDTO;
import com.yaotown.ecommate.infra.module.properties.YaotownMessageConfigProperties;
import com.yaotown.ecommate.infra.module.service.CaptchaService;
import com.yaotown.ecommate.infra.module.util.InfraRedisKeyUtil;
import com.yaotown.sdk.message.module.service.api.domain.MessageParam;
import com.yaotown.sdk.message.module.service.api.domain.SendRequest;
import com.yaotown.sdk.message.module.service.api.enums.BusinessCodeEnum;
import com.yaotown.sdk.message.module.service.api.service.SendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2025/5/9
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SmsCodeSendServiceImpl implements SmsCodeSendService {

    private final SendService sendService;
    private final CaptchaService captchaService;
    private final YaotownMessageConfigProperties messageConfigProperties;
    private final RedisUtil redisUtil;

    @Value("${yaotown.local-sms.enabled:true}")
    private Boolean localSmsCodeEnabled;
    @Value("${yaotown.local-sms.code:123456}")
    private String localSmsCode;

    /**
     * Feign 接口调用
     *
     * @param reqDTO
     * @return
     */
    @Override
    public String sendSmsCode(SmsCodeSendRequestDTO reqDTO) {
        // 执行发送逻辑
        return doSendSmsCode(reqDTO.getPhone(), reqDTO.getBizType());
    }

    /**
     * 企业端接口调用
     *
     * @param req
     */
    @Override
    public String smsCodeSend(SmsCodeRequestDTO req) {
        // 判断验证码
        CaptchaReqDTO captcha = req.getCaptcha();
        captchaService.verifyCaptcha(captcha.getCaptchaKey(), captcha.getCaptchaCode(), true);
        // 执行发送逻辑
        return doSendSmsCode(req.getPhone(), req.getBizType());
    }

    public String doSendSmsCode(String phone, Integer bizType) {
        //判断是否已发送
        String smsCodeLockKey = InfraRedisKeyUtil.smsCodeLockKey(phone);
        Boolean canSend = redisUtil.setIfAbsent(smsCodeLockKey, "locked", 1, TimeUnit.MINUTES);
        if (Boolean.FALSE.equals(canSend)) {
            throw new BusinessException(ErrorCodeConstants.FREQUENT_OPERATIONS);
        }
        String smsId = IdUtil.fastSimpleUUID();
        String code = RandomStringUtils.randomNumeric(6);
        //判断是否生产环境
        if (localSmsCodeEnabled && !SpringUtils.isProd()) {
            cacheSmsCode(phone, bizType, smsId, localSmsCode);
            return smsId;
        }
        //短信发送
        doSend(code, phone, bizType);
        //存入redis
        cacheSmsCode(phone, bizType, smsId, code);
        return smsId;
    }

    private void cacheSmsCode(String phone, Integer bizType, String smsId, String code) {
        SmsCodeRedisParam redisParam = new SmsCodeRedisParam();
        redisParam.setCode(code);
        redisParam.setPhone(phone);
        redisParam.setSmsId(smsId);
        redisParam.setBizType(bizType);
        String smsCodeKey = InfraRedisKeyUtil.smsCodeKey(phone);
        redisUtil.set(smsCodeKey, redisParam, 3, TimeUnit.MINUTES);
    }

    @Override
    public void codeVerify(SmsCodeCheckDTO smsCodeCheck, boolean deleteCode) {
        String smsCodeKey = InfraRedisKeyUtil.smsCodeKey(smsCodeCheck.getPhone());
        SmsCodeRedisParam param = (SmsCodeRedisParam) redisUtil.get(smsCodeKey);
        if (Objects.isNull(param)) {
            throw new BusinessException(ErrorCodeConstants.SMS_CODE_DOES_NOT_EXIST_OR_EXPIRED);
        }
        // 获取 code 和 mobile 的值
        String code = param.getCode();
        String mobile = param.getPhone();
        String smsId = param.getSmsId();

        if (!smsCodeCheck.getCode().equals(code)) {
            throw new BusinessException(ErrorCodeConstants.THE_VERIFICATION_CODE_IS_INCORRECT);
        }
        if (!smsCodeCheck.getPhone().equals(mobile)) {
            throw new BusinessException(ErrorCodeConstants.THE_PHONE_NUMBER_IS_INCORRECT);
        }
        if (!smsCodeCheck.getSmsId().equals(smsId)) {
            throw new BusinessException(ErrorCodeConstants.SMS_ID_IS_INCORRECT);
        }
        if (deleteCode) {
            redisUtil.del(smsCodeKey);
        }
    }

    /**
     * 供应商端接口调用
     *
     * @param req
     */
    @Override
    public String supplierSmsCodeSend(SupplierSmsCodeRequestDTO req) {
        // 执行发送逻辑
        return doSendSmsCode(req.getPhone(), req.getBizType());
    }


    /**
     * @param code    code
     * @param phone   手机号
     * @param bizType 类型
     */
    private void doSend(String code, String phone, Integer bizType) {
        Map<String, String> variablesMap = new HashMap<>();
        variablesMap.put("code", code);
        MessageParamReqDTO messageParamReqDTO = MessageParamReqDTO.builder().receiver(phone)
                .needCallback(false).variables(variablesMap).build();
        SendRequest sendRequest = new SendRequest();
        // 普通发送
        sendRequest.setCode(BusinessCodeEnum.COMMON_SEND.getCode());
        sendRequest.setMessageTemplateId(messageConfigProperties.getSmsMessageTemplateId());

        MessageParam messageParam = new MessageParam();
        messageParam.setBizId(messageParamReqDTO.getBizId());
        messageParam.setReceiver(messageParamReqDTO.getReceiver());
        messageParam.setVariables(messageParamReqDTO.getVariables());
        messageParam.setExtra(messageParamReqDTO.getExtra());
        messageParam.setNeedCallback(messageParamReqDTO.getNeedCallback());
        messageParam.setCallbackTopicName(messageParamReqDTO.getCallbackTopicName());
        sendRequest.setMessageParam(messageParam);
        // 请求发送消息
        try {
            sendService.send(sendRequest);
        } catch (Exception e) {
            log.error("[发送短信sdk] errorMsg: ", e);
            throw new BusinessException(ErrorCodeConstants.SENDING_SMS_FAILED, "发送短信失败");
        }
    }

}
