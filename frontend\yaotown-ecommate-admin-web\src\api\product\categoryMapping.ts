import request from '@/config/axios'

/**
 * 获取顶级类目列表
 * @param platform 平台编码，例如：YHT, FXG, KWAISHOP
 */
export const getCategoryTopList = (platform: string) => {
  return request.get({ url: `/v1/platform/product/category/top/list/${platform}` })
}

/**
 * 获取类目树信息（用于懒加载子节点）
 * @param platform 平台编码，例如：YHT, FXG, KWAISHOP
 * @param parentId 父类目ID，传0表示获取顶级类目
 * @param showStatus 显示状态(0:不显示 1:显示)，可选参数
 */
export const getCategoryTree = (platform: string, parentId: string, showStatus?: number) => {
  const params: any = {}
  if (showStatus !== undefined) {
    params.showStatus = showStatus
  }
  return request.get({
    url: `/v1/platform/product/category/tree/${platform}/${parentId}`,
    params
  })
}

/**
 * 获取类目属性
 * @param platformCategoryId 类目ID
 * @param platform 平台编码，例如：YHT, FXG, KWAISHOP
 */
export const getCategoryAttributes = (platform: string, platformCategoryId: string) => {
  return request.get({
    url: `/v1/platform/product/attribute/list/${platform}/${platformCategoryId}`
  })
}

/**
 * 根据目标平台类目ID获取映射关系
 * @param targetPlatform 目标平台编码
 * @param targetCategoryId 目标平台类目ID
 */
export const getCategoryMappingByTargetId = (targetPlatform: string, targetCategoryId: string) => {
  return request.get({
    url: `/v1/platform/product/category/mapping/get/${targetPlatform}/${targetCategoryId}`
  })
}

/**
 * 获取类目路径
 * @param platform 平台编码
 * @param categoryId 类目ID
 */
export const getCategoryPath = (platform: string, categoryId: string) => {
  return request.get({
    url: `/v1/platform/product/category/path/${platform}/${categoryId}`
  })
}

/**
 * 获取属性映射列表
 * @param targetPlatform 目标平台编码
 * @param sourceCategoryId 源类目ID
 * @param platformCategoryLeafId 目标类目ID
 */
export const getAttributeMappingList = (targetPlatform: string,sourceCategoryId: string, platformCategoryLeafId: string) => {
  return request.get({
    url: `/v1/platform/product/attribute/mapping/get/${targetPlatform}/${sourceCategoryId}/${platformCategoryLeafId}`
  })
}

/**
 * 保存属性映射
 * @param data 映射数据
 */
export const saveAttributeMapping = (data: any) => {
  return request.post({
    url: '/v1/platform/product/attribute/mapping/save',
    data
  })
}

/**
 * 删除属性映射
 * @param id 映射ID
 */
export const deleteAttributeMapping = (id: string) => {
  return request.delete({
    url: `/v1/platform/product/attribute/mapping/delete/${id}`
  })
}

/**
 * 语义匹配接口
 * @param data 批量映射数据
 */
export const semanticMatch = (data: any) => {
  return request.post({
    url: '/v1/platform/product/attribute/mapping/semantic-match',
    // url: `/v1/platform/product/attribute/mapping/semantic-match?query=${data.query}&threshold=${data.threshold}&candidates=${data.candidates}`,
    data
  })
}

export interface TaskProgress {
  taskId: string
  platform: string
  status: string
  startTime: Date
  endTime: Date
  totalItems: number
  processedItems: number
  currentPage: number
  totalPages: number
  createdMappings: number
  updatedAttributes: number
  errorMessage: string
}

/**
 * 初始化类目属性映射任务
 * @param platform 平台类型，例如：KWAISHOP
 * @returns 任务ID
 */
export const initCategoryAttributeMapping = (platform: string) => {
  return request.post<string>({
    url: `/v1/platform/product/attribute/mapping/init/${platform}`
  })
}

/**
 * 查询类目属性映射任务进度
 * @param taskId 任务ID
 * @returns 任务进度信息
 */
export const getTaskProgress = (taskId: string) => {
  return request.get<TaskProgress>({
    url: `/v1/platform/product/attribute/mapping/progress/${taskId}`
  })
}

