package com.yaotown.ecommate.product.types.util;

import com.yaotown.ecommate.common.core.constant.CommonRedisConstant;
import com.yaotown.ecommate.common.core.enums.ShowStatusEnum;
import com.yaotown.ecommate.product.types.constant.ProductRedisConstant;

/**
 * <AUTHOR>
 */
public class ProductRedisKeyUtil {

    /**
     * 选品中心 类目缓存
     *
     * @return String
     */
    public static String supplyCategoryInfoParentKey(String platform, String parentId, Integer showStatus) {
        if (showStatus != null && showStatus.equals(ShowStatusEnum.VISIBLE.getCode())) {
            return CommonRedisConstant.REDIS_KEY_PREFIX + ProductRedisConstant.SUPPLY_CATEGORY_INFO_PARENT_PREFIX + platform + ":" + parentId + ":" + showStatus;
        }
        return CommonRedisConstant.REDIS_KEY_PREFIX + ProductRedisConstant.SUPPLY_CATEGORY_INFO_PARENT_PREFIX + platform + ":" + parentId;
    }

    public static String supplyCategoryInfoTopKey(String platform, Integer showStatus) {
        if (showStatus != null && showStatus.equals(ShowStatusEnum.VISIBLE.getCode())) {
            return CommonRedisConstant.REDIS_KEY_PREFIX + ProductRedisConstant.SUPPLY_CATEGORY_INFO_TOP_PREFIX + platform + ":" + showStatus;
        }
        return CommonRedisConstant.REDIS_KEY_PREFIX + ProductRedisConstant.SUPPLY_CATEGORY_INFO_TOP_PREFIX + platform;
    }

    /**
     * 类目属性映射任务进度键
     *
     * @param taskId 任务ID
     * @return Redis键
     */
    public static String categoryAttributeTaskKey(String taskId) {
        return CommonRedisConstant.REDIS_KEY_PREFIX + ProductRedisConstant.CATEGORY_ATTRIBUTE_TASK_PREFIX + taskId;
    }
}
