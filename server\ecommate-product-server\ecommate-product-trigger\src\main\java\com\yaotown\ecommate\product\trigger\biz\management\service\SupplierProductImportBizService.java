package com.yaotown.ecommate.product.trigger.biz.management.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.ecommate.common.core.enums.BooleanEnum;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.product.domain.product.management.model.aggregate.SupplierProductAggregate;
import com.yaotown.ecommate.product.domain.product.management.model.entity.ProductChangeLogEntity;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierProductCategoryEntity;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierProductEntity;
import com.yaotown.ecommate.product.domain.product.management.model.entity.SupplierProductSkuEntity;
import com.yaotown.ecommate.product.domain.product.management.model.valobj.SupplierProductIdVO;
import com.yaotown.ecommate.product.domain.product.management.model.valobj.SupplierProductImageVO;
import com.yaotown.ecommate.product.domain.product.management.repository.ISupplierProductCategoryRepository;
import com.yaotown.ecommate.product.domain.product.management.service.ISupplierProductManageService;
import com.yaotown.ecommate.product.domain.product.management.service.ProductChangeLogService;
import com.yaotown.ecommate.product.trigger.biz.management.model.request.SupplierProductImportReqDTO;
import com.yaotown.ecommate.product.trigger.biz.management.model.request.SupplierProductPageQueryReqDTO;
import com.yaotown.ecommate.product.trigger.biz.management.model.response.SupplierProductExportDTO;
import com.yaotown.ecommate.product.trigger.biz.management.model.response.SupplierProductImportRespDTO;
import com.yaotown.ecommate.product.types.enums.AuditStatusEnum;
import com.yaotown.ecommate.product.types.enums.erp.ErpPlatformTypeEnum;
import com.yaotown.ecommate.product.types.enums.management.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 供应商产品导入导出业务服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierProductImportBizService {

    private final ISupplierProductManageService supplierProductManageService;
    private final ProductChangeLogService productChangeLogService;
    private final ISupplierProductCategoryRepository supplierProductCategoryRepository;

    /**
     * 批处理大小，每批处理的记录数
     */
    private static final int BATCH_SIZE = 100;

    /**
     * 查询导出大小，每次数据
     */
    private static final int PAGE_SIZE = 200;

    /**
     * 导入产品Excel
     */
    public SupplierProductImportRespDTO importProducts(MultipartFile file, Long enterpriseId) throws IOException {
        SupplierProductImportRespDTO result = new SupplierProductImportRespDTO();
        
        List<Map<String, Object>> errorRecords = new ArrayList<>();
        // 创建解析监听器
        ExcelListener listener = new ExcelListener(errorRecords, enterpriseId);
        
        // 解析Excel文件
        EasyExcel.read(file.getInputStream(), SupplierProductImportReqDTO.class, listener).sheet().doRead();
        
        // 设置返回结果
        result.setTotalCount(listener.getTotalCount());
        result.setSuccessCount(listener.getSuccessCount());
        result.setFailCount(listener.getFailCount());
        result.setErrorRecords(errorRecords);
        
        // 在importProducts方法中添加导入统计和错误日志
        log.info("Excel导入完成: 总行数={}, 有效数据={}, 成功保存={}, 失败数={}", result.getTotalCount(), listener.getProcessedCount(), listener.getSuccessCount(), result.getFailCount());
        
        return result;
    }
    
    /**
     * Excel解析监听器
     */
    public class ExcelListener extends AnalysisEventListener<SupplierProductImportReqDTO> {
        private static final Logger log = LoggerFactory.getLogger(ExcelListener.class);
        
        private final List<Map<String, Object>> errorRecords;
        private final List<SupplierProductImportReqDTO> validProducts = new ArrayList<>();
        private final Map<SupplierProductImportReqDTO, Integer> rowNumMap = new HashMap<>(); // 存储行号映射
        private int totalCount = 0;
        private int rowIndex = 0; // 添加行号记录
        
        // 批处理相关字段
        private final Long enterpriseId;
        private final AtomicInteger successCount = new AtomicInteger(0);

        /**
         * 构造函数
         *
         * @param errorRecords 错误记录列表
         * @param enterpriseId 企业ID
         */
        public ExcelListener(List<Map<String, Object>> errorRecords, Long enterpriseId) {
            this.errorRecords = errorRecords;
            this.enterpriseId = enterpriseId;
        }
        
        @Override
        public void invoke(SupplierProductImportReqDTO data, AnalysisContext context) {
            totalCount++;
            rowIndex = context.readRowHolder().getRowIndex() + 1; // Excel行号(1-based)
            
            try {
                // 校验数据
                validateBasicData(data);
                
                // 存储行号映射关系
                rowNumMap.put(data, rowIndex);
                
                // 添加到有效数据列表
                validProducts.add(data);

                // 达到批处理大小时，触发批处理
                if (validProducts.size() >= BATCH_SIZE) {
                    processBatch();
                }
            } catch (Exception e) {
                // 记录错误信息
                Map<String, Object> error = new HashMap<>();
                error.put("rowNum", rowIndex); // 添加行号
                error.put("productName", data.getProductName());
                error.put("productCode", data.getProductCode());
                error.put("errorMsg", e.getMessage());
                errorRecords.add(error);
                log.error("数据校验失败: 行号={}, {}, {}", rowIndex, data, e.getMessage());
            }
        }
        
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 处理最后一批数据
            if (!validProducts.isEmpty()) {
                processBatch();
            }
            log.info("Excel解析完成，总计: {}条, 有效: {}条, 成功导入: {}条", totalCount, getProcessedCount(), successCount.get());
        }

        /**
         * 处理一批数据
         */
        private void processBatch() {
            if (validProducts.isEmpty()) {
                return;
            }

            List<SupplierProductImportReqDTO> batchList = new ArrayList<>(validProducts);
            validProducts.clear(); // 清空待处理列表

            try {
                // 处理这批数据
                int processedCount = processProductBatch(batchList);
                successCount.addAndGet(processedCount);
            } catch (Exception e) {
                log.error("批处理失败: {}", e.getMessage(), e);
                // 将整批数据记录为错误
                for (SupplierProductImportReqDTO item : batchList) {
                    Map<String, Object> error = new HashMap<>();
                    error.put("productCode", item.getProductCode());
                    error.put("productName", item.getProductName());
                    Integer rowNum = getRowNum(item);
                    if (rowNum != null) {
                        error.put("rowNum", rowNum);
                    }
                    error.put("errorMsg", "批处理失败: " + e.getMessage());
                    errorRecords.add(error);
                }
            }
        }

        /**
         * 处理一批商品数据
         *
         * @param batchList 待处理的商品数据列表
         * @return 成功处理的记录数
         */
        private int processProductBatch(List<SupplierProductImportReqDTO> batchList) {
            int successCount = 0;

            // 检查是否有商品需要处理
            if (batchList.isEmpty()) {
                return 0;
            }

            // 检查数据库中是否已存在相同编码的商品
            Set<String> productCodes = batchList.stream().map(SupplierProductImportReqDTO::getProductCode).collect(Collectors.toSet());

            //用查询接口检查商品编码是否已存在
            Map<String, Boolean> existsMap = supplierProductManageService.checkProductCodesExists(productCodes, enterpriseId);

            // 处理已存在的商品编码，添加到错误记录并从有效数据列表中移除
            List<SupplierProductImportReqDTO> filteredList = batchList;
            if (existsMap != null && !existsMap.isEmpty()) {
                filteredList = new ArrayList<>();

                for (SupplierProductImportReqDTO data : batchList) {
                    String productCode = data.getProductCode();

                    // 如果商品编码已存在且对应值为true
                    if (existsMap.containsKey(productCode) && Boolean.TRUE.equals(existsMap.get(productCode))) {
                        // 添加错误记录
                        Map<String, Object> error = new HashMap<>();
                        error.put("productCode", productCode);
                        error.put("productName", data.getProductName());
                        // 添加行号信息
                        Integer rowNum = getRowNum(data);
                        if (rowNum != null) {
                            error.put("rowNum", rowNum);
                        }
                        error.put("errorMsg", "商品编码已存在: " + productCode);
                        errorRecords.add(error);
                        log.warn("跳过已存在的商品: 行号={}, {}, {}", rowNum, productCode, data.getProductName());
                    } else {
                        // 商品不存在，加入过滤后的列表
                        filteredList.add(data);
                    }
                }

                log.info("检查商品编码: 总计={}, 已存在={}, 待处理={}", productCodes.size(), productCodes.size() - filteredList.size(), filteredList.size());
            }

            // 分组处理商品，将相同商品自编码的记录合并为一个商品的多个SKU
            Map<String, List<SupplierProductImportReqDTO>> productGroups = filteredList.stream().filter(data -> StringUtils.isNotBlank(data.getProductCode())).collect(Collectors.groupingBy(SupplierProductImportReqDTO::getProductCode));

            // 处理每个商品组
            for (Map.Entry<String, List<SupplierProductImportReqDTO>> entry : productGroups.entrySet()) {
                String productCode = entry.getKey();
                List<SupplierProductImportReqDTO> skuDataList = entry.getValue();

                if (CollUtil.isEmpty(skuDataList)) {
                    continue;
                }

                try {
                    // 检查SKU自编码是否唯一
                    Set<String> skuCodes = new HashSet<>();
                    for (SupplierProductImportReqDTO skuData : skuDataList) {
                        if (skuCodes.contains(skuData.getSkuCode())) {
                            throw new BusinessException("商品编码 " + productCode + " 包含重复的SKU自编码: " + skuData.getSkuCode());
                        }
                        skuCodes.add(skuData.getSkuCode());
                    }

                    // 使用第一条数据创建商品基本信息
                    SupplierProductImportReqDTO baseData = skuDataList.get(0);
                    SupplierProductAggregate aggregate = buildSupplierProductAggregate(baseData, enterpriseId);

                    // 如果有多个SKU，添加额外的SKU到商品中
                    if (skuDataList.size() > 1) {
                        for (int i = 1; i < skuDataList.size(); i++) {
                            SupplierProductImportReqDTO skuData = skuDataList.get(i);

                            // 创建SKU实体
                            SupplierProductSkuEntity skuEntity = createSkuEntity(skuData, enterpriseId);

                            // 添加到聚合对象中
                            aggregate.getProductSkus().add(skuEntity);
                        }
                    }

                    // 保存商品
                    SupplierProductIdVO productIdVO = supplierProductManageService.addProduct(aggregate);
                    if (productIdVO != null && productIdVO.getProductId() != null) {
                        // 记录商品新增日志
                        ProductChangeLogEntity entity = new ProductChangeLogEntity();
                        entity.setProductId(productIdVO.getProductId());
                        entity.setEnterpriseId(enterpriseId);
                        entity.setOperationType(ProductOperationTypeEnum.CREATE.getCode());
                        productChangeLogService.recordChangeLog(entity);

                        successCount++;
                        log.info("导入商品成功, productId: {}, productOuterId: {}", productIdVO.getProductId(), aggregate.getProductSpu().getProductOuterId());
                    }
                } catch (Exception e) {
                    // 单个商品保存失败，记录错误并继续
                    log.error("处理商品编码 {} 时发生错误: {}", productCode, e.getMessage(), e);

                    // 添加行号信息到错误记录中
                    SupplierProductImportReqDTO firstItem = skuDataList.get(0);
                    Integer rowNum = getRowNum(firstItem);

                    // 记录到错误列表中
                    Map<String, Object> error = new HashMap<>();
                    error.put("productCode", productCode);
                    error.put("productName", firstItem.getProductName());
                    if (rowNum != null) {
                        error.put("rowNum", rowNum);
                    }
                    error.put("errorMsg", "处理商品失败: " + e.getMessage());
                    error.put("skuCount", skuDataList.size()); // 添加该商品下的SKU数量信息
                    errorRecords.add(error);
                }
            }

            return successCount;
        }
        
        public int getTotalCount() {
            return totalCount;
        }

        public int getProcessedCount() {
            return totalCount - errorRecords.size();
        }

        public int getSuccessCount() {
            return successCount.get();
        }
        
        public int getFailCount() {
            return errorRecords.size();
        }

        public Integer getRowNum(SupplierProductImportReqDTO data) {
            return rowNumMap.get(data);
        }
        }
        
        /**
         * 验证基本数据
         */
        private void validateBasicData(SupplierProductImportReqDTO data) {
            // 校验必填字段
            if (StringUtils.isBlank(data.getProductName())) {
                throw new BusinessException("商品名称不能为空");
            }
            if (StringUtils.isBlank(data.getProductCode())) {
                throw new BusinessException("商品自编码不能为空");
            }
            if (StringUtils.isBlank(data.getSkuCode())) {
                throw new BusinessException("SKU自编码不能为空");
            }
            if (StringUtils.isBlank(data.getSpecs())) {
                throw new BusinessException("规格不能为空");
            }
            if (data.getSalePrice() == null || data.getSalePrice() <= 0) {
                throw new BusinessException("销售价格必须大于0");
            }

        // 校验分类字段
        if (StringUtils.isNotBlank(data.getProductCategory()) && data.getProductCategory().length() > 100) {
            throw new BusinessException("商品分类名称长度不能超过100字符");
            }
            
            // 添加价格合理性校验
            if (data.getCostPrice() != null && data.getCostPrice() < 0) {
                throw new BusinessException("成本价不能为负数");
            }
            if (data.getCostPrice() != null && data.getSalePrice() != null && data.getCostPrice() > data.getSalePrice()) {
                throw new BusinessException("成本价不应高于销售价");
            }

            // 在validateBasicData方法中添加更多验证规则
            if (StringUtils.isNotBlank(data.getProductName()) && data.getProductName().length() > 200) {
                throw new BusinessException("商品名称长度不能超过200字符");
            }

            // 添加图片URL验证
            if (StringUtils.isNotBlank(data.getMainImage())) {
                for (String url : data.getMainImage().split(",")) {
                    if (StringUtils.isNotBlank(url) && !isValidImageUrl(url.trim())) {
                        throw new BusinessException("主图URL格式不正确: " + url.trim());
                    }
                }
            }
        }

        // 判断图片URL有效性的辅助方法
        private boolean isValidImageUrl(String url) {
        return url.startsWith("http") && (url.endsWith(".jpg") || url.endsWith(".jpeg") || url.endsWith(".png") || url.endsWith(".gif"));
    }
    
    /**
     * 构建供应商产品聚合对象
     */
    private SupplierProductAggregate buildSupplierProductAggregate(SupplierProductImportReqDTO data, Long enterpriseId) {
        // 设置基本信息
        SupplierProductEntity productSpu = new SupplierProductEntity();
        productSpu.setEnterpriseId(enterpriseId);
        productSpu.setOrigin(ProductOriginEnum.ACCOUNT_ADD.getId());
        productSpu.setProductOuterId(data.getProductCode());
        productSpu.setPlatform(ErpPlatformTypeEnum.YHT.getValue());
        productSpu.setProductType(1); // 默认为实物商品
        productSpu.setProductName(data.getProductName());
        productSpu.setProductFullName(data.getProductName()); // 使用商品名称作为全称
        
        // 使用第一张主图作为商品主图
        if (StringUtils.isNotBlank(data.getMainImage())) {
            String firstImage = data.getMainImage().split(",")[0];
            productSpu.setProductImageUrl(firstImage);
        }
        
        productSpu.setDraftStatus(ProductDraftStatusEnum.DRAFT.getId());
        productSpu.setDistributorStatus(BooleanEnum.FALSE.getId());
        productSpu.setSaleState(ProductSaleState.OnShelve.getId());
        productSpu.setAuditStatus(AuditStatusEnum.UNREVIEWED.getId());
        productSpu.setDelStatus(ProductDelStatusEnum.PROD_NORMAL.getId());

        // 处理商品分类
        handleProductCategory(data, productSpu, enterpriseId);

        // 设置规格信息
        SupplierProductSkuEntity supplierProductSkuEntity = createSkuEntity(data, enterpriseId);
        
        List<SupplierProductSkuEntity> skuList = new ArrayList<>();
        skuList.add(supplierProductSkuEntity);

        // 处理主图
        List<SupplierProductImageVO> mainImages = new ArrayList<>();
        if (StringUtils.isNotBlank(data.getMainImage())) {
            // 处理可能含有英文逗号分隔的URL
            String[] mainImageUrls = StringUtils.isNotBlank(data.getMainImage()) ? data.getMainImage().split(",") : new String[0];
            for (String url : mainImageUrls) {
                if (StringUtils.isNotBlank(url)) {
                    SupplierProductImageVO imageVO = new SupplierProductImageVO();
                    imageVO.setEnterpriseId(enterpriseId);
                    imageVO.setImageType(ProductImageTypeEnum.MAIN.getId());
                    imageVO.setRelationType(ProductImageRelationTypeEnum.PROD.getId());
                    imageVO.setUrl(url.trim());
                    mainImages.add(imageVO);
                }
            }
        }

        // 处理详情图
        List<SupplierProductImageVO> detailImages = new ArrayList<>();
        if (StringUtils.isNotBlank(data.getDetailImage())) {
            // 处理可能含有英文逗号分隔的URL
            String[] detailImageUrls = data.getDetailImage().split(",");
            for (String url : detailImageUrls) {
                if (StringUtils.isNotBlank(url)) {
                    SupplierProductImageVO imageVO = new SupplierProductImageVO();
                    imageVO.setEnterpriseId(enterpriseId);
                    imageVO.setImageType(ProductImageTypeEnum.DESCRIPTION.getId());
                    imageVO.setRelationType(ProductImageRelationTypeEnum.PROD.getId());
                    imageVO.setUrl(url.trim());
                    detailImages.add(imageVO);
                }
            }
        }

        // 合并所有图片
        List<SupplierProductImageVO> allImages = new ArrayList<>();
        allImages.addAll(mainImages);
        allImages.addAll(detailImages);

        // 创建聚合对象
        SupplierProductAggregate aggregate = new SupplierProductAggregate();
        aggregate.setProductSpu(productSpu);
        aggregate.setProductSkus(skuList);
        aggregate.setImages(allImages);

        return aggregate;
    }
    
    /**
     * 创建SKU实体
     */
    private SupplierProductSkuEntity createSkuEntity(SupplierProductImportReqDTO skuData, Long enterpriseId) {
                        // 创建SKU实体
                        SupplierProductSkuEntity skuEntity = new SupplierProductSkuEntity();
                        skuEntity.setEnterpriseId(enterpriseId);
                        skuEntity.setProductSkuBind(0);
                        skuEntity.setProductSkuProps(skuData.getSpecs());
                        
                        // 设置SKU图片
                        if (StringUtils.isNotBlank(skuData.getSpecImage())) {
                            skuEntity.setImageUrl(skuData.getSpecImage());
                        } else if (StringUtils.isNotBlank(skuData.getMainImage())) {
                            String firstImage = skuData.getMainImage().split(",")[0];
                            skuEntity.setImageUrl(firstImage);
                        }
                        
                        skuEntity.setShowOpt(1); // 默认显示
                        
                        // 设置价格（转换为分）
                        if (skuData.getSalePrice() != null) {
            Long salePriceInCents = BigDecimal.valueOf(skuData.getSalePrice()).multiply(new BigDecimal(100)).longValue();
                            skuEntity.setSalePrice(salePriceInCents);
                            skuEntity.setMinRetailPrice(salePriceInCents);
                            skuEntity.setMaxRetailPrice(salePriceInCents);
                        }
                        
                        // 设置成本价
                        if (skuData.getCostPrice() != null) {
            Long costPriceInCents = BigDecimal.valueOf(skuData.getCostPrice()).multiply(new BigDecimal(100)).longValue();
                            skuEntity.setProductSkuCost(costPriceInCents);
                            skuEntity.setProductSkuSupplyPrice(costPriceInCents);
                        }
                        
                        // 设置库存
                        skuEntity.setStock(skuData.getStock() != null ? skuData.getStock().intValue() : 0);

        // SKU规格处理增强
        if (StringUtils.isBlank(skuData.getSpecs())) {
            skuData.setSpecs("默认规格"); // 提供默认值而不是抛出异常
        }
                        
                        // 设置SKU编码和条形码
                        skuEntity.setProductSkuOuterId(skuData.getSkuCode());
                        skuEntity.setProductSkuBarCode(skuData.getBarcode());
                        
        return skuEntity;
    }

    /**
     * 处理商品分类
     * 根据分类名称查询分类ID并设置相应的一、二、三级分类ID
     *
     * @param data         导入数据
     * @param productSpu   商品SPU实体
     * @param enterpriseId 企业ID
     */
    private void handleProductCategory(SupplierProductImportReqDTO data, SupplierProductEntity productSpu, Long enterpriseId) {
        // 如果分类名称为空，则不处理
        if (StringUtils.isBlank(data.getProductCategory())) {
            return;
        }

        try {
            // 根据分类名称查询分类
            SupplierProductCategoryEntity category = supplierProductCategoryRepository.getCategoryByName(data.getProductCategory(), enterpriseId);

            if (category == null) {
                log.warn("未找到商品分类: {}, enterpriseId: {}", data.getProductCategory(), enterpriseId);
                return;
            }

            // 获取分类级别
            Integer level = category.getLevel();
            if (level == null) {
                log.warn("分类级别为空: {}, enterpriseId: {}", data.getProductCategory(), enterpriseId);
                return;
            }

            // 根据级别处理分类ID
            if (level == 3) {
                // 3级分类，设置三级分类ID
                productSpu.setSupplierCategoryIdThree(category.getId());

                // 获取完整分类路径
                List<SupplierProductCategoryEntity> categoryPath = supplierProductCategoryRepository.getCategoryPath(category.getId(), enterpriseId);

                // 设置父级分类ID
                for (SupplierProductCategoryEntity pathCategory : categoryPath) {
                    if (pathCategory.getLevel() == 1) {
                        productSpu.setSupplierCategoryIdOne(pathCategory.getId());
                    } else if (pathCategory.getLevel() == 2) {
                        productSpu.setSupplierCategoryIdTwo(pathCategory.getId());
                    }
                }
            } else if (level == 2) {
                // 2级分类，设置二级分类ID
                productSpu.setSupplierCategoryIdTwo(category.getId());

                // 获取父级分类
                if (category.getParentId() != null && category.getParentId() > 0) {
                    SupplierProductCategoryEntity parentCategory = supplierProductCategoryRepository.getCategory(category.getParentId(), enterpriseId);
                    if (parentCategory != null) {
                        productSpu.setSupplierCategoryIdOne(parentCategory.getId());
                    }
                }
            } else if (level == 1) {
                // 1级分类，只设置一级分类ID
                productSpu.setSupplierCategoryIdOne(category.getId());
            }

            log.info("设置商品分类成功: 名称={}, 一级分类ID={}, 二级分类ID={}, 三级分类ID={}", data.getProductCategory(), productSpu.getSupplierCategoryIdOne(), productSpu.getSupplierCategoryIdTwo(), productSpu.getSupplierCategoryIdThree());
        } catch (Exception e) {
            log.error("处理商品分类出错: {}, {}", data.getProductCategory(), e.getMessage(), e);
        }
    }

    /**
     * 导出商品SKU数据到Excel
     * 
     * @param response     HTTP响应对象
     * @param enterpriseId 企业ID
     * @param queryModel   查询参数，与分页查询接口相同
     * @throws IOException IO异常
     */
    public void exportProductSkusToExcel(HttpServletResponse response, Long enterpriseId, QueryModel<SupplierProductPageQueryReqDTO> queryModel) throws IOException {
        
        try {
            OutputStream outputStream = response.getOutputStream();
            
            // 准备查询参数
            SupplierProductPageQueryReqDTO param = null;
            String productOuterId = null;
            String productName = null;
            Integer saleState = null;
            Integer auditStatus = null;
            Integer distributorStatus = null;
            
            // 从查询模型中提取查询条件
            if (queryModel != null && queryModel.getParam() != null) {
                param = queryModel.getParam();
                productOuterId = param.getProductOuterId();
                productName = param.getProductName();
                saleState = param.getSaleState();
                auditStatus = param.getAuditStatus();
                distributorStatus = param.getDistributorStatus();
                log.info("使用条件查询导出商品列表: productName={}, productOuterId={}, saleState={}, auditStatus={}, distributorStatus={}", productName, productOuterId, saleState, auditStatus, distributorStatus);
            } else {
                log.info("无条件查询全部商品");
            }
            
            // 创建ExcelWriter对象
            try (ExcelWriter excelWriter = EasyExcel.write(outputStream, SupplierProductExportDTO.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build()) {

                // 创建sheet
                WriteSheet writeSheet = EasyExcel.writerSheet("商品数据").build();
                
                // 分页查询并写入
                int totalExportCount = 0;
                
                // 查询第一页数据
                QueryModel<Void> pageQueryModel = new QueryModel<>();
                pageQueryModel.setPageSize(PAGE_SIZE);
                pageQueryModel.setPageNum(1);
                
                // 查询第一页
                PageData<SupplierProductAggregate> pageData = supplierProductManageService.querySupplierProductAggregateList(pageQueryModel, productOuterId, productName, saleState, auditStatus, distributorStatus, enterpriseId);
                
                // 获取总记录数和第一页数据
                long totalCount = pageData.getPageTotal();
                List<SupplierProductAggregate> aggregates = pageData.getPageContents();
                
                // 检查是否有数据需要导出
                if (totalCount == 0 || CollUtil.isEmpty(aggregates)) {
                    log.warn("未查询到商品数据，导出空模板");
                    excelWriter.write(Collections.emptyList(), writeSheet);
                    return;
                }
                
                log.info("查询到总记录数：{}，开始分页导出", totalCount);
                
                // 计算总页数
                int totalPages = (int) Math.ceil((double) totalCount / PAGE_SIZE);
                log.info("总页数：{}, 页大小：{}", totalPages, PAGE_SIZE);
                
                // 处理第一页数据
                totalExportCount += processAndWritePage(excelWriter, writeSheet, aggregates, 1, totalExportCount);
                
                // 处理后续页数据（如果有）
                for (int pageNum = 2; pageNum <= totalPages; pageNum++) {
                    pageQueryModel.setPageNum(pageNum);
                    
                    // 查询当前页数据
                    pageData = supplierProductManageService.querySupplierProductAggregateList(pageQueryModel, productOuterId, productName, saleState, auditStatus, distributorStatus, enterpriseId);
                    
                    aggregates = pageData.getPageContents();
                    
                    if (CollUtil.isEmpty(aggregates)) {
                        log.warn("第{}页未查询到数据，跳过", pageNum);
                        continue;
                    }
                    
                    // 处理当前页数据并更新导出计数
                    totalExportCount += processAndWritePage(excelWriter, writeSheet, aggregates, pageNum, totalExportCount);
                }
                
                log.info("导出完成，总共导出{}条记录", totalExportCount);
            }
            
        } catch (Exception e) {
            log.error("导出Excel异常: {}", e.getMessage(), e);
            throw new IOException("导出Excel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理并写入一页数据
     *
     * @param excelWriter  Excel写入器
     * @param writeSheet   写入的Sheet
     * @param aggregates   当前页商品聚合对象列表
     * @param pageNum      页码
     * @param currentCount 当前已处理的记录数
     * @return 当前页处理的记录数
     */
    private int processAndWritePage(ExcelWriter excelWriter, com.alibaba.excel.write.metadata.WriteSheet writeSheet, List<SupplierProductAggregate> aggregates, int pageNum, int currentCount) {
        log.info("正在处理第{}页数据，共{}条记录", pageNum, aggregates.size());

        // 构建当前页的导出数据
        List<SupplierProductExportDTO> currentPageData = new ArrayList<>(aggregates.size() * 2); // 预分配容量

        // 处理每个商品聚合对象
        for (SupplierProductAggregate aggregate : aggregates) {
            SupplierProductEntity product = aggregate.getProductSpu();
            if (product == null) {
                continue;
            }

            List<SupplierProductSkuEntity> skuList = aggregate.getProductSkus();
            if (CollUtil.isEmpty(skuList)) {
                continue;
            }

            // 处理每个SKU
            for (SupplierProductSkuEntity sku : skuList) {
                SupplierProductExportDTO exportDTO = new SupplierProductExportDTO();

                // 设置商品基本信息
                exportDTO.setProductName(product.getProductName());
                exportDTO.setProductCode(product.getProductOuterId());

                // 设置SKU信息
                exportDTO.setSkuCode(sku.getProductSkuOuterId());
                exportDTO.setSpecs(sku.getProductSkuProps());

                // 价格转换为元
                if (sku.getSalePrice() != null) {
                    exportDTO.setSalePrice(new BigDecimal(sku.getSalePrice()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if (sku.getProductSkuCost() != null) {
                    exportDTO.setCostPrice(new BigDecimal(sku.getProductSkuCost()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }

                // 设置库存
                exportDTO.setStock(sku.getStock());

                // 设置条形码
                exportDTO.setBarcode(sku.getProductSkuBarCode());

                // 设置状态描述
                exportDTO.setSaleStateDesc(getSaleStateDesc(product.getSaleState()));

                currentPageData.add(exportDTO);
            }
        }

        // 将当前页数据写入Excel
        if (!currentPageData.isEmpty()) {
            excelWriter.write(currentPageData, writeSheet);
            log.info("已写入第{}页数据，当前页{}条，累计{}条", pageNum, currentPageData.size(), currentCount + currentPageData.size());
            return currentPageData.size();
        }

        return 0;
    }

    /**
     * 获取上架状态描述
     */
    private String getSaleStateDesc(Integer saleState) {
        if (saleState == null) {
            return "";
        }
        if (saleState.equals(ProductSaleState.OnShelve.getId())) {
            return "已上架";
        } else if (saleState.equals(ProductSaleState.OnSale.getId())) {
            return "已下架";
        } else {
            return "未知状态";
        }
    }
} 