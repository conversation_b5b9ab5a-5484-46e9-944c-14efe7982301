package com.yaotown.ecommate.product.trigger.biz.management.model.request;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 运费模板批量请求DTO
 */
@Data
public class FreightTemplateBatchReqDTO {

    /**
     * 模板项列表
     */
    @NotEmpty(message = "模板项列表不能为空")
    @Valid
    private List<FreightTemplateReqDTO> templateItems;
} 