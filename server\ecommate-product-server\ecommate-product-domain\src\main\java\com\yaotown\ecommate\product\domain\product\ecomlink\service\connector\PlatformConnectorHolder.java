package com.yaotown.ecommate.product.domain.product.ecomlink.service.connector;


import com.yaotown.ecommate.common.core.util.spring.SpringUtils;
import com.yaotown.ecommate.product.domain.product.ecomlink.adapter.ErpPlatformConnectorProperties;
import com.yaotown.ecommate.product.domain.product.ecomlink.service.connector.impl.ErpPlatformConnectorDecorator;
import com.yaotown.ecommate.product.domain.product.ecomlink.service.connector.impl.ErpPlatformConnectorV2Decorator;
import com.yaotown.ecommate.product.domain.product.ecomlink.service.connector.impl.ErpPlatformConnectorV3Decorator;
import com.yaotown.ecommate.product.types.enums.erp.ErpPlatformTypeEnum;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * channel->Handler的映射关系
 *
 * <AUTHOR>
 */
@Component
public class PlatformConnectorHolder {

    private Map<String, IPlatformConnector<?>> connectors = new HashMap<>(128);

    @Resource
    private ErpPlatformConnectorProperties erpPlatformConnectorProperties;

    public void putHandler(String platform, IPlatformConnector connector) {
        // 如果是SPHXD平台，直接放入map中，不需要额外处理
        if (platform.equals(ErpPlatformTypeEnum.SPHXD.getValue())) {
            connectors.put(platform, connector);
            return;
        }
        if (erpPlatformConnectorProperties.getEnabled()) {
            if (erpPlatformConnectorProperties.getEnabledV2Connector()){
                ErpPlatformConnectorV2Decorator erpPlatformConnectorDecorator = SpringUtils.getBean(ErpPlatformConnectorV2Decorator.class);
                erpPlatformConnectorDecorator.setConnector(connector);
                connectors.put(platform, erpPlatformConnectorDecorator);
                return;
            }
            if (erpPlatformConnectorProperties.getEnabledV3Connector()){
                ErpPlatformConnectorV3Decorator erpPlatformConnectorDecorator = SpringUtils.getBean(ErpPlatformConnectorV3Decorator.class);
                erpPlatformConnectorDecorator.setConnector(connector);
                connectors.put(platform, erpPlatformConnectorDecorator);
                return;
            }
            ErpPlatformConnectorDecorator erpPlatformConnectorDecorator = SpringUtils.getBean(ErpPlatformConnectorDecorator.class);
            erpPlatformConnectorDecorator.setConnector(connector);
            connectors.put(platform, erpPlatformConnectorDecorator);
            return;
        }
        connectors.put(platform, connector);
    }

    public IPlatformConnector<?> route(String platform) {
        return connectors.get(platform);
    }

    /**
     * 添加商品响应对象
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Accessors(chain = true)
    public static class AddPlatformProductResponse implements Serializable {
        /**
         * 平台产品id
         */
        private String platformProductId;
    }

}
