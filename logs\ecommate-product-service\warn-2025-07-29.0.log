2025-07-29 16:13:40.949 [main] WARN  [ecommate-product-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-product-server] & group[DEFAULT_GROUP]
2025-07-29 16:13:40.955 [main] WARN  [ecommate-product-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-product-server.yaml] & group[DEFAULT_GROUP]
2025-07-29 16:13:45.277 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.281 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.283 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$942/0x000002308c8749a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.291 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.359 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration' of type [com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [environmentSetup] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-29 16:13:45.363 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq.enhance-com.yaotown.sdk.mq.properties.RocketEnhanceProperties' of type [com.yaotown.sdk.mq.properties.RocketEnhanceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.369 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.373 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.627 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.633 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.637 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:45.687 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-29 16:13:45.691 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:13:46.051 [main] WARN  [ecommate-product-server] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-29 16:13:50.524 [main] WARN  [ecommate-product-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.product.infrastructure.po.listing.ProductCollectionPO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-29 16:14:17.804 [com.alibaba.nacos.client.naming.grpc.redo.0] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - Grpc Connection is disconnect, skip current redo task
2025-07-29 16:15:03.537 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 74827
2025-07-29 16:15:03.555 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 75032
2025-07-29 16:15:03.572 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 75444
2025-07-29 16:15:03.588 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 75725
2025-07-29 16:15:03.606 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 75920
2025-07-29 16:19:39.087 [XNIO-1 task-2] WARN  [ecommate-product-server] com.alibaba.druid.pool.DruidAbstractDataSource - discard long time none received connection. , jdbcUrl : *******************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 274912
2025-07-29 16:35:27.188 [Thread-3] WARN  [ecommate-product-server] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-29 16:35:27.188 [Thread-5] WARN  [ecommate-product-server] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-29 16:35:27.189 [Thread-5] WARN  [ecommate-product-server] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-29 16:35:27.190 [Thread-3] WARN  [ecommate-product-server] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-07-29 16:41:27.007 [main] WARN  [ecommate-product-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-product-server] & group[DEFAULT_GROUP]
2025-07-29 16:41:27.011 [main] WARN  [ecommate-product-server] c.a.cloud.nacos.client.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[ecommate-product-server.yaml] & group[DEFAULT_GROUP]
2025-07-29 16:41:31.204 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.211 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.213 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$942/0x000001fdec8791c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.219 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.295 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration' of type [com.yaotown.sdk.mq.config.RocketMQEnhanceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [environmentSetup] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-29 16:41:31.301 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq.enhance-com.yaotown.sdk.mq.properties.RocketEnhanceProperties' of type [com.yaotown.sdk.mq.properties.RocketEnhanceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [environmentSetup]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.309 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.311 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.604 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.609 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.612 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:31.653 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-29 16:41:31.658 [main] WARN  [ecommate-product-server] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-29 16:41:32.002 [main] WARN  [ecommate-product-server] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-29 16:41:35.931 [main] WARN  [ecommate-product-server] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.yaotown.ecommate.product.infrastructure.po.listing.ProductCollectionPO ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-29 16:41:49.920 [com.alibaba.nacos.client.naming.grpc.redo.0] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - Grpc Connection is disconnect, skip current redo task
2025-07-29 16:43:29.935 [Thread-3] WARN  [ecommate-product-server] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-29 16:43:29.935 [Thread-5] WARN  [ecommate-product-server] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-29 16:43:29.935 [Thread-5] WARN  [ecommate-product-server] com.alibaba.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-29 16:43:29.936 [Thread-3] WARN  [ecommate-product-server] c.alibaba.nacos.common.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] WARN  [ecommate-product-server] com.alibaba.nacos.client.naming - [NamingHttpClientManager] Destruction of the end
