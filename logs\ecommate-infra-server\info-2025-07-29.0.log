2025-07-29 16:13:33.434 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848
2025-07-29 16:13:33.436 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000
2025-07-29 16:13:33.437 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0
2025-07-29 16:13:33.440 [Thread-2] INFO  [ecommate-infra-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-07-29 16:13:33.456 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels.....
2025-07-29 16:13:33.456 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-07-29 16:13:33.456 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null
2025-07-29 16:13:33.457 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect properties labels: {}
2025-07-29 16:13:33.457 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null
2025-07-29 16:13:33.457 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect jvm labels: {}
2025-07-29 16:13:33.457 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect env raw labels: null
2025-07-29 16:13:33.457 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - default nacos collect env labels: {}
2025-07-29 16:13:33.458 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-07-29 16:13:33.465 [main] INFO  [ecommate-infra-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:13:33.465 [main] INFO  [ecommate-infra-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:13:33.496 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2025-07-29 16:13:33.614 [main] INFO  [ecommate-infra-server] c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-07-29 16:13:33.618 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0
2025-07-29 16:13:33.629 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$636/0x0000018a4c3bcf20
2025-07-29 16:13:33.630 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$637/0x0000018a4c3bd340
2025-07-29 16:13:33.630 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-29 16:13:33.630 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-29 16:13:33.635 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-29 16:13:33.651 [main] INFO  [ecommate-infra-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:13:34.227 [main] INFO  [ecommate-infra-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-29 16:13:34.229 [main] INFO  [ecommate-infra-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-29 16:13:34.229 [main] INFO  [ecommate-infra-server] c.a.n.common.ability.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-29 16:13:34.230 [main] INFO  [ecommate-infra-server] c.a.n.c.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-29 16:13:34.244 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1753776807287_192.168.48.1_53901
2025-07-29 16:13:34.245 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] Notify connected event to listeners.
2025-07-29 16:13:34.245 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-29 16:13:34.245 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.ClientWorker - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] Connected,notify listen context...
2025-07-29 16:13:34.245 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [3cc74f9c-0a3e-4399-a4c7-45a80a7217b4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$657/0x0000018a4c537178
2025-07-29 16:13:34.277 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.utils.JvmUtil - isMultiInstance:false
2025-07-29 16:13:34.477 [main] INFO  [ecommate-infra-server] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-ecommate-infra-server-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-infra-server.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-infra-server,DEFAULT_GROUP'}]
2025-07-29 16:13:34.506 [main] INFO  [ecommate-infra-server] com.yaotown.ecommate.infra.web.InfraApplication - The following 1 profile is active: "local"
2025-07-29 16:13:36.282 [main] INFO  [ecommate-infra-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 16:13:36.285 [main] INFO  [ecommate-infra-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 16:13:36.303 [main] INFO  [ecommate-infra-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-07-29 16:13:36.630 [main] INFO  [ecommate-infra-server] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=65eeff2a-3e87-3183-91b9-d8ac601b838a
2025-07-29 16:13:37.889 [main] INFO  [ecommate-infra-server] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 16:13:37.889 [main] INFO  [ecommate-infra-server] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3371 ms
2025-07-29 16:13:39.780 [main] INFO  [ecommate-infra-server] org.redisson.Version - Redisson 3.32.0
2025-07-29 16:13:40.052 [redisson-netty-1-4] INFO  [ecommate-infra-server] org.redisson.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6379
2025-07-29 16:13:40.242 [redisson-netty-1-19] INFO  [ecommate-infra-server] org.redisson.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6379
2025-07-29 16:13:41.689 [main] INFO  [ecommate-infra-server] o.a.r.s.autoconfigure.RocketMQAutoConfiguration - a producer (ecommate-infra-server) init on namesrv **************:9876
2025-07-29 16:13:43.193 [main] INFO  [ecommate-infra-server] c.a.c.config.AjCaptchaServiceAutoConfiguration - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='源汇通', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=10, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
2025-07-29 16:13:43.196 [main] INFO  [ecommate-infra-server] c.anji.captcha.service.impl.CaptchaServiceFactory - supported-captchaCache-service:[local]
2025-07-29 16:13:43.203 [main] INFO  [ecommate-infra-server] c.anji.captcha.service.impl.CaptchaServiceFactory - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-07-29 16:13:43.229 [main] INFO  [ecommate-infra-server] com.anji.captcha.util.ImageUtils - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@65f913fd, ROTATE=[Ljava.lang.String;@56059cf1, ORIGINAL=[Ljava.lang.String;@3b89bccf, PIC_CLICK=[Ljava.lang.String;@299a0651, ROTATE_BLOCK=[Ljava.lang.String;@4518e64]
2025-07-29 16:13:43.229 [main] INFO  [ecommate-infra-server] c.a.c.service.impl.BlockPuzzleCaptchaServiceImpl - --->>>初始化验证码底图<<<---blockPuzzle
2025-07-29 16:13:43.362 [main] INFO  [ecommate-infra-server] c.a.c.service.impl.BlockPuzzleCaptchaServiceImpl - 初始化local缓存...
2025-07-29 16:13:44.036 [main] INFO  [ecommate-infra-server] c.y.s.m.module.handler.handler.impl.EmailHandler - EmailHandler#init Rate Limit Strategy: requestRateLimit Value: 5.0
2025-07-29 16:13:44.145 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.im.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.im.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.im.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.push.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.push.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.push.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.sms.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.sms.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.sms.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.email.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.148 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.email.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.email.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.official_accounts.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.official_accounts.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.official_accounts.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.mini_program.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.mini_program.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.mini_program.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.149 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_robot.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_robot.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_robot.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_work_notice.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_work_notice.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.ding_ding_work_notice.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat_robot.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat_robot.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.enterprise_we_chat_robot.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_robot.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.150 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_robot.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.151 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_robot.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.151 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.alipay_mini_program.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.151 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.alipay_mini_program.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.151 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.alipay_mini_program.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.151 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_application_robot.notice, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.151 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_application_robot.marketing, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.151 [main] INFO  [ecommate-infra-server] org.dromara.dynamictp.core.DtpRegistry - DynamicTp register executor: TpMainFields(threadPoolName=message.fei_shu_application_robot.auth_code, corePoolSize=2, maxPoolSize=2, keepAliveTime=60, queueType=VariableLinkedBlockingQueue, queueCapacity=128, rejectType=CallerRunsPolicy, allowCoreThreadTimeOut=false), source: austin
2025-07-29 16:13:44.451 [main] INFO  [ecommate-infra-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:rocketMqBizReceiver, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-29 16:13:44.680 [main] INFO  [ecommate-infra-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:rocketMqRecallReceiver, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-29 16:13:47.271 [main] INFO  [ecommate-infra-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:13:47.646 [main] INFO  [ecommate-infra-server] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-29 16:13:47.903 [main] INFO  [ecommate-infra-server] c.y.ecommate.common.id.config.IdAutoConfiguration - 构建ID生成器时使用随机workId，它的值为: 45
2025-07-29 16:13:49.759 [main] INFO  [ecommate-infra-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:13:50.020 [main] INFO  [ecommate-infra-server] io.undertow - starting server: Undertow - 2.3.13.Final
2025-07-29 16:13:50.033 [main] INFO  [ecommate-infra-server] org.xnio - XNIO version 3.8.8.Final
2025-07-29 16:13:50.050 [main] INFO  [ecommate-infra-server] org.xnio.nio - XNIO NIO Implementation Version 3.8.8.Final
2025-07-29 16:13:50.080 [main] INFO  [ecommate-infra-server] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 16:13:50.138 [main] INFO  [ecommate-infra-server] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 38785 (http) with context path '/'
2025-07-29 16:13:50.141 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - initializer namespace from ans.namespace attribute : null
2025-07-29 16:13:50.142 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-07-29 16:13:50.142 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - initializer namespace from namespace attribute :null
2025-07-29 16:13:50.147 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-07-29 16:13:50.150 [main] INFO  [ecommate-infra-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:13:50.151 [main] INFO  [ecommate-infra-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:13:50.219 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 030741cd-ed2c-4dd4-ac7e-298da5ca33e9
2025-07-29 16:13:50.220 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - Create naming rpc client for uuid->030741cd-ed2c-4dd4-ac7e-298da5ca33e9
2025-07-29 16:13:50.221 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [030741cd-ed2c-4dd4-ac7e-298da5ca33e9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-07-29 16:13:50.221 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [030741cd-ed2c-4dd4-ac7e-298da5ca33e9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-07-29 16:13:50.221 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [030741cd-ed2c-4dd4-ac7e-298da5ca33e9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-07-29 16:13:50.221 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [030741cd-ed2c-4dd4-ac7e-298da5ca33e9] Try to connect to server on start up, server: {serverIp = '**************', server main port = 9848}
2025-07-29 16:13:50.221 [main] INFO  [ecommate-infra-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:10848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:13:53.238 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [030741cd-ed2c-4dd4-ac7e-298da5ca33e9] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-29 16:13:53.238 [main] INFO  [ecommate-infra-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:13:53.264 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [030741cd-ed2c-4dd4-ac7e-298da5ca33e9] Success to connect to server [**************:8848] on start up, connectionId = 1753776826370_192.168.48.1_53995
2025-07-29 16:13:53.265 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [030741cd-ed2c-4dd4-ac7e-298da5ca33e9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-29 16:13:53.265 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [030741cd-ed2c-4dd4-ac7e-298da5ca33e9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$657/0x0000018a4c537178
2025-07-29 16:13:53.266 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - [030741cd-ed2c-4dd4-ac7e-298da5ca33e9] Notify connected event to listeners.
2025-07-29 16:13:53.266 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - Grpc connection connect
2025-07-29 16:13:53.268 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - [REGISTER-SERVICE] ecommate-cfx registering service ecommate-infra-server with instance Instance{instanceId='null', ip='*************', port=38785, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={preserved.heart.beat.timeout=3000, preserved.ip.delete.timeout=3000, preserved.register.source=SPRING_CLOUD, version=1.0.0, IPv6=null, preserved.heart.beat.interval=1000}}
2025-07-29 16:13:53.285 [main] INFO  [ecommate-infra-server] c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ecommate-infra-server *************:38785 register finished
2025-07-29 16:13:54.557 [main] INFO  [ecommate-infra-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:13:58.648 [main] INFO  [ecommate-infra-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='unique-send-consumer-group_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='message-send_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='com.yaotown.sdk.message.module.ctsmd', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:14:02.780 [main] INFO  [ecommate-infra-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='message-recall_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='message-recall_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='com.yaotown.sdk.message.module.ctsmd', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:14:02.818 [main] INFO  [ecommate-infra-server] com.yaotown.ecommate.infra.web.InfraApplication - Started InfraApplication in 32.687 seconds (process running for 33.517)
2025-07-29 16:14:02.826 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - config listener notify warn timeout millis use default 60000 millis 
2025-07-29 16:14:02.827 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snapshot = true 
2025-07-29 16:14:02.828 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-infra-server+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:14:02.835 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-infra-server, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:14:02.835 [main] INFO  [ecommate-infra-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-infra-server, group=DEFAULT_GROUP
2025-07-29 16:14:02.839 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-infra-server-local.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:14:02.839 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-infra-server-local.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:14:02.839 [main] INFO  [ecommate-infra-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-infra-server-local.yaml, group=DEFAULT_GROUP
2025-07-29 16:14:02.839 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-infra-server.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:14:02.840 [main] INFO  [ecommate-infra-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-infra-server.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:14:02.841 [main] INFO  [ecommate-infra-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-infra-server.yaml, group=DEFAULT_GROUP
2025-07-29 16:14:03.339 [RMI TCP Connection(1)-*************] INFO  [ecommate-infra-server] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:14:03.340 [RMI TCP Connection(1)-*************] INFO  [ecommate-infra-server] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 16:14:03.344 [RMI TCP Connection(1)-*************] INFO  [ecommate-infra-server] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-29 16:14:03.481 [RMI TCP Connection(2)-*************] INFO  [ecommate-infra-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-29 16:15:03.202 [XNIO-1 task-2] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/rpc-api/infra/oauth2/token/check) 参数({accessToken=8978da2c-d133-410c-8166-6ed3801383ff})]
2025-07-29 16:15:03.318 [XNIO-1 task-2] INFO  [ecommate-infra-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/rpc-api/infra/oauth2/token/check) 耗时(116 ms)]
2025-07-29 16:20:00.021 [scheduling-1] INFO  [ecommate-infra-server] c.y.e.infra.module.task.MessagePushScheduledTask - 开始扫描消息推送任务
2025-07-29 16:20:00.023 [scheduling-1] INFO  [ecommate-infra-server] c.y.e.infra.module.task.MessagePushScheduledTask - 扫描消息推送任务结束
2025-07-29 16:40:00.001 [scheduling-1] INFO  [ecommate-infra-server] c.y.e.infra.module.task.MessagePushScheduledTask - 开始扫描消息推送任务
2025-07-29 16:40:00.001 [scheduling-1] INFO  [ecommate-infra-server] c.y.e.infra.module.task.MessagePushScheduledTask - 扫描消息推送任务结束
2025-07-29 16:43:32.706 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] c.y.s.m.m.s.c.ThreadPoolExecutorShutdownDefinition - 容器关闭前处理线程池优雅关闭开始, 当前要处理的线程池数量为: 39 >>>>>>>>>>>>>>>>
2025-07-29 16:43:32.727 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] io.undertow - stopping server: Undertow - 2.3.13.Final
2025-07-29 16:43:32.730 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 16:43:32.737 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 16:43:32.737 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] ecommate-cfx deregistering service ecommate-infra-server with instance: Instance{instanceId='null', ip='*************', port=38785, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
2025-07-29 16:43:32.744 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-07-29 16:43:32.745 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-29 16:43:32.745 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->030741cd-ed2c-4dd4-ac7e-298da5ca33e9
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@371e35d0[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 593]
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@67e254fc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.common.remote.client - Close current connection 1753776826370_192.168.48.1_53995
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] c.a.nacos.common.remote.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@601aa72a[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 352]
2025-07-29 16:43:32.746 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->030741cd-ed2c-4dd4-ac7e-298da5ca33e9
2025-07-29 16:43:32.747 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] c.a.n.client.auth.ram.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-29 16:43:32.747 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] c.a.n.client.auth.ram.identify.CredentialService - [null] CredentialService is freed
2025-07-29 16:43:32.747 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
2025-07-29 16:43:32.751 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='message-recall_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='message-recall_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='com.yaotown.sdk.message.module.ctsmd', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:43:32.751 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='unique-send-consumer-group_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='message-send_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='com.yaotown.sdk.message.module.ctsmd', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:43:34.149 [com.alibaba.nacos.client.Worker.13] INFO  [ecommate-infra-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-07-29 16:43:36.587 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-29 16:43:36.592 [SpringApplicationShutdownHook] INFO  [ecommate-infra-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
