2025-07-29 16:13:28.695 [background-preinit] INFO  [ecommate-gateway,,,] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 16:13:30.327 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848
2025-07-29 16:13:30.327 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000
2025-07-29 16:13:30.329 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0
2025-07-29 16:13:30.332 [Thread-2] INFO  [ecommate-gateway,,,] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-07-29 16:13:30.352 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels.....
2025-07-29 16:13:30.352 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-07-29 16:13:30.352 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null
2025-07-29 16:13:30.353 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.labels - default nacos collect properties labels: {}
2025-07-29 16:13:30.353 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null
2025-07-29 16:13:30.353 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.labels - default nacos collect jvm labels: {}
2025-07-29 16:13:30.353 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.labels - default nacos collect env raw labels: null
2025-07-29 16:13:30.353 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.labels - default nacos collect env labels: {}
2025-07-29 16:13:30.354 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-07-29 16:13:30.363 [main] INFO  [ecommate-gateway,,,] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:13:30.363 [main] INFO  [ecommate-gateway,,,] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:13:30.411 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2025-07-29 16:13:30.540 [main] INFO  [ecommate-gateway,,,] c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-07-29 16:13:30.544 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 6737ff16-db3a-4624-98a2-6d5ad981c488_config-0
2025-07-29 16:13:30.558 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$631/0x0000026af53c6dc0
2025-07-29 16:13:30.558 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$632/0x0000026af53c71e0
2025-07-29 16:13:30.558 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-29 16:13:30.558 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-29 16:13:30.563 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-29 16:13:30.583 [main] INFO  [ecommate-gateway,,,] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:13:31.245 [main] INFO  [ecommate-gateway,,,] c.a.n.common.ability.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-29 16:13:31.246 [main] INFO  [ecommate-gateway,,,] c.a.n.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-29 16:13:31.247 [main] INFO  [ecommate-gateway,,,] c.a.n.common.ability.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-29 16:13:31.247 [main] INFO  [ecommate-gateway,,,] c.a.n.c.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-29 16:13:31.264 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1753776804307_192.168.48.1_53874
2025-07-29 16:13:31.264 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] Notify connected event to listeners.
2025-07-29 16:13:31.264 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.ClientWorker - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] Connected,notify listen context...
2025-07-29 16:13:31.264 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-29 16:13:31.265 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6737ff16-db3a-4624-98a2-6d5ad981c488_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$652/0x0000026af5540870
2025-07-29 16:13:31.504 [main] INFO  [ecommate-gateway,,,] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-ecommate-gateway-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-gateway.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-gateway,DEFAULT_GROUP'}]
2025-07-29 16:13:31.508 [main] INFO  [ecommate-gateway,,,] com.yaotown.gateway.GatewayApplication - The following 1 profile is active: "local"
2025-07-29 16:13:32.298 [main] INFO  [ecommate-gateway,,,] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 16:13:32.301 [main] INFO  [ecommate-gateway,,,] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 16:13:32.331 [main] INFO  [ecommate-gateway,,,] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-29 16:13:32.499 [main] INFO  [ecommate-gateway,,,] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=66aaf91f-04e2-325e-896f-270929718886
2025-07-29 16:13:33.544 [main] INFO  [ecommate-gateway,,,] org.redisson.Version - Redisson 3.32.0
2025-07-29 16:13:33.808 [redisson-netty-3-4] INFO  [ecommate-gateway,,,] org.redisson.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6379
2025-07-29 16:13:34.118 [redisson-netty-3-19] INFO  [ecommate-gateway,,,] org.redisson.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6379
2025-07-29 16:13:35.644 [main] INFO  [ecommate-gateway,,,] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:13:36.905 [main] INFO  [ecommate-gateway,,,] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:13:38.043 [main] INFO  [ecommate-gateway,,,] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:13:38.262 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-29 16:13:38.262 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-29 16:13:38.262 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-29 16:13:38.262 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-29 16:13:38.262 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-29 16:13:38.262 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-29 16:13:38.262 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-29 16:13:38.262 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-29 16:13:38.262 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-29 16:13:38.263 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBody]
2025-07-29 16:13:38.263 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-29 16:13:38.263 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-07-29 16:13:38.263 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-29 16:13:38.263 [main] INFO  [ecommate-gateway,,,] o.s.c.gateway.route.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-29 16:13:38.505 [main] INFO  [ecommate-gateway,,,] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-29 16:13:38.839 [main] INFO  [ecommate-gateway,,,] o.s.boot.web.embedded.netty.NettyWebServer - Netty started on port 38787 (http)
2025-07-29 16:13:39.964 [main] INFO  [ecommate-gateway,,,] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:13:41.112 [main] INFO  [ecommate-gateway,,,] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:13:41.116 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - initializer namespace from ans.namespace attribute : null
2025-07-29 16:13:41.116 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-07-29 16:13:41.116 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - initializer namespace from namespace attribute :null
2025-07-29 16:13:41.120 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-07-29 16:13:41.124 [main] INFO  [ecommate-gateway,,,] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:13:41.124 [main] INFO  [ecommate-gateway,,,] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:13:41.193 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 6bd30133-6c75-44da-b6bf-36ff90260b3e
2025-07-29 16:13:41.194 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - Create naming rpc client for uuid->6bd30133-6c75-44da-b6bf-36ff90260b3e
2025-07-29 16:13:41.194 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-07-29 16:13:41.194 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-07-29 16:13:41.194 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-07-29 16:13:41.194 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-29 16:13:41.195 [main] INFO  [ecommate-gateway,,,] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:13:41.206 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Success to connect to server [**************:8848] on start up, connectionId = 1753776814321_192.168.48.1_53936
2025-07-29 16:13:41.206 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-29 16:13:41.206 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Notify connected event to listeners.
2025-07-29 16:13:41.206 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$652/0x0000026af5540870
2025-07-29 16:13:41.206 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - Grpc connection connect
2025-07-29 16:13:41.207 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [REGISTER-SERVICE] ecommate-cfx registering service ecommate-gateway with instance Instance{instanceId='null', ip='*************', port=38787, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={preserved.heart.beat.timeout=3000, preserved.ip.delete.timeout=3000, preserved.register.source=SPRING_CLOUD, version=1.0.0, IPv6=null, preserved.heart.beat.interval=1000}}
2025-07-29 16:13:41.213 [main] INFO  [ecommate-gateway,,,] c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ecommate-gateway *************:38787 register finished
2025-07-29 16:13:41.253 [boundedElastic-3] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:ecommate-gateway, group:DEFAULT_GROUP, clusters: 
2025-07-29 16:13:41.253 [boundedElastic-3] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:ecommate-gateway, group:DEFAULT_GROUP, cluster: 
2025-07-29 16:13:41.253 [boundedElastic-4] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:ecommate-gateway, group:DEFAULT_GROUP, clusters: 
2025-07-29 16:13:41.253 [boundedElastic-4] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:ecommate-gateway, group:DEFAULT_GROUP, cluster: 
2025-07-29 16:13:41.262 [boundedElastic-3] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@ecommate-gateway -> [{"instanceId":"*************#38787##DEFAULT_GROUP@@ecommate-gateway","ip":"*************","port":38787,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-gateway","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:13:41.262 [boundedElastic-4] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@ecommate-gateway -> [{"instanceId":"*************#38787##DEFAULT_GROUP@@ecommate-gateway","ip":"*************","port":38787,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-gateway","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:13:41.266 [boundedElastic-4] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@ecommate-gateway -> [{"instanceId":"*************#38787##DEFAULT_GROUP@@ecommate-gateway","ip":"*************","port":38787,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-gateway","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:13:41.266 [boundedElastic-3] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@ecommate-gateway -> [{"instanceId":"*************#38787##DEFAULT_GROUP@@ecommate-gateway","ip":"*************","port":38787,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-gateway","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:13:41.298 [main] INFO  [ecommate-gateway,,,] c.a.c.n.discovery.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-29 16:13:41.313 [main] INFO  [ecommate-gateway,,,] com.yaotown.gateway.GatewayApplication - Started GatewayApplication in 14.326 seconds (process running for 15.012)
2025-07-29 16:13:41.318 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.CacheData - config listener notify warn timeout millis use default 60000 millis 
2025-07-29 16:13:41.318 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snapshot = true 
2025-07-29 16:13:41.319 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848] [subscribe] ecommate-gateway-local.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:13:41.320 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-gateway-local.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:13:41.320 [main] INFO  [ecommate-gateway,,,] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-gateway-local.yaml, group=DEFAULT_GROUP
2025-07-29 16:13:41.320 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848] [subscribe] ecommate-gateway+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:13:41.320 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-gateway, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:13:41.320 [main] INFO  [ecommate-gateway,,,] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-gateway, group=DEFAULT_GROUP
2025-07-29 16:13:41.321 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848] [subscribe] ecommate-gateway.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:13:41.321 [main] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-gateway.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:13:41.321 [main] INFO  [ecommate-gateway,,,] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-gateway.yaml, group=DEFAULT_GROUP
2025-07-29 16:13:41.759 [nacos-grpc-client-executor-**************-20] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Receive server push request, request = NotifySubscriberRequest, requestId = 497
2025-07-29 16:13:41.760 [nacos-grpc-client-executor-**************-20] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Ack server push request, request = NotifySubscriberRequest, requestId = 497
2025-07-29 16:14:11.313 [boundedElastic-1] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:ecommate-infra-server, group:DEFAULT_GROUP, clusters: 
2025-07-29 16:14:11.313 [boundedElastic-4] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:ecommate-infra-server, group:DEFAULT_GROUP, clusters: 
2025-07-29 16:14:11.313 [boundedElastic-1] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:ecommate-infra-server, group:DEFAULT_GROUP, cluster: 
2025-07-29 16:14:11.313 [boundedElastic-4] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:ecommate-infra-server, group:DEFAULT_GROUP, cluster: 
2025-07-29 16:14:11.319 [boundedElastic-1] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@ecommate-infra-server -> [{"instanceId":"*************#38785##DEFAULT_GROUP@@ecommate-infra-server","ip":"*************","port":38785,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-infra-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:14:11.322 [boundedElastic-1] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@ecommate-infra-server -> [{"instanceId":"*************#38785##DEFAULT_GROUP@@ecommate-infra-server","ip":"*************","port":38785,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-infra-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:14:11.887 [nacos-grpc-client-executor-**************-31] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Receive server push request, request = NotifySubscriberRequest, requestId = 499
2025-07-29 16:14:11.888 [nacos-grpc-client-executor-**************-31] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Ack server push request, request = NotifySubscriberRequest, requestId = 499
2025-07-29 16:14:41.323 [boundedElastic-2] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:ecommate-product-server, group:DEFAULT_GROUP, clusters: 
2025-07-29 16:14:41.323 [boundedElastic-2] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:ecommate-product-server, group:DEFAULT_GROUP, cluster: 
2025-07-29 16:14:41.324 [boundedElastic-6] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:ecommate-product-server, group:DEFAULT_GROUP, clusters: 
2025-07-29 16:14:41.324 [boundedElastic-6] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:ecommate-product-server, group:DEFAULT_GROUP, cluster: 
2025-07-29 16:14:41.328 [boundedElastic-2] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@ecommate-product-server -> [{"instanceId":"*************#38786##DEFAULT_GROUP@@ecommate-product-server","ip":"*************","port":38786,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-product-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:14:41.332 [boundedElastic-2] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@ecommate-product-server -> [{"instanceId":"*************#38786##DEFAULT_GROUP@@ecommate-product-server","ip":"*************","port":38786,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-product-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:14:41.908 [nacos-grpc-client-executor-**************-44] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Receive server push request, request = NotifySubscriberRequest, requestId = 500
2025-07-29 16:14:41.908 [nacos-grpc-client-executor-**************-44] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Ack server push request, request = NotifySubscriberRequest, requestId = 500
2025-07-29 16:15:04.354 [reactor-http-nio-2] INFO  [ecommate-gateway,,,] c.yaotown.gateway.filter.logging.AccessLogFilter - [writeAccessLog][网关日志：{
	"userId":null,
	"userType":null,
	"routeId":"ecommate-supplier-product-service",
	"schema":"http",
	"requestUrl":"/v1/supplier/product/supplier-product/trans-to-platform-product/FXG",
	"queryParams":{
		
	},
	"requestBody":{
		"supplierProductId":676920503282373
	},
	"requestHeaders":"{\"Authorization\":\"Bearer 8978da2c-d133-410c-8166-6ed3801383ff\",\"User-Agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"Content-Type\":\"application/json\",\"Accept\":\"*/*\",\"Host\":\"*************:38787\",\"Accept-Encoding\":\"gzip, deflate, br\",\"Connection\":\"keep-alive\",\"Content-Length\":\"46\"}",
	"userIp":"*************",
	"responseBody":{
		"code":0,
		"message":"操作成功",
		"data":{
			"productType":"0",
			"name":"测试商品002",
			"pic":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/98dc0e635c974bbca01423d0fb523eba.jpg|https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/f3c81a76f2d8419db5e2ef3ae99ebc2b.png",
			"description":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/eb1fd91a8ecf4035a0c9c340e58182e6.jpeg",
			"mainImageThreeToFour":"",
			"payType":"1",
			"reduceType":"1",
			"freightId":"",
			"weight":0.0,
			"weightUnit":"1",
			"deliveryDelayDay":"2",
			"supply7dayReturn":"1",
			"mobile":"",
			"commit":"false",
			"specPrices":[
				{
					"code":"",
					"outSkuId":"677237951924933",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"红色",
					"specDetailName2":"S"
				},
				{
					"code":"",
					"outSkuId":"677237951924934",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"红色",
					"specDetailName2":"M"
				},
				{
					"code":"",
					"outSkuId":"677237951924935",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"黑色",
					"specDetailName2":"S"
				},
				{
					"code":"",
					"outSkuId":"677237951924936",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"黑色",
					"specDetailName2":"M"
				}
			],
			"specs":"颜色|红色,红色,黑色,黑色^型号|S,M,S,M",
			"outerProductId":"676920503282373",
			"deliveryMethod":0,
			"specPic":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/17457d57608c4f31902b1f439d6beb4a.png,https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/0b8fc2379c354696b0af9f0b52501af7.png,https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/4661a0f5b2d544789704268e428441ea.jpg,https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/e657b58f887d410ab7af9dc5943618eb.jpg",
			"maximumPerOrder":100,
			"limitPerBuyer":0,
			"minimumPerOrder":1,
			"shortProductName":"",
			"specInfo":{
				"specs":[
					{
						"specId":"颜色",
						"specName":"颜色",
						"values":[
							{
								"valueId":"红色",
								"valueName":"红色"
							},
							{
								"valueId":"红色",
								"valueName":"红色"
							},
							{
								"valueId":"黑色",
								"valueName":"黑色"
							},
							{
								"valueId":"黑色",
								"valueName":"黑色"
							}
						]
					},
					{
						"specId":"型号",
						"specName":"型号",
						"values":[
							{
								"valueId":"S",
								"valueName":"S"
							},
							{
								"valueId":"M",
								"valueName":"M"
							},
							{
								"valueId":"S",
								"valueName":"S"
							},
							{
								"valueId":"M",
								"valueName":"M"
							}
						]
					}
				]
			},
			"specPricesV2":[
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924933"
				},
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924934"
				},
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924935"
				},
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924936"
				}
			]
		}
	},
	"responseHeaders":"{\"transfer-encoding\":\"chunked\",\"Vary\":\"Origin\",\"Expires\":\"0\",\"Cache-Control\":\"no-cache, no-store, max-age=0, must-revalidate\",\"X-XSS-Protection\":\"0\",\"Pragma\":\"no-cache\",\"Date\":\"Tue, 29 Jul 2025 08:15:04 GMT\",\"X-Content-Type-Options\":\"nosniff\",\"Content-Type\":\"application/json\"}",
	"httpStatus":"OK",
	"startTime":"2025-07-29 16:15:02.750",
	"endTime":"2025-07-29 16:15:04.233",
	"duration":"1483 ms"
}]
2025-07-29 16:19:42.772 [reactor-http-nio-2] INFO  [ecommate-gateway,,,] c.yaotown.gateway.filter.logging.AccessLogFilter - [writeAccessLog][网关日志：{
	"userId":null,
	"userType":null,
	"routeId":"ecommate-supplier-product-service",
	"schema":"http",
	"requestUrl":"/v1/supplier/product/supplier-product/trans-to-platform-product/FXG",
	"queryParams":{
		
	},
	"requestBody":{
		"supplierProductId":676920503282373
	},
	"requestHeaders":"{\"Authorization\":\"Bearer 8978da2c-d133-410c-8166-6ed3801383ff\",\"User-Agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"Content-Type\":\"application/json\",\"Accept\":\"*/*\",\"Host\":\"*************:38787\",\"Accept-Encoding\":\"gzip, deflate, br\",\"Connection\":\"keep-alive\",\"Content-Length\":\"46\"}",
	"userIp":"*************",
	"responseBody":{
		"code":0,
		"message":"操作成功",
		"data":{
			"productType":"0",
			"categoryLeafId":"20169",
			"name":"测试商品002",
			"pic":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/98dc0e635c974bbca01423d0fb523eba.jpg|https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/f3c81a76f2d8419db5e2ef3ae99ebc2b.png",
			"description":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/eb1fd91a8ecf4035a0c9c340e58182e6.jpeg",
			"mainImageThreeToFour":"",
			"payType":"1",
			"reduceType":"1",
			"freightId":"",
			"weight":0.0,
			"weightUnit":"1",
			"deliveryDelayDay":"2",
			"supply7dayReturn":"1",
			"mobile":"",
			"commit":"false",
			"specPrices":[
				{
					"code":"",
					"outSkuId":"677237951924933",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"红色",
					"specDetailName2":"S"
				},
				{
					"code":"",
					"outSkuId":"677237951924934",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"红色",
					"specDetailName2":"M"
				},
				{
					"code":"",
					"outSkuId":"677237951924935",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"黑色",
					"specDetailName2":"S"
				},
				{
					"code":"",
					"outSkuId":"677237951924936",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"黑色",
					"specDetailName2":"M"
				}
			],
			"specs":"颜色|红色,红色,黑色,黑色^型号|S,M,S,M",
			"outerProductId":"676920503282373",
			"deliveryMethod":0,
			"specPic":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/17457d57608c4f31902b1f439d6beb4a.png,https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/0b8fc2379c354696b0af9f0b52501af7.png,https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/4661a0f5b2d544789704268e428441ea.jpg,https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/e657b58f887d410ab7af9dc5943618eb.jpg",
			"maximumPerOrder":100,
			"limitPerBuyer":0,
			"minimumPerOrder":1,
			"shortProductName":"",
			"specInfo":{
				"specs":[
					{
						"specId":"颜色",
						"specName":"颜色",
						"values":[
							{
								"valueId":"红色",
								"valueName":"红色"
							},
							{
								"valueId":"红色",
								"valueName":"红色"
							},
							{
								"valueId":"黑色",
								"valueName":"黑色"
							},
							{
								"valueId":"黑色",
								"valueName":"黑色"
							}
						]
					},
					{
						"specId":"型号",
						"specName":"型号",
						"values":[
							{
								"valueId":"S",
								"valueName":"S"
							},
							{
								"valueId":"M",
								"valueName":"M"
							},
							{
								"valueId":"S",
								"valueName":"S"
							},
							{
								"valueId":"M",
								"valueName":"M"
							}
						]
					}
				]
			},
			"specPricesV2":[
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924933"
				},
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924934"
				},
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924935"
				},
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924936"
				}
			]
		}
	},
	"responseHeaders":"{\"transfer-encoding\":\"chunked\",\"Vary\":\"Origin\",\"Expires\":\"0\",\"Cache-Control\":\"no-cache, no-store, max-age=0, must-revalidate\",\"X-XSS-Protection\":\"0\",\"Pragma\":\"no-cache\",\"Date\":\"Tue, 29 Jul 2025 08:19:42 GMT\",\"X-Content-Type-Options\":\"nosniff\",\"Content-Type\":\"application/json\"}",
	"httpStatus":"OK",
	"startTime":"2025-07-29 16:19:39.005",
	"endTime":"2025-07-29 16:19:42.767",
	"duration":"3761 ms"
}]
2025-07-29 16:35:27.751 [nacos-grpc-client-executor-**************-350] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Receive server push request, request = NotifySubscriberRequest, requestId = 504
2025-07-29 16:35:27.752 [nacos-grpc-client-executor-**************-350] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@ecommate-product-server -> [{"instanceId":"*************#38786##DEFAULT_GROUP@@ecommate-product-server","ip":"*************","port":38786,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-product-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:35:27.752 [nacos-grpc-client-executor-**************-350] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@ecommate-product-server -> []
2025-07-29 16:35:27.753 [nacos-grpc-client-executor-**************-350] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Ack server push request, request = NotifySubscriberRequest, requestId = 504
2025-07-29 16:41:50.484 [nacos-grpc-client-executor-**************-436] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Receive server push request, request = NotifySubscriberRequest, requestId = 508
2025-07-29 16:41:50.485 [nacos-grpc-client-executor-**************-436] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@ecommate-product-server -> [{"instanceId":"*************#38786##DEFAULT_GROUP@@ecommate-product-server","ip":"*************","port":38786,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-product-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:41:50.486 [nacos-grpc-client-executor-**************-436] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@ecommate-product-server -> [{"instanceId":"*************#38786##DEFAULT_GROUP@@ecommate-product-server","ip":"*************","port":38786,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-product-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:41:50.488 [nacos-grpc-client-executor-**************-436] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Ack server push request, request = NotifySubscriberRequest, requestId = 508
2025-07-29 16:42:12.997 [reactor-http-nio-2] INFO  [ecommate-gateway,,,] c.yaotown.gateway.filter.logging.AccessLogFilter - [writeAccessLog][网关日志：{
	"userId":null,
	"userType":null,
	"routeId":"ecommate-supplier-product-service",
	"schema":"http",
	"requestUrl":"/v1/supplier/product/supplier-product/trans-to-platform-product/FXG",
	"queryParams":{
		
	},
	"requestBody":{
		"supplierProductId":676920503282373
	},
	"requestHeaders":"{\"Authorization\":\"Bearer 8978da2c-d133-410c-8166-6ed3801383ff\",\"User-Agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"Content-Type\":\"application/json\",\"Accept\":\"*/*\",\"Host\":\"*************:38787\",\"Accept-Encoding\":\"gzip, deflate, br\",\"Connection\":\"keep-alive\",\"Content-Length\":\"46\"}",
	"userIp":"*************",
	"responseBody":{
		"code":0,
		"message":"操作成功",
		"data":{
			"productType":"0",
			"categoryLeafId":"20169",
			"name":"测试商品002",
			"pic":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/98dc0e635c974bbca01423d0fb523eba.jpg|https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/f3c81a76f2d8419db5e2ef3ae99ebc2b.png",
			"description":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/eb1fd91a8ecf4035a0c9c340e58182e6.jpeg",
			"mainImageThreeToFour":"",
			"payType":"1",
			"reduceType":"1",
			"freightId":"",
			"weight":0.0,
			"weightUnit":"1",
			"deliveryDelayDay":"2",
			"supply7dayReturn":"1",
			"mobile":"",
			"commit":"false",
			"specPrices":[
				{
					"code":"",
					"outSkuId":"677237951924933",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"红色",
					"specDetailName2":"S"
				},
				{
					"code":"",
					"outSkuId":"677237951924934",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"红色",
					"specDetailName2":"M"
				},
				{
					"code":"",
					"outSkuId":"677237951924935",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"黑色",
					"specDetailName2":"S"
				},
				{
					"code":"",
					"outSkuId":"677237951924936",
					"price":"100",
					"stockNum":"1000",
					"specDetailName1":"黑色",
					"specDetailName2":"M"
				}
			],
			"specs":"颜色|红色,红色,黑色,黑色^型号|S,M,S,M",
			"outerProductId":"676920503282373",
			"deliveryMethod":0,
			"specPic":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/17457d57608c4f31902b1f439d6beb4a.png,https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/0b8fc2379c354696b0af9f0b52501af7.png,https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/4661a0f5b2d544789704268e428441ea.jpg,https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/e657b58f887d410ab7af9dc5943618eb.jpg",
			"maximumPerOrder":100,
			"limitPerBuyer":0,
			"minimumPerOrder":1,
			"shortProductName":"",
			"specInfo":{
				"specs":[
					{
						"specId":"颜色",
						"specName":"颜色",
						"values":[
							{
								"valueId":"红色",
								"valueName":"红色"
							},
							{
								"valueId":"红色",
								"valueName":"红色"
							},
							{
								"valueId":"黑色",
								"valueName":"黑色"
							},
							{
								"valueId":"黑色",
								"valueName":"黑色"
							}
						]
					},
					{
						"specId":"型号",
						"specName":"型号",
						"values":[
							{
								"valueId":"S",
								"valueName":"S"
							},
							{
								"valueId":"M",
								"valueName":"M"
							},
							{
								"valueId":"S",
								"valueName":"S"
							},
							{
								"valueId":"M",
								"valueName":"M"
							}
						]
					}
				]
			},
			"specPricesV2":[
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924933"
				},
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924934"
				},
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924935"
				},
				{
					"specDetailIds":[
						
					],
					"price":100,
					"settlementPrice":100,
					"stockNum":1000,
					"code":"",
					"outerSkuId":"677237951924936"
				}
			]
		}
	},
	"responseHeaders":"{\"transfer-encoding\":\"chunked\",\"Vary\":\"Origin\",\"Expires\":\"0\",\"Cache-Control\":\"no-cache, no-store, max-age=0, must-revalidate\",\"X-XSS-Protection\":\"0\",\"Pragma\":\"no-cache\",\"Date\":\"Tue, 29 Jul 2025 08:42:12 GMT\",\"X-Content-Type-Options\":\"nosniff\",\"Content-Type\":\"application/json\"}",
	"httpStatus":"OK",
	"startTime":"2025-07-29 16:42:12.096",
	"endTime":"2025-07-29 16:42:12.984",
	"duration":"888 ms"
}]
2025-07-29 16:42:17.388 [reactor-http-nio-2] INFO  [ecommate-gateway,,,] c.yaotown.gateway.filter.logging.AccessLogFilter - [writeAccessLog][网关日志：{
	"userId":null,
	"userType":null,
	"routeId":"ecommate-supplier-product-service",
	"schema":"http",
	"requestUrl":"/v1/supplier/product/supplier-product/trans-to-platform-product/XHS",
	"queryParams":{
		
	},
	"requestBody":{
		"supplierProductId":676920503282373
	},
	"requestHeaders":"{\"Authorization\":\"Bearer 8978da2c-d133-410c-8166-6ed3801383ff\",\"User-Agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"Content-Type\":\"application/json\",\"Accept\":\"*/*\",\"Host\":\"*************:38787\",\"Accept-Encoding\":\"gzip, deflate, br\",\"Connection\":\"keep-alive\",\"Content-Length\":\"46\"}",
	"userIp":"*************",
	"responseBody":{
		"code":0,
		"message":"操作成功",
		"data":{
			"name":"测试商品002",
			"categoryId":"65f995d23e946300016b0b12",
			"shippingTemplateId":"",
			"shippingGrossWeight":0,
			"variantIds":[
				"颜色",
				"型号"
			],
			"images":[
				"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/98dc0e635c974bbca01423d0fb523eba.jpg",
				"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/f3c81a76f2d8419db5e2ef3ae99ebc2b.png"
			],
			"articleNo":"123654",
			"imageDescriptions":[
				"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/eb1fd91a8ecf4035a0c9c340e58182e6.jpeg"
			],
			"isChannel":false,
			"deliveryMode":1,
			"freeReturn":1,
			"skuList":[
				{
					"originalPrice":10000,
					"price":10000,
					"stock":1000,
					"erpCode":"",
					"variants":[
						{
							"id":"颜色",
							"name":"颜色",
							"value":"红色",
							"valueId":"红色"
						},
						{
							"id":"型号",
							"name":"型号",
							"value":"S",
							"valueId":"S"
						}
					],
					"deliveryTime":{
						"time":"3",
						"type":"days"
					},
					"specImage":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/17457d57608c4f31902b1f439d6beb4a.png",
					"barcode":"",
					"buyable":true,
					"isGift":false,
					"deliveryFlag":1
				},
				{
					"originalPrice":10000,
					"price":10000,
					"stock":1000,
					"erpCode":"",
					"variants":[
						{
							"id":"颜色",
							"name":"颜色",
							"value":"红色",
							"valueId":"红色"
						},
						{
							"id":"型号",
							"name":"型号",
							"value":"M",
							"valueId":"M"
						}
					],
					"deliveryTime":{
						"time":"3",
						"type":"days"
					},
					"specImage":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/0b8fc2379c354696b0af9f0b52501af7.png",
					"barcode":"",
					"buyable":true,
					"isGift":false,
					"deliveryFlag":1
				},
				{
					"originalPrice":10000,
					"price":10000,
					"stock":1000,
					"erpCode":"",
					"variants":[
						{
							"id":"颜色",
							"name":"颜色",
							"value":"黑色",
							"valueId":"黑色"
						},
						{
							"id":"型号",
							"name":"型号",
							"value":"S",
							"valueId":"S"
						}
					],
					"deliveryTime":{
						"time":"3",
						"type":"days"
					},
					"specImage":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/4661a0f5b2d544789704268e428441ea.jpg",
					"barcode":"",
					"buyable":true,
					"isGift":false,
					"deliveryFlag":1
				},
				{
					"originalPrice":10000,
					"price":10000,
					"stock":1000,
					"erpCode":"",
					"variants":[
						{
							"id":"颜色",
							"name":"颜色",
							"value":"黑色",
							"valueId":"黑色"
						},
						{
							"id":"型号",
							"name":"型号",
							"value":"M",
							"valueId":"M"
						}
					],
					"deliveryTime":{
						"time":"3",
						"type":"days"
					},
					"specImage":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/e657b58f887d410ab7af9dc5943618eb.jpg",
					"barcode":"",
					"buyable":true,
					"isGift":false,
					"deliveryFlag":1
				}
			]
		}
	},
	"responseHeaders":"{\"transfer-encoding\":\"chunked\",\"Vary\":\"Origin\",\"Expires\":\"0\",\"Cache-Control\":\"no-cache, no-store, max-age=0, must-revalidate\",\"X-XSS-Protection\":\"0\",\"Pragma\":\"no-cache\",\"Date\":\"Tue, 29 Jul 2025 08:42:17 GMT\",\"X-Content-Type-Options\":\"nosniff\",\"Content-Type\":\"application/json\"}",
	"httpStatus":"OK",
	"startTime":"2025-07-29 16:42:16.990",
	"endTime":"2025-07-29 16:42:17.382",
	"duration":"392 ms"
}]
2025-07-29 16:42:22.508 [reactor-http-nio-2] INFO  [ecommate-gateway,,,] c.yaotown.gateway.filter.logging.AccessLogFilter - [writeAccessLog][网关日志：{
	"userId":null,
	"userType":null,
	"routeId":"ecommate-supplier-product-service",
	"schema":"http",
	"requestUrl":"/v1/supplier/product/supplier-product/trans-to-platform-product/KWAISHOP",
	"queryParams":{
		
	},
	"requestBody":"{\r\n    \"supplierProductId\": 676920503282373",
	"requestHeaders":"{\"Authorization\":\"Bearer 8978da2c-d133-410c-8166-6ed3801383ff\",\"User-Agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"Content-Type\":\"application/json\",\"Accept\":\"*/*\",\"Host\":\"*************:38787\",\"Accept-Encoding\":\"gzip, deflate, br\",\"Connection\":\"keep-alive\",\"Content-Length\":\"43\"}",
	"userIp":"*************",
	"responseBody":{
		"code":4000,
		"message":"请求参数不正确"
	},
	"responseHeaders":"{\"transfer-encoding\":\"chunked\",\"Vary\":\"Origin\",\"Expires\":\"0\",\"Cache-Control\":\"no-cache, no-store, max-age=0, must-revalidate\",\"X-XSS-Protection\":\"0\",\"Pragma\":\"no-cache\",\"Date\":\"Tue, 29 Jul 2025 08:42:22 GMT\",\"X-Content-Type-Options\":\"nosniff\",\"Content-Type\":\"application/json\"}",
	"httpStatus":"OK",
	"startTime":"2025-07-29 16:42:22.440",
	"endTime":"2025-07-29 16:42:22.502",
	"duration":"62 ms"
}]
2025-07-29 16:42:31.395 [reactor-http-nio-2] INFO  [ecommate-gateway,,,] c.yaotown.gateway.filter.logging.AccessLogFilter - [writeAccessLog][网关日志：{
	"userId":null,
	"userType":null,
	"routeId":"ecommate-supplier-product-service",
	"schema":"http",
	"requestUrl":"/v1/supplier/product/supplier-product/trans-to-platform-product/KWAISHOP",
	"queryParams":{
		
	},
	"requestBody":{
		"supplierProductId":676920503282373
	},
	"requestHeaders":"{\"Authorization\":\"Bearer 8978da2c-d133-410c-8166-6ed3801383ff\",\"User-Agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"Content-Type\":\"application/json\",\"Accept\":\"*/*\",\"Host\":\"*************:38787\",\"Accept-Encoding\":\"gzip, deflate, br\",\"Connection\":\"keep-alive\",\"Content-Length\":\"46\"}",
	"userIp":"*************",
	"responseBody":{
		"code":0,
		"message":"操作成功",
		"data":{
			"title":"测试商品002",
			"relItemId":676920503282373,
			"categoryId":1814,
			"imageUrls":[
				"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/98dc0e635c974bbca01423d0fb523eba.jpg",
				"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/f3c81a76f2d8419db5e2ef3ae99ebc2b.png"
			],
			"skuList":[
				{
					"relSkuId":677237951924933,
					"skuStock":1000,
					"skuSalePrice":100,
					"skuNick":"",
					"skuProps":[
						{
							"propName":"颜色",
							"propValueName":"红色",
							"imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/17457d57608c4f31902b1f439d6beb4a.png",
							"isMainProp":1
						},
						{
							"propName":"型号",
							"propValueName":"S",
							"imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/17457d57608c4f31902b1f439d6beb4a.png",
							"isMainProp":1
						}
					]
				},
				{
					"relSkuId":677237951924934,
					"skuStock":1000,
					"skuSalePrice":100,
					"skuNick":"",
					"skuProps":[
						{
							"propName":"颜色",
							"propValueName":"红色",
							"imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/0b8fc2379c354696b0af9f0b52501af7.png",
							"isMainProp":1
						},
						{
							"propName":"型号",
							"propValueName":"M",
							"imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/0b8fc2379c354696b0af9f0b52501af7.png",
							"isMainProp":1
						}
					]
				},
				{
					"relSkuId":677237951924935,
					"skuStock":1000,
					"skuSalePrice":100,
					"skuNick":"",
					"skuProps":[
						{
							"propName":"颜色",
							"propValueName":"黑色",
							"imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/4661a0f5b2d544789704268e428441ea.jpg",
							"isMainProp":1
						},
						{
							"propName":"型号",
							"propValueName":"S",
							"imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/4661a0f5b2d544789704268e428441ea.jpg",
							"isMainProp":1
						}
					]
				},
				{
					"relSkuId":677237951924936,
					"skuStock":1000,
					"skuSalePrice":100,
					"skuNick":"",
					"skuProps":[
						{
							"propName":"颜色",
							"propValueName":"黑色",
							"imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/e657b58f887d410ab7af9dc5943618eb.jpg",
							"isMainProp":1
						},
						{
							"propName":"型号",
							"propValueName":"M",
							"imageUrl":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/e657b58f887d410ab7af9dc5943618eb.jpg",
							"isMainProp":1
						}
					]
				}
			],
			"purchaseLimit":false,
			"details":"<img src=\"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/eb1fd91a8ecf4035a0c9c340e58182e6.jpeg\" />",
			"detailImageUrls":[
				"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/eb1fd91a8ecf4035a0c9c340e58182e6.jpeg"
			],
			"stockPartner":false,
			"serviceRule":{
				"refundRule":"1",
				"promiseDeliveryTime":172800000,
				"deliveryMethod":"1",
				"servicePromise":{
					"brokenRefund":true
				}
			},
			"saleTimeFlag":false,
			"payWay":1,
			"multipleStock":false,
			"sellingPoint":"测试商品002"
		}
	},
	"responseHeaders":"{\"transfer-encoding\":\"chunked\",\"Vary\":\"Origin\",\"Expires\":\"0\",\"Cache-Control\":\"no-cache, no-store, max-age=0, must-revalidate\",\"X-XSS-Protection\":\"0\",\"Pragma\":\"no-cache\",\"Date\":\"Tue, 29 Jul 2025 08:42:31 GMT\",\"X-Content-Type-Options\":\"nosniff\",\"Content-Type\":\"application/json\"}",
	"httpStatus":"OK",
	"startTime":"2025-07-29 16:42:30.994",
	"endTime":"2025-07-29 16:42:31.391",
	"duration":"397 ms"
}]
2025-07-29 16:42:36.852 [reactor-http-nio-2] INFO  [ecommate-gateway,,,] c.yaotown.gateway.filter.logging.AccessLogFilter - [writeAccessLog][网关日志：{
	"userId":null,
	"userType":null,
	"routeId":"ecommate-supplier-product-service",
	"schema":"http",
	"requestUrl":"/v1/supplier/product/supplier-product/trans-to-platform-product/SPHXD",
	"queryParams":{
		
	},
	"requestBody":{
		"supplierProductId":676920503282373
	},
	"requestHeaders":"{\"Authorization\":\"Bearer 8978da2c-d133-410c-8166-6ed3801383ff\",\"User-Agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"Content-Type\":\"application/json\",\"Accept\":\"*/*\",\"Host\":\"*************:38787\",\"Accept-Encoding\":\"gzip, deflate, br\",\"Connection\":\"keep-alive\",\"Content-Length\":\"46\"}",
	"userIp":"*************",
	"responseBody":{
		"code":0,
		"message":"操作成功",
		"data":{
			"outProductId":"676920503282373",
			"title":"测试商品002",
			"headImgs":[
				"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/98dc0e635c974bbca01423d0fb523eba.jpg",
				"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/f3c81a76f2d8419db5e2ef3ae99ebc2b.png"
			],
			"deliverMethod":0,
			"descInfo":{
				"imgs":[
					"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/eb1fd91a8ecf4035a0c9c340e58182e6.jpeg"
				]
			},
			"cats":[
				{
					"catId":"6153"
				},
				{
					"catId":"6214"
				},
				{
					"catId":"526035"
				}
			],
			"catsV2":[
				{
					"catId":"6153"
				},
				{
					"catId":"6214"
				},
				{
					"catId":"526035"
				}
			],
			"spuCode":"123654",
			"expressInfo":{
				"templateId":"",
				"weight":500.0
			},
			"extraService":{
				"sevenDayReturn":1,
				"freightInsurance":0,
				"fakeOnePayThree":0,
				"damageGuarantee":1
			},
			"skus":[
				{
					"outSkuId":"",
					"thumbImg":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/17457d57608c4f31902b1f439d6beb4a.png",
					"salePrice":100,
					"stockNum":1000,
					"skuCode":"",
					"skuAttrs":[
						{
							"attrKey":"颜色",
							"attrValue":"红色"
						},
						{
							"attrKey":"型号",
							"attrValue":"S"
						}
					],
					"skuDeliverInfo":{
						"stockType":0
					}
				},
				{
					"outSkuId":"",
					"thumbImg":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/0b8fc2379c354696b0af9f0b52501af7.png",
					"salePrice":100,
					"stockNum":1000,
					"skuCode":"",
					"skuAttrs":[
						{
							"attrKey":"颜色",
							"attrValue":"红色"
						},
						{
							"attrKey":"型号",
							"attrValue":"M"
						}
					],
					"skuDeliverInfo":{
						"stockType":0
					}
				},
				{
					"outSkuId":"",
					"thumbImg":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/4661a0f5b2d544789704268e428441ea.jpg",
					"salePrice":100,
					"stockNum":1000,
					"skuCode":"",
					"skuAttrs":[
						{
							"attrKey":"颜色",
							"attrValue":"黑色"
						},
						{
							"attrKey":"型号",
							"attrValue":"S"
						}
					],
					"skuDeliverInfo":{
						"stockType":0
					}
				},
				{
					"outSkuId":"",
					"thumbImg":"https://yaotown-ecommate.oss-cn-shenzhen.aliyuncs.com/ecommate/public/673736646376773/20250728/e657b58f887d410ab7af9dc5943618eb.jpg",
					"salePrice":100,
					"stockNum":1000,
					"skuCode":"",
					"skuAttrs":[
						{
							"attrKey":"颜色",
							"attrValue":"黑色"
						},
						{
							"attrKey":"型号",
							"attrValue":"M"
						}
					],
					"skuDeliverInfo":{
						"stockType":0
					}
				}
			],
			"listing":0,
			"sizeChart":{
				"enable":false
			}
		}
	},
	"responseHeaders":"{\"transfer-encoding\":\"chunked\",\"Vary\":\"Origin\",\"Expires\":\"0\",\"Cache-Control\":\"no-cache, no-store, max-age=0, must-revalidate\",\"X-XSS-Protection\":\"0\",\"Pragma\":\"no-cache\",\"Date\":\"Tue, 29 Jul 2025 08:42:36 GMT\",\"X-Content-Type-Options\":\"nosniff\",\"Content-Type\":\"application/json\"}",
	"httpStatus":"OK",
	"startTime":"2025-07-29 16:42:36.272",
	"endTime":"2025-07-29 16:42:36.847",
	"duration":"575 ms"
}]
2025-07-29 16:43:30.496 [nacos-grpc-client-executor-**************-461] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Receive server push request, request = NotifySubscriberRequest, requestId = 513
2025-07-29 16:43:30.496 [nacos-grpc-client-executor-**************-461] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@ecommate-product-server -> [{"instanceId":"*************#38786##DEFAULT_GROUP@@ecommate-product-server","ip":"*************","port":38786,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-product-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:43:30.496 [nacos-grpc-client-executor-**************-461] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@ecommate-product-server -> []
2025-07-29 16:43:30.497 [nacos-grpc-client-executor-**************-461] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Ack server push request, request = NotifySubscriberRequest, requestId = 513
2025-07-29 16:43:33.308 [nacos-grpc-client-executor-**************-462] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Receive server push request, request = NotifySubscriberRequest, requestId = 514
2025-07-29 16:43:33.308 [nacos-grpc-client-executor-**************-462] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@ecommate-infra-server -> [{"instanceId":"*************#38785##DEFAULT_GROUP@@ecommate-infra-server","ip":"*************","port":38785,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-infra-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"ipDeleteTimeout":3000,"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000}]
2025-07-29 16:43:33.308 [nacos-grpc-client-executor-**************-462] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@ecommate-infra-server -> []
2025-07-29 16:43:33.345 [nacos-grpc-client-executor-**************-462] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - [6bd30133-6c75-44da-b6bf-36ff90260b3e] Ack server push request, request = NotifySubscriberRequest, requestId = 514
2025-07-29 16:43:35.168 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 16:43:35.168 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] ecommate-cfx deregistering service ecommate-gateway with instance: Instance{instanceId='null', ip='*************', port=38787, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
2025-07-29 16:43:35.173 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-07-29 16:43:35.174 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-29 16:43:35.174 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-29 16:43:35.174 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-29 16:43:35.174 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-29 16:43:35.174 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-29 16:43:35.174 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-29 16:43:35.174 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->6bd30133-6c75-44da-b6bf-36ff90260b3e
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@478355c1[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 596]
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@31b3b666[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-29 16:43:35.175 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.common.remote.client - Close current connection 1753776814321_192.168.48.1_53936
2025-07-29 16:43:35.176 [nacos-grpc-client-executor-**************-464] INFO  [ecommate-gateway,,,] c.a.nacos.common.remote.client.grpc.GrpcClient - [1753776814321_192.168.48.1_53936]Ignore complete event,isRunning:false,isAbandon=false
2025-07-29 16:43:35.178 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] c.a.nacos.common.remote.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@358e6cf6[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 465]
2025-07-29 16:43:35.178 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->6bd30133-6c75-44da-b6bf-36ff90260b3e
2025-07-29 16:43:35.178 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] c.a.n.client.auth.ram.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-29 16:43:35.178 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] c.a.n.client.auth.ram.identify.CredentialService - [null] CredentialService is freed
2025-07-29 16:43:35.178 [SpringApplicationShutdownHook] INFO  [ecommate-gateway,,,] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
