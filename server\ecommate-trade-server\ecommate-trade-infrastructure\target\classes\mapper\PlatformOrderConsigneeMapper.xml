<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaotown.ecommate.trade.infrastructure.mapper.PlatformOrderConsigneeMapper">
  <resultMap id="BaseResultMap" type="com.yaotown.ecommate.trade.infrastructure.po.PlatformOrderConsigneePO">
    <!--@mbg.generated-->
    <!--@Table yt_platform_order_consignee-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_order_id" jdbcType="BIGINT" property="platformOrderId" />
    <result column="ext_order_id" jdbcType="VARCHAR" property="extOrderId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="supplier_enterprise_id" jdbcType="BIGINT" property="supplierEnterpriseId" />
    <result column="current_enterprise_id" jdbcType="BIGINT" property="currentEnterpriseId" />
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    <result column="receiver_country" jdbcType="VARCHAR" property="receiverCountry" />
    <result column="receiver_state" jdbcType="VARCHAR" property="receiverState" />
    <result column="receiver_city" jdbcType="VARCHAR" property="receiverCity" />
    <result column="receiver_district" jdbcType="VARCHAR" property="receiverDistrict" />
    <result column="receiver_town" jdbcType="VARCHAR" property="receiverTown" />
    <result column="receiver_address" jdbcType="VARCHAR" property="receiverAddress" />
    <result column="receiver_zip" jdbcType="VARCHAR" property="receiverZip" />
    <result column="receiver_mobile" jdbcType="VARCHAR" property="receiverMobile" />
    <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone" />
    <result column="done_time" jdbcType="TIMESTAMP" property="doneTime" />
    <result column="done_version" jdbcType="BIGINT" property="doneVersion" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, platform_order_id, ext_order_id, platform, enterprise_id, supplier_enterprise_id, 
    current_enterprise_id, receiver_name, receiver_country, receiver_state, receiver_city, 
    receiver_district, receiver_town, receiver_address, receiver_zip, receiver_mobile, 
    receiver_phone, done_time, done_version, delete_flag, created, updated
  </sql>
</mapper>