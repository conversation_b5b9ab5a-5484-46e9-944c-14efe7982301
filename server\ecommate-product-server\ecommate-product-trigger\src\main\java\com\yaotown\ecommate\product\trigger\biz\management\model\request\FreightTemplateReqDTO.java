package com.yaotown.ecommate.product.trigger.biz.management.model.request;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 运费模板请求DTO
 */
@Data
public class FreightTemplateReqDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /**
     * 地区名称
     */
    @NotBlank(message = "地区名称不能为空")
    private String regionName;

    /**
     * 地区编码，多个用逗号分隔
     */
    private String regionCodes;

    /**
     * 首件(个)
     */
    @NotNull(message = "首件数量不能为空")
    private Integer firstItem;

    /**
     * 首件运费(分)
     */
    @NotNull(message = "首件运费不能为空")
    private Long firstItemFee;

    /**
     * 续件(个)
     */
    private Integer additionalItem;

    /**
     * 续件运费(分)
     */
    private Long additionalItemFee;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
} 