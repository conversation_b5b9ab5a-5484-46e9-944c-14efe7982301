package com.yaotown.ecommate.product.trigger.http.supplier;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.yaotown.common.base.core.entity.PageData;
import com.yaotown.common.base.core.entity.QueryModel;
import com.yaotown.common.base.core.entity.R;
import com.yaotown.ecommate.common.core.entity.CurrentAccount;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.security.core.util.SecurityUtils;
import com.yaotown.ecommate.common.web.apilog.core.annotation.ApiAccessLog;
import com.yaotown.ecommate.product.api.model.response.ShopInfoRespDTO;
import com.yaotown.ecommate.product.domain.enterprise.adapter.port.IEnterpriseErpLinkPort;
import com.yaotown.ecommate.product.domain.enterprise.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.product.domain.enterprise.model.valobj.ShopInfoSearchVO;
import com.yaotown.ecommate.product.domain.enterprise.service.IRefreshShopAccessTokenService;
import com.yaotown.ecommate.product.domain.enterprise.service.IShopInfoService;
import com.yaotown.ecommate.product.trigger.biz.convert.ShopInfoConvert;
import com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request.SupplierShopAuthorizationReqDTO;
import com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request.SupplierShopCreateReqDTO;
import com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request.SupplierShopPageReqDTO;
import com.yaotown.ecommate.product.trigger.biz.supplycenter.model.request.SupplierShopUpdateReqDTO;
import com.yaotown.ecommate.product.types.enums.erp.ErpPlatformTypeEnum;
import com.yaotown.ecommate.product.types.enums.shop.AuthStatusEnum;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 供应商店铺管理
 *
 * @apiFolder 供应商端/产品中心服务/店铺管理
 * @classPrefixPath /v1/supplier/
 */
@RestController
@AllArgsConstructor
@RequestMapping("/product/shop")
public class SupplierShopController {

    private final IShopInfoService shopInfoService;
    private final IEnterpriseErpLinkPort enterpriseShopInfoPort;
    private final IRefreshShopAccessTokenService refreshShopAccessTokenService;

    /**
     * 同步小亚通的店铺数据
     *
     * @return 同步结果
     */
    @PostMapping("/sync")
    @ApiAccessLog(responseEnable = true)
    public R<Long> syncErpShopInfo() {
        CurrentAccount loginAccount = SecurityUtils.getLoginAccount();
        enterpriseShopInfoPort.syncErpShopInfoData(loginAccount.getEnterpriseId(), loginAccount.getAccountId());
        return R.success();
    }

    /**
     * 获取店铺授权
     *
     * @param reqDTO platformType 平台类型
     * @param reqDTO redirectSuccessUrl 授权成功后的跳转URL
     * @param reqDTO redirectFailureUrl 授权失败后的跳转URL
     * @return 授权URL
     */
    @PostMapping("/auth")
    public R<String> getShopAuthUrl(@RequestBody SupplierShopAuthorizationReqDTO reqDTO) {
        String authUrl = shopInfoService.shopAuthorization(reqDTO.getPlatform(), reqDTO.getRedirectSuccessUrl(), reqDTO.getRedirectFailureUrl());
        return R.success(authUrl);
    }

    /**
     * 获取店铺列表
     *
     * @param queryModel 查询模型，包含分页信息和查询条件
     * @return 店铺列表数据
     */
    @PostMapping("/list")
    public R<PageData<ShopInfoRespDTO>> getShopList(@RequestBody QueryModel<SupplierShopPageReqDTO> queryModel) {
        // 从queryModel中获取分页参数和查询条件
        SupplierShopPageReqDTO condition = queryModel.getParam();

        // 转换查询条件为ShopInfoSearchVO
        ShopInfoSearchVO searchVO = new ShopInfoSearchVO();
        BeanUtil.copyProperties(condition, searchVO);

        // 设置当前登录用户的企业ID
        CurrentAccount loginAccount = SecurityUtils.getLoginAccount();
        searchVO.setEnterpriseId(loginAccount.getEnterpriseId());

        // 构建查询模型
        QueryModel<ShopInfoSearchVO> shopQueryModel = new QueryModel<>();
        shopQueryModel.setPageNum(queryModel.getPageNum());
        shopQueryModel.setPageSize(queryModel.getPageSize());
        shopQueryModel.setParam(searchVO);

        // 直接查询数据库
        PageData<ShopInfoEntity> pageData = shopInfoService.selectShopInfoList(shopQueryModel);
        return R.success(ShopInfoConvert.INSTANCE.toShopInfoRespDTOPageData(pageData));
    }

    /**
     * 新增店铺
     *
     * @param createReqDTO 店铺创建信息
     * @return 新增的店铺ID
     */
    @PostMapping("/create")
    public R<Long> createShop(@Valid @RequestBody SupplierShopCreateReqDTO createReqDTO) {
        // 将DTO转换为实体
        ShopInfoEntity shopInfoEntity = new ShopInfoEntity();
        BeanUtil.copyProperties(createReqDTO, shopInfoEntity);

        // 如果有shopId且为字符串类型，尝试转换为Long
        if (createReqDTO.getShopId() != null) {
            shopInfoEntity.setShopId(Long.parseLong(createReqDTO.getShopId()));
        }
        // 设置XYT的店铺ID为0
        shopInfoEntity.setXytShopId("0");
        shopInfoEntity.setAuthStatus(AuthStatusEnum.UNAUTHORIZED.getCode());
        // 调用服务保存店铺信息
        Long shopId = shopInfoService.createShop(shopInfoEntity);
        // 店铺到期时间默认设置10年
        shopInfoEntity.setExpiredDate(DateUtil.offset(DateUtil.date(), DateField.YEAR, 10));
        // 创建完成后，刷新授权
        ShopInfoEntity shopInfo = shopInfoService.findShopInfo(shopId);
        //目前只刷新视频号店铺的授权
        if (shopInfo != null && shopInfo.getPlatformType().equals(ErpPlatformTypeEnum.SPHXD.getValue())) {
            refreshShopAccessTokenService.refreshToken(shopInfo, true);
        }

        return R.success(shopId);
    }

    /**
     * 刷新店铺授权
     *
     * @param shopId 店铺ID
     * @return 刷新结果
     */
    @GetMapping("/refresh-auth")
    public R<Boolean> refreshShopAuth(@RequestParam Long shopId) {
        if (shopId == null) {
            throw new BusinessException("店铺ID不能为空");
        }

        // 获取店铺信息
        ShopInfoEntity shopInfo = shopInfoService.findShopInfo(shopId);
        if (shopInfo == null) {
            throw new BusinessException("店铺不存在");
        }

        // 调用刷新token的方法
        refreshShopAccessTokenService.refreshToken(shopInfo, true);

        return R.success(true);
    }

    /**
     * 获取店铺详情
     *
     * @param shopId 店铺ID
     * @return 店铺详情
     */
    @GetMapping("/details")
    public R<ShopInfoRespDTO> getShopDetails(@RequestParam Long shopId) {
        if (shopId == null) {
            throw new BusinessException("店铺ID不能为空");
        }

        // 获取当前登录用户的企业ID
        CurrentAccount loginAccount = SecurityUtils.getLoginAccount();
        Long enterpriseId = loginAccount.getEnterpriseId();

        // 获取店铺信息
        ShopInfoEntity shopInfo = shopInfoService.findShopInfo(shopId);
        if (shopInfo == null) {
            throw new BusinessException("店铺不存在");
        }
        // 将ShopInfoEntity转换为ShopInfoRespDTO
        ShopInfoRespDTO respDTO = new ShopInfoRespDTO();
        BeanUtil.copyProperties(shopInfo, respDTO);

        return R.success(respDTO);
    }

    /**
     * 更新店铺信息
     *
     * @param updateReqDTO 店铺更新信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Boolean> updateShop(@Valid @RequestBody SupplierShopUpdateReqDTO updateReqDTO) {
        if (updateReqDTO.getShopId() == null) {
            throw new BusinessException("店铺ID不能为空");
        }

        // 获取当前登录用户的企业ID
        CurrentAccount loginAccount = SecurityUtils.getLoginAccount();
        Long enterpriseId = loginAccount.getEnterpriseId();

        // 获取店铺信息，验证是否存在
        ShopInfoEntity existingShop = shopInfoService.findShopInfo(updateReqDTO.getShopId());
        if (existingShop == null) {
            throw new BusinessException("店铺不存在");
        }
        // 将DTO转换为实体
        ShopInfoEntity shopInfoEntity = new ShopInfoEntity();
        BeanUtil.copyProperties(updateReqDTO, shopInfoEntity);
        
        // 设置店铺ID
        shopInfoEntity.setShopId(updateReqDTO.getShopId());
        
        // 保留原有的授权状态和XYT店铺ID
        shopInfoEntity.setAuthStatus(existingShop.getAuthStatus());
        shopInfoEntity.setXytShopId(existingShop.getXytShopId());
        shopInfoEntity.setEnterpriseId(enterpriseId);
        shopInfoEntity.setAccountId(loginAccount.getAccountId());

        // 调用服务更新店铺信息
        boolean result = shopInfoService.updateShopInfo(shopInfoEntity);

        return R.success(result);
    }
}
