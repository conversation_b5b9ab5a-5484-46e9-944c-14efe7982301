<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="84e89aad-338c-4271-ab1d-5298a3cb7995" name="更改" comment="[U] 补充店铺统一授权管理sql">
      <change beforePath="$PROJECT_DIR$/framework/ecommate-gateway/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/framework/ecommate-gateway/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/yaotown-ecommate-admin-web/.env.local" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/yaotown-ecommate-admin-web/.env.local" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/yaotown-supplier-admin-web/pnpm-lock.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/yaotown-supplier-admin-web/pnpm-lock.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/ecommate-infra-server/ecommate-infra-web/src/main/resources/application-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/server/ecommate-infra-server/ecommate-infra-web/src/main/resources/application-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/ecommate-infra-server/ecommate-infra-web/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/server/ecommate-infra-server/ecommate-infra-web/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-app/src/main/resources/application-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-app/src/main/resources/application-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-app/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-app/src/main/resources/bootstrap-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-infrastructure/src/main/java/com/yaotown/ecommate/product/infrastructure/repository/supply/category/SupplyCategoryInfoRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-infrastructure/src/main/java/com/yaotown/ecommate/product/infrastructure/repository/supply/category/SupplyCategoryInfoRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-trigger/src/main/java/com/yaotown/ecommate/product/trigger/http/platform/category/CategoryManageController.java" beforeDir="false" afterPath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-trigger/src/main/java/com/yaotown/ecommate/product/trigger/http/platform/category/CategoryManageController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-types/src/main/java/com/yaotown/ecommate/product/types/util/ProductRedisKeyUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/server/ecommate-product-server/ecommate-product-types/src/main/java/com/yaotown/ecommate/product/types/util/ProductRedisKeyUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-app/src/main/resources/application-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-app/src/main/resources/application-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-app/src/main/resources/bootstrap-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-app/src/main/resources/bootstrap-local.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="module-info" />
        <option value="Interface" />
        <option value="Class" />
        <option value="package-info" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <branch-grouping />
    <option name="FILTER_BY_ACTION_IN_POPUP" value="false" />
    <option name="FILTER_BY_REPOSITORY_IN_POPUP" value="false" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/common-lib/doc" value="release/v1.1.0" />
        <entry key="$PROJECT_DIR$/common-lib/ecommate-common" value="dev" />
        <entry key="$PROJECT_DIR$/framework/ecommate-gateway" value="dev" />
        <entry key="$PROJECT_DIR$/frontend/yaotown-ecommate-admin-web" value="master-backups" />
        <entry key="$PROJECT_DIR$/frontend/yaotown-supplier-admin-web" value="dev" />
        <entry key="$PROJECT_DIR$/server/ecommate-infra-server" value="dev" />
        <entry key="$PROJECT_DIR$/server/ecommate-product-server" value="dev" />
        <entry key="$PROJECT_DIR$/server/ecommate-trade-server" value="feature_capital_account_20250708" />
      </map>
    </option>
    <option name="RECENT_COMMON_BRANCH" value="release/v1.1.0" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/frontend/yaotown-ecommate-admin-web" />
    <option name="ROOT_SYNC" value="SYNC" />
    <option name="SHOW_RECENT_BRANCHES" value="false" />
    <option name="SHOW_TAGS" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/framework/ecommate-gateway/src/main/resources/bootstrap.yml" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/server/ecommate-infra-server/pom.xml" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/server/ecommate-trade-server/ecommate-trade-domain/src/main/java/com/yaotown/ecommate/trade/domain/order/ecomlink/service/connector/impl/FxgChannelConnector.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="alwaysUpdateSnapshots" value="true" />
        <option name="customMavenHome" value="D:\app\workapp\maven\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\app\workapp\maven\maven_repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\app\workapp\maven\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/frontend/yaotown-ecommate-admin-web/package.json" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2vo2pZy5MG1OrkeiEQEPxzQy5Yf" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JUnit.AttributeConversionTest.aggregateAllPlatformAttributesCombined.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.aggregateByLeafCategoryId.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.AttributeConversionTest.executeAggregationFromMongoDB.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.AttributeConversionTest.test1.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testAttributeDefinitionAggregation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testConvertPlatformOptionMappingToAttributeOptionMapping.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testDouyinConversionProcessV2.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testExtractAttributeDefinitionData.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testFreshProductRuleConversion.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testFreshProductRuleConversionV2.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testFullConversionProcess.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testImageAttributeConversion.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testPost2.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.AttributeConversionTest.testSphxdAttributeConversion.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testSphxdAttributeConversionV2.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeConversionTest.testUpdateSphxdAttributePinyinId.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.AttributeConversionTest.testXhsAttributeConversionV2.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AttributeDefinitionAggregationTest.testFindAttributeDefinitionsByIds.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.AttributeDefinitionAggregationTest.testGetAggregationRules.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ErpApiClientServiceImplTest.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.ErpApiClientServiceImplTest.testPost (1).executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.ErpApiClientServiceImplTest.testPost.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.ErpApiClientServiceTest.recursionUpdateVisibleCategory.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.ErpApiClientServiceTest.test.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ErpApiClientServiceTest.test03.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ErpApiClientServiceTest.test2 (1).executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ErpApiClientServiceTest.test2.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.ErpApiClientServiceTest.testPost (1).executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ErpApiClientServiceTest.testPost2.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.ErpApiClientServiceTest.testPost3.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.KuaiShopAttributeMappingTest.testPost.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.KuaiShopAttributeMappingTest.testPost2.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ProductFactoryServiceTest.testPost.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.SemanticMatchClientTest.test03.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.SemanticMatchClientTest.test04.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.SphxdAttributeMappingTest.testSphxdAttributeMapping.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.XhsAttributeMappingTest.testXhsAttributeMapping.executor&quot;: &quot;Debug&quot;,
    &quot;Maven.ecommate-common [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-common [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-common [deploy].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-common [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-common [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-compute-server [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-gateway [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-gateway [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-gateway [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-infra-server [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-infra-server [deploy].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-infra-server [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-infra-server [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-orderlink-server [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-product-app [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-product-app [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-product-server [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-product-server [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-product-server [deploy].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-product-server [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-product-server [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ecommate-trade-server [clean].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ComputeApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.GatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.InfraApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.OrderlinkApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ProductApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.TradeApplication (1).executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.TradeApplication.executor&quot;: &quot;Run&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/project/yaotown/yaotown-ecommate/server/ecommate-trade-server/ecommate-trade-infrastructure/src/main/java&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;npm.i.executor&quot;: &quot;Run&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.32988507&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.propVCSSupport.DirectoryMappings&quot;,
    &quot;settings.editor.splitter.proportion&quot;: &quot;0.25763747&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\project\\yaotown\\yaotown-ecommate\\frontend\\yaotown-ecommate-admin-web\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.AesUtil.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.HttpUtils.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.OAuth2Utils.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.OrderSyncConfigProperties.executor&quot;: &quot;Run&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="advancedNonBlockingAnalysisEnabled" value="false" />
    <option name="notificationShown" value="true" />
    <option name="stackFrameCustomizationEnabled" value="false" />
    <option name="streamDebugEnabled" value="false" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\project\yaotown\yaotown-ecommate\common-lib\doc\json\order" />
      <recent name="D:\project\yaotown\yaotown-ecommate\server\ecommate-orderlink-server\ecommate-orderlink-web\src\main\resources" />
      <recent name="D:\project\yaotown\yaotown-ecommate\server\ecommate-orderlink-server" />
      <recent name="D:\project\yaotown\yaotown-ecommate\common-lib\doc\sql" />
      <recent name="D:\project\yaotown\yaotown-ecommate\server\ecommate-orderlink-server\ecommate-orderlink-module\ecommate-orderlink-module-biz\src\main\java\com\yaotown\ecommate\orderlink\module\service" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\project\yaotown\yaotown-ecommate\common-lib\doc\sql\updated" />
      <recent name="D:\project\yaotown\yaotown-ecommate\server\ecommate-product-server\ecommate-product-infrastructure\src\main\resources\mapper\management" />
      <recent name="D:\project\yaotown\yaotown-ecommate\frontend\yaotown-ecommate-admin-web\src\views\product\category-mapping" />
      <recent name="D:\project\yaotown\yaotown-ecommate\common-lib\doc\sql" />
      <recent name="D:\project\yaotown\yaotown-ecommate\common-lib\doc\json" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj" />
      <recent name="com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity" />
      <recent name="com.yaotown.ecommate.trade.types.enums.order" />
      <recent name="com.yaotown.ecommate.trade.infrastructure.mapper" />
      <recent name="com.yaotown.ecommate.trade.infrastructure.po" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.TradeApplication">
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ErpApiClientServiceTest.test2" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ecommate-trade-app" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yaotown.ecommate.trade.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.yaotown.ecommate.trade" />
      <option name="MAIN_CLASS_NAME" value="com.yaotown.ecommate.trade.ErpApiClientServiceTest" />
      <option name="METHOD_NAME" value="test2" />
      <option name="TEST_OBJECT" value="method" />
      <option name="VM_PARAMETERS" value="-ea --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SemanticMatchClientTest.test04" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ecommate-product-app" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yaotown.ecommate.product.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.yaotown.ecommate.product" />
      <option name="MAIN_CLASS_NAME" value="com.yaotown.ecommate.product.SemanticMatchClientTest" />
      <option name="METHOD_NAME" value="test04" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <shortenClasspath name="ARGS_FILE" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ecommate-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yaotown.gateway.GatewayApplication" />
      <option name="VM_PARAMETERS" value="-Xmx256M -Xms256M -Xss256k -XX:MetaspaceSize=64M -XX:MaxMetaspaceSize=128M" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="InfraApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ecommate-infra-web" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yaotown.ecommate.infra.web.InfraApplication" />
      <option name="VM_PARAMETERS" value="-Xmx256M -Xms256M -Xss256k -XX:MetaspaceSize=64M -XX:MaxMetaspaceSize=128M" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OrderlinkApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ecommate-orderlink-web" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yaotown.ecommate.orderlink.web.OrderlinkApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProductApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ecommate-product-app" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yaotown.ecommate.product.ProductApplication" />
      <option name="VM_PARAMETERS" value="-Xmx1024M -Xms1024M -Xss256k -XX:MetaspaceSize=64M -XX:MaxMetaspaceSize=256M" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TradeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ecommate-trade-app" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yaotown.ecommate.trade.TradeApplication" />
      <option name="VM_PARAMETERS" value="-Xmx256M -Xms256M -Xss256k -XX:MetaspaceSize=128M -XX:MaxMetaspaceSize=256M --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yaotown.ecommate.trade.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/frontend/yaotown-ecommate-admin-web/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="i" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/frontend/yaotown-ecommate-admin-web/package.json" />
      <command value="run" />
      <scripts>
        <script value="i" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="JUnit.ErpApiClientServiceTest.test2" />
      <item itemvalue="JUnit.SemanticMatchClientTest.test04" />
      <item itemvalue="npm.dev" />
      <item itemvalue="npm.i" />
      <item itemvalue="Spring Boot.GatewayApplication" />
      <item itemvalue="Spring Boot.OrderlinkApplication" />
      <item itemvalue="Spring Boot.ProductApplication" />
      <item itemvalue="Spring Boot.InfraApplication" />
      <item itemvalue="Spring Boot.TradeApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.ErpApiClientServiceTest.test2" />
        <item itemvalue="JUnit.ErpApiClientServiceTest.test2" />
        <item itemvalue="npm.i" />
        <item itemvalue="npm.dev" />
        <item itemvalue="JUnit.SemanticMatchClientTest.test04" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="84e89aad-338c-4271-ab1d-5298a3cb7995" name="更改" comment="" />
      <created>1744797289876</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744797289876</updated>
      <workItem from="1744797291005" duration="6322000" />
      <workItem from="1744803810773" duration="652000" />
      <workItem from="1744857560066" duration="27892000" />
      <workItem from="1744946570398" duration="121000" />
      <workItem from="1744946703046" duration="106936000" />
      <workItem from="1745475121114" duration="46487000" />
      <workItem from="1745582159258" duration="4270000" />
      <workItem from="1745718792168" duration="989000" />
      <workItem from="1745719805134" duration="62000" />
      <workItem from="1745719879718" duration="6021000" />
      <workItem from="1745725935607" duration="57338000" />
      <workItem from="1745891876136" duration="91335000" />
      <workItem from="1746495896307" duration="129306000" />
      <workItem from="1746789558774" duration="30902000" />
      <workItem from="1746862156722" duration="16764000" />
      <workItem from="1746955632753" duration="4231000" />
      <workItem from="1747018536783" duration="1360000" />
      <workItem from="1747020261415" duration="83221000" />
      <workItem from="1747200797659" duration="10483000" />
      <workItem from="1747217432059" duration="122865000" />
      <workItem from="1747647519802" duration="209000" />
      <workItem from="1747647741690" duration="2941000" />
      <workItem from="1747651199483" duration="65234000" />
      <workItem from="1747831881719" duration="6560000" />
      <workItem from="1747880580248" duration="10998000" />
      <workItem from="1747900178289" duration="41028000" />
      <workItem from="1747995681649" duration="66000" />
      <workItem from="1747995758057" duration="112536000" />
      <workItem from="1748310133564" duration="17736000" />
      <workItem from="1748344328786" duration="7514000" />
      <workItem from="1748394305401" duration="22146000" />
      <workItem from="1748489034292" duration="42390000" />
      <workItem from="1748605032920" duration="9166000" />
      <workItem from="1748916247753" duration="58361000" />
      <workItem from="1749107142698" duration="26289000" />
      <workItem from="1749210324324" duration="16653000" />
      <workItem from="1749378650826" duration="48316000" />
      <workItem from="1749486089533" duration="47770000" />
      <workItem from="1749643498586" duration="6510000" />
      <workItem from="1749656036118" duration="1432000" />
      <workItem from="1749657479039" duration="855000" />
      <workItem from="1749658341136" duration="245000" />
      <workItem from="1749658592298" duration="2554000" />
      <workItem from="1749661158495" duration="6726000" />
      <workItem from="1749697407317" duration="20333000" />
      <workItem from="1749720891347" duration="73507000" />
      <workItem from="1749881713181" duration="104000" />
      <workItem from="1749881835271" duration="25055000" />
      <workItem from="1749960032063" duration="168972000" />
      <workItem from="1750340824588" duration="87813000" />
      <workItem from="1750666157475" duration="26838000" />
      <workItem from="1750752471870" duration="10478000" />
      <workItem from="1750771405070" duration="1284000" />
      <workItem from="1750817996709" duration="33082000" />
      <workItem from="1750904952534" duration="60053000" />
      <workItem from="1751100030073" duration="90167000" />
      <workItem from="1751455444410" duration="134664000" />
      <workItem from="1751968428144" duration="66904000" />
      <workItem from="1752200098406" duration="275815000" />
      <workItem from="1753068949788" duration="81000" />
      <workItem from="1753069071072" duration="66071000" />
      <workItem from="1753266040284" duration="33588000" />
      <workItem from="1753353769725" duration="1093000" />
      <workItem from="1753354891366" duration="67811000" />
      <workItem from="1753753892273" duration="20395000" />
      <workItem from="1753779282714" duration="128000" />
      <workItem from="1753779421997" duration="325000" />
      <workItem from="1753779826112" duration="264000" />
      <workItem from="1753780096769" duration="8565000" />
      <workItem from="1753789770978" duration="236000" />
      <workItem from="1753790225829" duration="4026000" />
      <workItem from="1753794266785" duration="41843000" />
      <workItem from="1753944331858" duration="18214000" />
      <workItem from="1754018320442" duration="370000" />
      <workItem from="1754018698090" duration="128000" />
      <workItem from="1754018950188" duration="4732000" />
    </task>
    <task id="LOCAL-00586" summary="[U] 小红书转换平台商品结构">
      <option name="closed" value="true" />
      <created>1752928312693</created>
      <option name="number" value="00586" />
      <option name="presentableId" value="LOCAL-00586" />
      <option name="project" value="LOCAL" />
      <updated>1752928312693</updated>
    </task>
    <task id="LOCAL-00587" summary="[U] 添加平台类目属性查询方法">
      <option name="closed" value="true" />
      <created>1753086789288</created>
      <option name="number" value="00587" />
      <option name="presentableId" value="LOCAL-00587" />
      <option name="project" value="LOCAL" />
      <updated>1753086789289</updated>
    </task>
    <task id="LOCAL-00588" summary="[U] 小红书类目属性值映射处理（选项值映射）">
      <option name="closed" value="true" />
      <created>1753088683873</created>
      <option name="number" value="00588" />
      <option name="presentableId" value="LOCAL-00588" />
      <option name="project" value="LOCAL" />
      <updated>1753088683873</updated>
    </task>
    <task id="LOCAL-00589" summary="[U] 小红书类目属性值映射处理（选项值映射）">
      <option name="closed" value="true" />
      <created>1753089008484</created>
      <option name="number" value="00589" />
      <option name="presentableId" value="LOCAL-00589" />
      <option name="project" value="LOCAL" />
      <updated>1753089008484</updated>
    </task>
    <task id="LOCAL-00590" summary="[A] 编辑平台资料（快手）接口">
      <option name="closed" value="true" />
      <created>1753089897150</created>
      <option name="number" value="00590" />
      <option name="presentableId" value="LOCAL-00590" />
      <option name="project" value="LOCAL" />
      <updated>1753089897150</updated>
    </task>
    <task id="LOCAL-00591" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753091590165</created>
      <option name="number" value="00591" />
      <option name="presentableId" value="LOCAL-00591" />
      <option name="project" value="LOCAL" />
      <updated>1753091590165</updated>
    </task>
    <task id="LOCAL-00592" summary="[U] 处理快手商品数据映射">
      <option name="closed" value="true" />
      <created>1753093201166</created>
      <option name="number" value="00592" />
      <option name="presentableId" value="LOCAL-00592" />
      <option name="project" value="LOCAL" />
      <updated>1753093201166</updated>
    </task>
    <task id="LOCAL-00593" summary="[U] 处理快手类目属性值映射">
      <option name="closed" value="true" />
      <created>1753094679756</created>
      <option name="number" value="00593" />
      <option name="presentableId" value="LOCAL-00593" />
      <option name="project" value="LOCAL" />
      <updated>1753094679756</updated>
    </task>
    <task id="LOCAL-00594" summary="[U] 调整对象命名">
      <option name="closed" value="true" />
      <created>1753163893614</created>
      <option name="number" value="00594" />
      <option name="presentableId" value="LOCAL-00594" />
      <option name="project" value="LOCAL" />
      <updated>1753163893614</updated>
    </task>
    <task id="LOCAL-00595" summary="[A] 店铺token自动刷新维护处理">
      <option name="closed" value="true" />
      <created>1753180071618</created>
      <option name="number" value="00595" />
      <option name="presentableId" value="LOCAL-00595" />
      <option name="project" value="LOCAL" />
      <updated>1753180071619</updated>
    </task>
    <task id="LOCAL-00596" summary="[A] 编辑平台资料（视频号小店）">
      <option name="closed" value="true" />
      <created>1753196742812</created>
      <option name="number" value="00596" />
      <option name="presentableId" value="LOCAL-00596" />
      <option name="project" value="LOCAL" />
      <updated>1753196742812</updated>
    </task>
    <task id="LOCAL-00597" summary="[A] 视频号小店商品映射处理器">
      <option name="closed" value="true" />
      <created>1753198853483</created>
      <option name="number" value="00597" />
      <option name="presentableId" value="LOCAL-00597" />
      <option name="project" value="LOCAL" />
      <updated>1753198853483</updated>
    </task>
    <task id="LOCAL-00598" summary="[U] 视频号小店类目属性映射处理">
      <option name="closed" value="true" />
      <created>1753237432612</created>
      <option name="number" value="00598" />
      <option name="presentableId" value="LOCAL-00598" />
      <option name="project" value="LOCAL" />
      <updated>1753237432612</updated>
    </task>
    <task id="LOCAL-00599" summary="[U] 定时任务补充开关配置">
      <option name="closed" value="true" />
      <created>1753250992083</created>
      <option name="number" value="00599" />
      <option name="presentableId" value="LOCAL-00599" />
      <option name="project" value="LOCAL" />
      <updated>1753250992083</updated>
    </task>
    <task id="LOCAL-00600" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753251977722</created>
      <option name="number" value="00600" />
      <option name="presentableId" value="LOCAL-00600" />
      <option name="project" value="LOCAL" />
      <updated>1753251977722</updated>
    </task>
    <task id="LOCAL-00601" summary="[U] 供应商类目管理接口">
      <option name="closed" value="true" />
      <created>1753253607188</created>
      <option name="number" value="00601" />
      <option name="presentableId" value="LOCAL-00601" />
      <option name="project" value="LOCAL" />
      <updated>1753253607188</updated>
    </task>
    <task id="LOCAL-00602" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753257499243</created>
      <option name="number" value="00602" />
      <option name="presentableId" value="LOCAL-00602" />
      <option name="project" value="LOCAL" />
      <updated>1753257499243</updated>
    </task>
    <task id="LOCAL-00603" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753261224796</created>
      <option name="number" value="00603" />
      <option name="presentableId" value="LOCAL-00603" />
      <option name="project" value="LOCAL" />
      <updated>1753261224796</updated>
    </task>
    <task id="LOCAL-00604" summary="[U] mongodb查询语句日志打印组件">
      <option name="closed" value="true" />
      <created>1753273944718</created>
      <option name="number" value="00604" />
      <option name="presentableId" value="LOCAL-00604" />
      <option name="project" value="LOCAL" />
      <updated>1753273944719</updated>
    </task>
    <task id="LOCAL-00605" summary="[F] 修复类目属性选项值转换失败问题">
      <option name="closed" value="true" />
      <created>1753274533839</created>
      <option name="number" value="00605" />
      <option name="presentableId" value="LOCAL-00605" />
      <option name="project" value="LOCAL" />
      <updated>1753274533840</updated>
    </task>
    <task id="LOCAL-00606" summary="[F] 修复类目属性选项聚合中_id字段异常问题">
      <option name="closed" value="true" />
      <created>1753275323750</created>
      <option name="number" value="00606" />
      <option name="presentableId" value="LOCAL-00606" />
      <option name="project" value="LOCAL" />
      <updated>1753275323750</updated>
    </task>
    <task id="LOCAL-00607" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753277592016</created>
      <option name="number" value="00607" />
      <option name="presentableId" value="LOCAL-00607" />
      <option name="project" value="LOCAL" />
      <updated>1753277592016</updated>
    </task>
    <task id="LOCAL-00608" summary="[U] 优化类目属性自动映射处理">
      <option name="closed" value="true" />
      <created>1753281400020</created>
      <option name="number" value="00608" />
      <option name="presentableId" value="LOCAL-00608" />
      <option name="project" value="LOCAL" />
      <updated>1753281400020</updated>
    </task>
    <task id="LOCAL-00609" summary="[U] sku规格解析修复">
      <option name="closed" value="true" />
      <created>1753341918006</created>
      <option name="number" value="00609" />
      <option name="presentableId" value="LOCAL-00609" />
      <option name="project" value="LOCAL" />
      <updated>1753341918006</updated>
    </task>
    <task id="LOCAL-00610" summary="[U] 转换到平台资料补充处理存在数据的情况">
      <option name="closed" value="true" />
      <created>1753343977465</created>
      <option name="number" value="00610" />
      <option name="presentableId" value="LOCAL-00610" />
      <option name="project" value="LOCAL" />
      <updated>1753343977465</updated>
    </task>
    <task id="LOCAL-00611" summary="[U] 抖店平台产品编辑后自动合并及转换">
      <option name="closed" value="true" />
      <created>1753349431285</created>
      <option name="number" value="00611" />
      <option name="presentableId" value="LOCAL-00611" />
      <option name="project" value="LOCAL" />
      <updated>1753349431285</updated>
    </task>
    <task id="LOCAL-00612" summary="[U] 快手平台产品编辑后自动合并及转换">
      <option name="closed" value="true" />
      <created>1753352929296</created>
      <option name="number" value="00612" />
      <option name="presentableId" value="LOCAL-00612" />
      <option name="project" value="LOCAL" />
      <updated>1753352929296</updated>
    </task>
    <task id="LOCAL-00613" summary="[U] 视频号小店，小红书平台产品编辑后自动合并及转换">
      <option name="closed" value="true" />
      <created>1753354798689</created>
      <option name="number" value="00613" />
      <option name="presentableId" value="LOCAL-00613" />
      <option name="project" value="LOCAL" />
      <updated>1753354798689</updated>
    </task>
    <task id="LOCAL-00614" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753360101611</created>
      <option name="number" value="00614" />
      <option name="presentableId" value="LOCAL-00614" />
      <option name="project" value="LOCAL" />
      <updated>1753360101611</updated>
    </task>
    <task id="LOCAL-00615" summary="[U] 补充文件路径相关表">
      <option name="closed" value="true" />
      <created>1753422949795</created>
      <option name="number" value="00615" />
      <option name="presentableId" value="LOCAL-00615" />
      <option name="project" value="LOCAL" />
      <updated>1753422949795</updated>
    </task>
    <task id="LOCAL-00616" summary="[U] 优化文件上传接口，补充私有文件下载接口">
      <option name="closed" value="true" />
      <created>1753432219293</created>
      <option name="number" value="00616" />
      <option name="presentableId" value="LOCAL-00616" />
      <option name="project" value="LOCAL" />
      <updated>1753432219293</updated>
    </task>
    <task id="LOCAL-00617" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753432392379</created>
      <option name="number" value="00617" />
      <option name="presentableId" value="LOCAL-00617" />
      <option name="project" value="LOCAL" />
      <updated>1753432392379</updated>
    </task>
    <task id="LOCAL-00618" summary="[U] 更新文件上传sdk版本为1.0.1-jdk17">
      <option name="closed" value="true" />
      <created>1753433168979</created>
      <option name="number" value="00618" />
      <option name="presentableId" value="LOCAL-00618" />
      <option name="project" value="LOCAL" />
      <updated>1753433168979</updated>
    </task>
    <task id="LOCAL-00619" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753435288234</created>
      <option name="number" value="00619" />
      <option name="presentableId" value="LOCAL-00619" />
      <option name="project" value="LOCAL" />
      <updated>1753435288234</updated>
    </task>
    <task id="LOCAL-00620" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753441149013</created>
      <option name="number" value="00620" />
      <option name="presentableId" value="LOCAL-00620" />
      <option name="project" value="LOCAL" />
      <updated>1753441149013</updated>
    </task>
    <task id="LOCAL-00621" summary="[U] 删除旧代码">
      <option name="closed" value="true" />
      <created>1753443515289</created>
      <option name="number" value="00621" />
      <option name="presentableId" value="LOCAL-00621" />
      <option name="project" value="LOCAL" />
      <updated>1753443515289</updated>
    </task>
    <task id="LOCAL-00622" summary="[U] 调整使用分表方式存储平台订单表数据">
      <option name="closed" value="true" />
      <created>1753770551507</created>
      <option name="number" value="00622" />
      <option name="presentableId" value="LOCAL-00622" />
      <option name="project" value="LOCAL" />
      <updated>1753770551508</updated>
    </task>
    <task id="LOCAL-00623" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753771689719</created>
      <option name="number" value="00623" />
      <option name="presentableId" value="LOCAL-00623" />
      <option name="project" value="LOCAL" />
      <updated>1753771689719</updated>
    </task>
    <task id="LOCAL-00624" summary="[U] hotfix">
      <option name="closed" value="true" />
      <created>1753774979793</created>
      <option name="number" value="00624" />
      <option name="presentableId" value="LOCAL-00624" />
      <option name="project" value="LOCAL" />
      <updated>1753774979793</updated>
    </task>
    <task id="LOCAL-00625" summary="[U] 优化转换平台产品信息：类目映射处理">
      <option name="closed" value="true" />
      <created>1753778637973</created>
      <option name="number" value="00625" />
      <option name="presentableId" value="LOCAL-00625" />
      <option name="project" value="LOCAL" />
      <updated>1753778637973</updated>
    </task>
    <task id="LOCAL-00626" summary="[U] 调整抖店拉单连接器(完成json对象定义、订单基础信息转换)">
      <option name="closed" value="true" />
      <created>1753796901125</created>
      <option name="number" value="00626" />
      <option name="presentableId" value="LOCAL-00626" />
      <option name="project" value="LOCAL" />
      <updated>1753796901125</updated>
    </task>
    <task id="LOCAL-00627" summary="[U] 平台订单补充收件人及订单备注表">
      <option name="closed" value="true" />
      <created>1753872181213</created>
      <option name="number" value="00627" />
      <option name="presentableId" value="LOCAL-00627" />
      <option name="project" value="LOCAL" />
      <updated>1753872181213</updated>
    </task>
    <task id="LOCAL-00628" summary="[U] 抖店连接器补充订单项、快递单号解析">
      <option name="closed" value="true" />
      <created>1753874032668</created>
      <option name="number" value="00628" />
      <option name="presentableId" value="LOCAL-00628" />
      <option name="project" value="LOCAL" />
      <updated>1753874032668</updated>
    </task>
    <task id="LOCAL-00629" summary="[U] 补充订单备注，订单收货人解析">
      <option name="closed" value="true" />
      <created>1753880466984</created>
      <option name="number" value="00629" />
      <option name="presentableId" value="LOCAL-00629" />
      <option name="project" value="LOCAL" />
      <updated>1753880466984</updated>
    </task>
    <task id="LOCAL-00630" summary="[U] 抖店平台订单连接器 补充订单备注，订单收货人解析">
      <option name="closed" value="true" />
      <created>1753931830070</created>
      <option name="number" value="00630" />
      <option name="presentableId" value="LOCAL-00630" />
      <option name="project" value="LOCAL" />
      <updated>1753931830070</updated>
    </task>
    <task id="LOCAL-00631" summary="[U] 平台订单连接器，补充根据游标拉取订单方法">
      <option name="closed" value="true" />
      <created>1753942489398</created>
      <option name="number" value="00631" />
      <option name="presentableId" value="LOCAL-00631" />
      <option name="project" value="LOCAL" />
      <updated>1753942489398</updated>
    </task>
    <task id="LOCAL-00632" summary="[U] 视频号小店平台订单连接器，根据游标拉取订单实现">
      <option name="closed" value="true" />
      <created>1753949362508</created>
      <option name="number" value="00632" />
      <option name="presentableId" value="LOCAL-00632" />
      <option name="project" value="LOCAL" />
      <updated>1753949362508</updated>
    </task>
    <task id="LOCAL-00633" summary="[U] 视频号小店平台订单连接器，实现订单信息解析">
      <option name="closed" value="true" />
      <created>1753954182675</created>
      <option name="number" value="00633" />
      <option name="presentableId" value="LOCAL-00633" />
      <option name="project" value="LOCAL" />
      <updated>1753954182675</updated>
    </task>
    <task id="LOCAL-00634" summary="[U] 补充店铺统一授权管理sql">
      <option name="closed" value="true" />
      <created>1753960855989</created>
      <option name="number" value="00634" />
      <option name="presentableId" value="LOCAL-00634" />
      <option name="project" value="LOCAL" />
      <updated>1753960855990</updated>
    </task>
    <option name="localTasksCounter" value="635" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Paths">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:D:/project/yaotown/yaotown-ecommate/server/ecommate-trade-server" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:D:/project/yaotown/yaotown-ecommate/framework/ecommate-gateway" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:D:/project/yaotown/yaotown-ecommate/server/ecommate-infra-server" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:D:/project/yaotown/yaotown-ecommate/server/ecommate-product-server" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dir:D:/project/yaotown/yaotown-ecommate/common-lib/doc" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="feature/supplier" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="[U] mongodb查询语句日志打印组件" />
    <MESSAGE value="[F] 修复类目属性选项值转换失败问题" />
    <MESSAGE value="[F] 修复类目属性选项聚合中_id字段异常问题" />
    <MESSAGE value="[U] 优化类目属性自动映射处理" />
    <MESSAGE value="[U] sku规格解析修复" />
    <MESSAGE value="[U] 转换到平台资料补充处理存在数据的情况" />
    <MESSAGE value="[U] 抖店平台产品编辑后自动合并及转换" />
    <MESSAGE value="[U] 快手平台产品编辑后自动合并及转换" />
    <MESSAGE value="[U] 视频号小店，小红书平台产品编辑后自动合并及转换" />
    <MESSAGE value="[U] 补充文件路径相关表" />
    <MESSAGE value="[U] 优化文件上传接口，补充私有文件下载接口" />
    <MESSAGE value="[U] 更新文件上传sdk版本为1.0.1-jdk17" />
    <MESSAGE value="[U] 删除旧代码" />
    <MESSAGE value="[U] 调整使用分表方式存储平台订单表数据" />
    <MESSAGE value="[U] hotfix" />
    <MESSAGE value="[U] 优化转换平台产品信息：类目映射处理" />
    <MESSAGE value="[U] 调整抖店拉单连接器(完成json对象定义、订单基础信息转换)" />
    <MESSAGE value="[U] 平台订单补充收件人及订单备注表" />
    <MESSAGE value="[U] 抖店连接器补充订单项、快递单号解析" />
    <MESSAGE value="[U] 补充订单备注，订单收货人解析" />
    <MESSAGE value="[U] 抖店平台订单连接器 补充订单备注，订单收货人解析" />
    <MESSAGE value="[U] 平台订单连接器，补充根据游标拉取订单方法" />
    <MESSAGE value="[U] 视频号小店平台订单连接器，根据游标拉取订单实现" />
    <MESSAGE value="[U] 视频号小店平台订单连接器，实现订单信息解析" />
    <MESSAGE value="[U] 补充店铺统一授权管理sql" />
    <option name="LAST_COMMIT_MESSAGE" value="[U] 补充店铺统一授权管理sql" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>