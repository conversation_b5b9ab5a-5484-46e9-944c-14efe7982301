package com.yaotown.ecommate.product.domain.product.ecomlink.model.aggregate.sphxd;

import com.yaotown.ecommate.product.domain.product.ecomlink.model.aggregate.ecomproduct.ECommerceProductModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * 视频号小店产品聚合模型
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SphxdProductAggregate extends ECommerceProductModel {

    /**
     * 外部平台自定义商品ID
     */
    private String outProductId;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题(已废弃)
     */
    private String subTitle;

    /**
     * 商品短标题
     */
    private String shortTitle;

    /**
     * 主图，多张
     */
    private List<String> headImgs;

    /**
     * 发货方式：0-快递发货；1-无需快递，手机号发货；3-无需快递，可选发货账号类型
     */
    private Integer deliverMethod;

    /**
     * 发货账号：1-微信openid；2-QQ号；3-手机号；4-邮箱
     */
    private List<Integer> deliverAcctType;

    /**
     * 商品详情信息
     */
    private DescInfo descInfo;

    /**
     * 商品类目，新类目树结构
     */
    private List<CategoryInfo> catsV2;

    /**
     * 商品类目，兼容旧版API
     */
    private List<CategoryInfo> cats;
    private List<String> qualifications;
    /**
     * 商品参数
     */
    private List<Attribute> attrs;

    /**
     * 商家自定义的商品编码
     */
    private String spuCode;

    /**
     * 品牌id，无品牌为"2100000000"
     */
    private String brandId;

    /**
     * 商品资质列表
     */
    private List<ProductQualification> productQuaInfos;

    /**
     * 运费信息
     */
    private ExpressInfo expressInfo;

    /**
     * 售后说明
     */
    @JsonProperty("afterSaleDesc")
    private String aftersaleDesc;

    /**
     * 限购信息
     */
    private LimitedInfo limitedInfo;

    /**
     * 额外服务
     */
    private ExtraService extraService;

    /**
     * SKU列表
     */
    private List<Sku> skus;

    /**
     * 添加完成后是否立即上架。1:是；0:否；默认0
     */
    private Integer listing;

    /**
     * 售后信息
     */
    private AfterSaleInfo afterSaleInfo;

    /**
     * 尺码表信息
     */
    private SizeChart sizeChart;

    /**
     * 商品详情信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DescInfo {
        /**
         * 商品详情图片
         */
        private List<String> imgs;
        
        /**
         * 商品详情文本
         */
        private String desc;
    }

    /**
     * 类目信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CategoryInfo {
        /**
         * 类目ID
         */
        private String catId;
    }

    /**
     * 属性信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Attribute {
        /**
         * 属性键key
         */
        private String attrKey;
        
        /**
         * 属性值
         */
        private String attrValue;
    }

    /**
     * 商品资质信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProductQualification {
        /**
         * 商品资质id
         */
        private String quaId;
        
        /**
         * 商品资质图片列表
         */
        private List<String> quaUrl;
    }

    /**
     * 运费信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExpressInfo {
        /**
         * 运费模板ID
         */
        private String templateId;
        
        /**
         * 商品重量，单位克
         */
        private Integer weight;
    }

    /**
     * 限购信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LimitedInfo {
        /**
         * 限购周期类型，0:无限购（默认），1:按自然日限购，2:按自然周限购，3:按自然月限购，4:按自然年限购
         */
        private Integer periodType;
        
        /**
         * 限购数量
         */
        private Integer limitedBuyNum;
    }

    /**
     * 额外服务
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExtraService {
        /**
         * 是否支持七天无理由退货
         */
        private Integer sevenDayReturn;
        
        /**
         * 是否支持运费险
         */
        private Integer freightInsurance;
        
        /**
         * 是否支持假一赔三
         */
        private Integer fakeOnePayThree;
        
        /**
         * 是否支持坏损包退
         */
        private Integer damageGuarantee;
    }

    /**
     * SKU信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Sku {
        /**
         * 外部平台自定义sku_id
         */
        private String outSkuId;
        
        /**
         * sku小图
         */
        private String thumbImg;
        
        /**
         * 售卖价格，以分为单位
         */
        private Long salePrice;
        
        /**
         * 库存
         */
        private Integer stockNum;
        
        /**
         * 商家自定义的sku编码
         */
        private String skuCode;
        
        /**
         * 销售属性
         */
        private List<SkuAttribute> skuAttrs;
        
        /**
         * SKU发货信息
         */
        private SkuDeliverInfo skuDeliverInfo;
    }

    /**
     * SKU属性
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SkuAttribute {
        /**
         * 属性键key
         */
        private String attrKey;
        
        /**
         * 属性值
         */
        private String attrValue;
    }

    /**
     * SKU发货信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SkuDeliverInfo {
        /**
         * sku库存情况。0:现货（默认），1:全款预售
         */
        private Integer stockType;
        
        /**
         * sku发货节点，该字段仅对stock_type=1有效。0:付款后n天发货，1:预售结束后n天发货
         */
        private Integer fullPaymentPresaleDeliveryType;
        
        /**
         * sku预售周期开始时间，秒级时间戳
         */
        private Long presaleBeginTime;
        
        /**
         * sku预售周期结束时间，秒级时间戳
         */
        private Long presaleEndTime;
        
        /**
         * sku发货时效，即付款后/预售结束后{full_payment_presale_delivery_time}天内发货
         */
        private Integer fullPaymentPresaleDeliveryTime;
    }

    /**
     * 售后信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AfterSaleInfo {
        /**
         * 售后/退货地址id
         */
        private Long afterSaleAddressId;
    }

    /**
     * 尺码表信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SizeChart {
        /**
         * 是否启用尺码表
         */
        private Boolean enable;
        
        /**
         * 尺码表规格列表
         */
        private List<Specification> specificationList;
    }

    /**
     * 尺码表规格
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Specification {
        /**
         * 尺码属性名称
         */
        private String name;
        
        /**
         * 尺码属性值的单位
         */
        private String unit;
        
        /**
         * 尺码属性值是否为区间
         */
        private Boolean isRange;
        
        /**
         * 尺码值与尺码属性值的映射列表
         */
        private List<ValueMapping> valueList;
    }

    /**
     * 尺码值映射
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ValueMapping {
        /**
         * 尺码值，需与商品属性中的尺码规格保持一致
         */
        private String key;
        
        /**
         * 尺码属性值；属性值为单值时填写
         */
        private String value;
        
        /**
         * 尺码属性值的左边界，需小于右边界；属性值为区间时填写
         */
        private String left;
        
        /**
         * 尺码属性值的右边界，需大于左边界；属性值为区间时填写
         */
        private String right;
    }

    /**
     * API响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Response {
        /**
         * 错误码
         */
        private Integer errcode;
        
        /**
         * 错误信息
         */
        private String errmsg;
        
        /**
         * 响应数据
         */
        private ResponseData data;
    }
    
    /**
     * API响应数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResponseData {
        /**
         * 商品ID
         */
        private String productId;
        
        /**
         * 创建时间
         */
        private String createTime;
    }
} 