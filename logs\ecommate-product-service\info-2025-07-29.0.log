2025-07-29 16:13:39.175 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848
2025-07-29 16:13:39.176 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000
2025-07-29 16:13:39.178 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0
2025-07-29 16:13:39.182 [Thread-2] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-07-29 16:13:39.218 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels.....
2025-07-29 16:13:39.218 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-07-29 16:13:39.218 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null
2025-07-29 16:13:39.219 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect properties labels: {}
2025-07-29 16:13:39.220 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null
2025-07-29 16:13:39.220 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect jvm labels: {}
2025-07-29 16:13:39.220 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect env raw labels: null
2025-07-29 16:13:39.220 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect env labels: {}
2025-07-29 16:13:39.220 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-07-29 16:13:39.236 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:13:39.236 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:13:39.314 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2025-07-29 16:13:39.477 [main] INFO  [ecommate-product-server] c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-07-29 16:13:39.488 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of 45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0
2025-07-29 16:13:39.525 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$635/0x000002308c42e8e8
2025-07-29 16:13:39.525 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$636/0x000002308c42ed08
2025-07-29 16:13:39.526 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-29 16:13:39.527 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-29 16:13:39.537 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-29 16:13:39.581 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:13:40.809 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-29 16:13:40.811 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-29 16:13:40.812 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-29 16:13:40.813 [main] INFO  [ecommate-product-server] c.a.n.c.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-29 16:13:40.838 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1753776813792_192.168.48.1_53933
2025-07-29 16:13:40.839 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] Notify connected event to listeners.
2025-07-29 16:13:40.839 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] Connected,notify listen context...
2025-07-29 16:13:40.841 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-29 16:13:40.841 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [45b1d2c4-00cd-4fa1-96a8-e752811e783e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$656/0x000002308c5a8d88
2025-07-29 16:13:40.934 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.utils.JvmUtil - isMultiInstance:false
2025-07-29 16:13:41.130 [main] INFO  [ecommate-product-server] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server,DEFAULT_GROUP'}]
2025-07-29 16:13:41.161 [main] INFO  [ecommate-product-server] com.yaotown.ecommate.product.ProductApplication - The following 1 profile is active: "local"
2025-07-29 16:13:43.494 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 16:13:43.496 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-29 16:13:44.106 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 604 ms. Found 3 MongoDB repository interfaces.
2025-07-29 16:13:44.122 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 16:13:44.124 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 16:13:44.166 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.domain.product.compute.repository.AttributeDefinitionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-29 16:13:44.166 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.infrastructure.mapper.compute.AttributeMappingMapper; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-29 16:13:44.166 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.infrastructure.mapper.compute.AttributeOptionMappingMapper; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-29 16:13:44.166 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-07-29 16:13:44.717 [main] INFO  [ecommate-product-server] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=6ebf2d27-d9d9-38bd-aabe-0aba95511e49
2025-07-29 16:13:46.091 [main] INFO  [ecommate-product-server] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 16:13:46.094 [main] INFO  [ecommate-product-server] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4899 ms
2025-07-29 16:13:46.771 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:13:46.876 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:13:48.768 [main] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2025-07-29 16:13:49.380 [main] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2,db_center} inited
2025-07-29 16:13:49.381 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [db_center] success
2025-07-29 16:13:49.381 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 16:13:49.381 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-29 16:13:51.084 [main] INFO  [ecommate-product-server] org.redisson.Version - Redisson 3.32.0
2025-07-29 16:13:51.570 [redisson-netty-1-4] INFO  [ecommate-product-server] org.redisson.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6379
2025-07-29 16:13:51.716 [redisson-netty-1-19] INFO  [ecommate-product-server] org.redisson.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6379
2025-07-29 16:13:52.747 [main] INFO  [ecommate-product-server] org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.0.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Alibaba/17.0.14+7"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='yaotown_product', source='yaotown_product', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[com.yaotown.ecommate.product.config.MongoQueryLogConfig$MongoQueryCommandListener@4b4a5691, io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@160c47b4], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@6e236542, com.mongodb.Jep395RecordCodecProvider@1fd18007, com.mongodb.KotlinCodecProvider@67e3839b]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@56d5a50f], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null}
2025-07-29 16:13:52.789 [cluster-ClusterId{value='688882c000b99926f74efc85', description='null'}-**************:27017] INFO  [ecommate-product-server] org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=**************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=51470600}
2025-07-29 16:13:54.181 [main] INFO  [ecommate-product-server] c.y.e.p.i.repository.CategoryAttributeRepository - 初始化MongoDB索引
2025-07-29 16:13:54.524 [main] INFO  [ecommate-product-server] c.y.e.p.i.repository.CategoryAttributeRepository - MongoDB索引创建完成
2025-07-29 16:13:56.160 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:13:59.600 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:13:59.622 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:13:59.642 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:14:03.369 [main] INFO  [ecommate-product-server] o.a.r.s.autoconfigure.RocketMQAutoConfiguration - a producer (ecommate-product-server) init on namesrv **************:9876
2025-07-29 16:14:06.045 [main] INFO  [ecommate-product-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:accountRegisterMessageConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-29 16:14:06.528 [main] INFO  [ecommate-product-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:listingTaskHandleMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-29 16:14:06.555 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:14:09.959 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:14:10.892 [main] INFO  [ecommate-product-server] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-29 16:14:11.442 [main] INFO  [ecommate-product-server] c.y.ecommate.common.id.config.IdAutoConfiguration - 构建ID生成器时使用随机workId，它的值为: 60
2025-07-29 16:14:13.800 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:14:14.209 [main] INFO  [ecommate-product-server] io.undertow - starting server: Undertow - 2.3.13.Final
2025-07-29 16:14:14.240 [main] INFO  [ecommate-product-server] org.xnio - XNIO version 3.8.8.Final
2025-07-29 16:14:14.275 [main] INFO  [ecommate-product-server] org.xnio.nio - XNIO NIO Implementation Version 3.8.8.Final
2025-07-29 16:14:14.397 [main] INFO  [ecommate-product-server] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 16:14:14.622 [main] INFO  [ecommate-product-server] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 38786 (http) with context path '/'
2025-07-29 16:14:14.638 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from ans.namespace attribute : null
2025-07-29 16:14:14.639 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-07-29 16:14:14.639 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from namespace attribute :null
2025-07-29 16:14:14.676 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-07-29 16:14:14.692 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:14:14.692 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:14:14.783 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of e7811af8-b6ae-4dfe-ab46-b5f982337e36
2025-07-29 16:14:14.791 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Create naming rpc client for uuid->e7811af8-b6ae-4dfe-ab46-b5f982337e36
2025-07-29 16:14:14.791 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-07-29 16:14:14.791 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-07-29 16:14:14.792 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-07-29 16:14:14.793 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Try to connect to server on start up, server: {serverIp = '**************', server main port = 9848}
2025-07-29 16:14:14.793 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:10848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:14:17.842 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-29 16:14:17.843 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:14:17.877 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Success to connect to server [**************:8848] on start up, connectionId = 1753776850981_192.168.48.1_54005
2025-07-29 16:14:17.877 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-29 16:14:17.877 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$656/0x000002308c5a8d88
2025-07-29 16:14:17.877 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Notify connected event to listeners.
2025-07-29 16:14:17.877 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Grpc connection connect
2025-07-29 16:14:17.880 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [REGISTER-SERVICE] ecommate-cfx registering service ecommate-product-server with instance Instance{instanceId='null', ip='*************', port=38786, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={preserved.heart.beat.timeout=3000, preserved.ip.delete.timeout=3000, preserved.register.source=SPRING_CLOUD, version=1.0.0, IPv6=null, preserved.heart.beat.interval=1000}}
2025-07-29 16:14:17.901 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ecommate-product-server *************:38786 register finished
2025-07-29 16:14:19.132 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:14:20.642 [main] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-enterprise-create-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-enterprise-create_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:14:22.087 [main] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-task-handle-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-task-handle_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:14:22.115 [main] INFO  [ecommate-product-server] com.yaotown.ecommate.product.ProductApplication - Started ProductApplication in 46.938 seconds (process running for 47.933)
2025-07-29 16:14:22.127 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - config listener notify warn timeout millis use default 60000 millis 
2025-07-29 16:14:22.127 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snapshot = true 
2025-07-29 16:14:22.128 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:14:22.160 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:14:22.160 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server, group=DEFAULT_GROUP
2025-07-29 16:14:22.162 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server-local.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:14:22.162 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server-local.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:14:22.162 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server-local.yaml, group=DEFAULT_GROUP
2025-07-29 16:14:22.163 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:14:22.163 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:14:22.163 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server.yaml, group=DEFAULT_GROUP
2025-07-29 16:14:22.438 [RMI TCP Connection(5)-*************] INFO  [ecommate-product-server] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:14:22.439 [RMI TCP Connection(5)-*************] INFO  [ecommate-product-server] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 16:14:22.447 [RMI TCP Connection(5)-*************] INFO  [ecommate-product-server] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-07-29 16:15:03.095 [XNIO-1 task-2] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:ecommate-infra-server, group:DEFAULT_GROUP, clusters: 
2025-07-29 16:15:03.095 [XNIO-1 task-2] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:ecommate-infra-server, group:DEFAULT_GROUP, cluster: 
2025-07-29 16:15:03.105 [XNIO-1 task-2] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@ecommate-infra-server -> [{"instanceId":"*************#38785##DEFAULT_GROUP@@ecommate-infra-server","ip":"*************","port":38785,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-infra-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000,"ipDeleteTimeout":3000}]
2025-07-29 16:15:03.106 [XNIO-1 task-2] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@ecommate-infra-server -> [{"instanceId":"*************#38785##DEFAULT_GROUP@@ecommate-infra-server","ip":"*************","port":38785,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ecommate-infra-server","metadata":{"preserved.heart.beat.timeout":"3000","preserved.ip.delete.timeout":"3000","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"1000"},"instanceHeartBeatTimeOut":3000,"instanceHeartBeatInterval":1000,"ipDeleteTimeout":3000}]
2025-07-29 16:15:03.360 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/supplier/product/supplier-product/trans-to-platform-product/FXG) 参数({
    "supplierProductId": 676920503282373
})]
2025-07-29 16:15:03.693 [nacos-grpc-client-executor-**************-20] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Receive server push request, request = NotifySubscriberRequest, requestId = 501
2025-07-29 16:15:03.695 [nacos-grpc-client-executor-**************-20] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [e7811af8-b6ae-4dfe-ab46-b5f982337e36] Ack server push request, request = NotifySubscriberRequest, requestId = 501
2025-07-29 16:15:04.229 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/supplier/product/supplier-product/trans-to-platform-product/FXG) 耗时(869 ms)]
2025-07-29 16:19:39.067 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/supplier/product/supplier-product/trans-to-platform-product/FXG) 参数({
    "supplierProductId": 676920503282373
})]
2025-07-29 16:19:42.765 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/supplier/product/supplier-product/trans-to-platform-product/FXG) 耗时(3698 ms)]
2025-07-29 16:35:27.208 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] io.undertow - stopping server: Undertow - 2.3.13.Final
2025-07-29 16:35:27.213 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 16:35:27.219 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 16:35:27.219 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] ecommate-cfx deregistering service ecommate-product-server with instance: Instance{instanceId='null', ip='*************', port=38786, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
2025-07-29 16:35:27.228 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-07-29 16:35:27.229 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->e7811af8-b6ae-4dfe-ab46-b5f982337e36
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@22e5cf8c[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 423]
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4d5ffd2c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-29 16:35:27.230 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - Close current connection 1753776850981_192.168.48.1_54005
2025-07-29 16:35:27.231 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2632face[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 255]
2025-07-29 16:35:27.231 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->e7811af8-b6ae-4dfe-ab46-b5f982337e36
2025-07-29 16:35:27.231 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-29 16:35:27.231 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialService - [null] CredentialService is freed
2025-07-29 16:35:27.231 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
2025-07-29 16:35:27.234 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-task-handle-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-task-handle_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:35:27.234 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-enterprise-create-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-enterprise-create_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:35:27.973 [com.alibaba.nacos.client.Worker.15] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-07-29 16:35:30.252 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 16:35:30.254 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-07-29 16:35:30.297 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-07-29 16:35:30.297 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.b.d.d.destroyer.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [db_center] success,
2025-07-29 16:35:30.297 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-29 16:35:30.297 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-29 16:35:30.297 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.b.d.d.destroyer.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 16:35:30.297 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 16:41:22.608 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [req-serv] nacos-server port:8848
2025-07-29 16:41:22.608 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - [settings] [http-client] connect timeout:1000
2025-07-29 16:41:22.610 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.utils.ParamUtil - PER_TASK_CONFIG_SIZE: 3000.0
2025-07-29 16:41:22.617 [Thread-2] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialWatcher - null No credential found
2025-07-29 16:41:22.650 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels.....
2025-07-29 16:41:22.650 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-07-29 16:41:22.650 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect properties raw labels: null
2025-07-29 16:41:22.652 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect properties labels: {}
2025-07-29 16:41:22.652 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect jvm raw labels: null
2025-07-29 16:41:22.652 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect jvm labels: {}
2025-07-29 16:41:22.652 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect env raw labels: null
2025-07-29 16:41:22.652 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - default nacos collect env labels: {}
2025-07-29 16:41:22.653 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.labels - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-07-29 16:41:22.670 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:41:22.670 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:41:22.739 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.Limiter - limitTime:5.0
2025-07-29 16:41:22.912 [main] INFO  [ecommate-product-server] c.a.n.client.config.impl.LocalConfigInfoProcessor - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-07-29 16:41:22.918 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of baf31395-66f6-4c46-842d-40a49f4ddc39_config-0
2025-07-29 16:41:22.955 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$635/0x000001fdec42e8e8
2025-07-29 16:41:22.955 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$636/0x000001fdec42ed08
2025-07-29 16:41:22.956 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-29 16:41:22.956 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-29 16:41:22.966 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 9848}
2025-07-29 16:41:23.002 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:10848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:41:26.728 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-29 16:41:26.728 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:41:26.858 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-29 16:41:26.861 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-29 16:41:26.862 [main] INFO  [ecommate-product-server] c.a.n.common.ability.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-29 16:41:26.862 [main] INFO  [ecommate-product-server] c.a.n.c.ability.discover.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-29 16:41:26.883 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Success to connect to server [**************:8848] on start up, connectionId = 1753778479865_192.168.48.1_54536
2025-07-29 16:41:26.884 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Notify connected event to listeners.
2025-07-29 16:41:26.884 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Connected,notify listen context...
2025-07-29 16:41:26.884 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-29 16:41:26.885 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [baf31395-66f6-4c46-842d-40a49f4ddc39_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$656/0x000001fdec5ae318
2025-07-29 16:41:26.988 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.utils.JvmUtil - isMultiInstance:false
2025-07-29 16:41:27.183 [main] INFO  [ecommate-product-server] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ecommate-product-server,DEFAULT_GROUP'}]
2025-07-29 16:41:27.222 [main] INFO  [ecommate-product-server] com.yaotown.ecommate.product.ProductApplication - The following 1 profile is active: "local"
2025-07-29 16:41:29.370 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 16:41:29.373 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-07-29 16:41:29.977 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 598 ms. Found 3 MongoDB repository interfaces.
2025-07-29 16:41:29.995 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 16:41:29.996 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 16:41:30.037 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.domain.product.compute.repository.AttributeDefinitionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-29 16:41:30.037 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.infrastructure.mapper.compute.AttributeMappingMapper; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-29 16:41:30.037 [main] INFO  [ecommate-product-server] o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yaotown.ecommate.product.infrastructure.mapper.compute.AttributeOptionMappingMapper; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-29 16:41:30.038 [main] INFO  [ecommate-product-server] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-07-29 16:41:30.578 [main] INFO  [ecommate-product-server] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=6ebf2d27-d9d9-38bd-aabe-0aba95511e49
2025-07-29 16:41:32.041 [main] INFO  [ecommate-product-server] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 16:41:32.041 [main] INFO  [ecommate-product-server] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4805 ms
2025-07-29 16:41:32.591 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:41:32.681 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:41:34.241 [main] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2025-07-29 16:41:34.871 [main] INFO  [ecommate-product-server] com.alibaba.druid.pool.DruidDataSource - {dataSource-2,db_center} inited
2025-07-29 16:41:34.872 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [db_center] success
2025-07-29 16:41:34.872 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 16:41:34.872 [main] INFO  [ecommate-product-server] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-29 16:41:36.447 [main] INFO  [ecommate-product-server] org.redisson.Version - Redisson 3.32.0
2025-07-29 16:41:36.881 [redisson-netty-1-4] INFO  [ecommate-product-server] org.redisson.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6379
2025-07-29 16:41:37.030 [redisson-netty-1-19] INFO  [ecommate-product-server] org.redisson.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6379
2025-07-29 16:41:37.593 [main] INFO  [ecommate-product-server] org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.0.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Alibaba/17.0.14+7"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='yaotown_product', source='yaotown_product', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[com.yaotown.ecommate.product.config.MongoQueryLogConfig$MongoQueryCommandListener@74e8ef59, io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@27192b0c], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@2e1a8c1a, com.mongodb.Jep395RecordCodecProvider@3fc736c4, com.mongodb.KotlinCodecProvider@770d65c2]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@30422a69], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null}
2025-07-29 16:41:37.634 [cluster-ClusterId{value='68888941c90e2167604820b8', description='null'}-**************:27017] INFO  [ecommate-product-server] org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=**************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=36449300}
2025-07-29 16:41:38.336 [main] INFO  [ecommate-product-server] c.y.e.p.i.repository.CategoryAttributeRepository - 初始化MongoDB索引
2025-07-29 16:41:38.591 [main] INFO  [ecommate-product-server] c.y.e.p.i.repository.CategoryAttributeRepository - MongoDB索引创建完成
2025-07-29 16:41:39.015 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:41:39.810 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:41:39.818 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:41:39.825 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:41:40.525 [main] INFO  [ecommate-product-server] o.a.r.s.autoconfigure.RocketMQAutoConfiguration - a producer (ecommate-product-server) init on namesrv **************:9876
2025-07-29 16:41:41.581 [main] INFO  [ecommate-product-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:accountRegisterMessageConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-29 16:41:41.819 [main] INFO  [ecommate-product-server] o.a.r.s.s.RocketMQMessageListenerContainerRegistrar - Register the listener to container, listenerBeanName:listingTaskHandleMqConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-29 16:41:41.835 [main] INFO  [ecommate-product-server] o.s.cloud.openfeign.FeignClientFactoryBean - For 'ecommate-infra-server' URL not provided. Will try picking an instance via load-balancing.
2025-07-29 16:41:44.106 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:41:44.513 [main] INFO  [ecommate-product-server] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-07-29 16:41:44.788 [main] INFO  [ecommate-product-server] c.y.ecommate.common.id.config.IdAutoConfiguration - 构建ID生成器时使用随机workId，它的值为: 0
2025-07-29 16:41:46.457 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:41:46.668 [main] INFO  [ecommate-product-server] io.undertow - starting server: Undertow - 2.3.13.Final
2025-07-29 16:41:46.682 [main] INFO  [ecommate-product-server] org.xnio - XNIO version 3.8.8.Final
2025-07-29 16:41:46.697 [main] INFO  [ecommate-product-server] org.xnio.nio - XNIO NIO Implementation Version 3.8.8.Final
2025-07-29 16:41:46.745 [main] INFO  [ecommate-product-server] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 16:41:46.815 [main] INFO  [ecommate-product-server] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 38786 (http) with context path '/'
2025-07-29 16:41:46.822 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from ans.namespace attribute : null
2025-07-29 16:41:46.822 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-07-29 16:41:46.822 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - initializer namespace from namespace attribute :null
2025-07-29 16:41:46.829 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-07-29 16:41:46.836 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 16:41:46.836 [main] INFO  [ecommate-product-server] c.a.n.p.auth.spi.client.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 16:41:46.916 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [RpcClientFactory] create a new rpc client of b7f902bb-97f2-4354-8b31-76d3db8d851e
2025-07-29 16:41:46.920 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Create naming rpc client for uuid->b7f902bb-97f2-4354-8b31-76d3db8d851e
2025-07-29 16:41:46.920 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [b7f902bb-97f2-4354-8b31-76d3db8d851e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-07-29 16:41:46.920 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [b7f902bb-97f2-4354-8b31-76d3db8d851e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-07-29 16:41:46.921 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [b7f902bb-97f2-4354-8b31-76d3db8d851e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-07-29 16:41:46.921 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [b7f902bb-97f2-4354-8b31-76d3db8d851e] Try to connect to server on start up, server: {serverIp = '**************', server main port = 9848}
2025-07-29 16:41:46.921 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:10848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:41:49.924 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [b7f902bb-97f2-4354-8b31-76d3db8d851e] Try to connect to server on start up, server: {serverIp = '**************', server main port = 8848}
2025-07-29 16:41:49.924 [main] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - grpc client connection server:************** ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-29 16:41:49.939 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [b7f902bb-97f2-4354-8b31-76d3db8d851e] Success to connect to server [**************:8848] on start up, connectionId = 1753778503058_192.168.48.1_54568
2025-07-29 16:41:49.939 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [b7f902bb-97f2-4354-8b31-76d3db8d851e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-29 16:41:49.939 [main] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [b7f902bb-97f2-4354-8b31-76d3db8d851e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$656/0x000001fdec5ae318
2025-07-29 16:41:49.939 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - [b7f902bb-97f2-4354-8b31-76d3db8d851e] Notify connected event to listeners.
2025-07-29 16:41:49.939 [com.alibaba.nacos.client.remote.worker.0] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Grpc connection connect
2025-07-29 16:41:49.941 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [REGISTER-SERVICE] ecommate-cfx registering service ecommate-product-server with instance Instance{instanceId='null', ip='*************', port=38786, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={preserved.heart.beat.timeout=3000, preserved.ip.delete.timeout=3000, preserved.register.source=SPRING_CLOUD, version=1.0.0, IPv6=null, preserved.heart.beat.interval=1000}}
2025-07-29 16:41:49.952 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - nacos registry, DEFAULT_GROUP ecommate-product-server *************:38786 register finished
2025-07-29 16:41:51.054 [main] INFO  [ecommate-product-server] org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-07-29 16:41:51.769 [main] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-enterprise-create-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-enterprise-create_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:41:52.408 [main] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - running container: DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-task-handle-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-task-handle_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:41:52.423 [main] INFO  [ecommate-product-server] com.yaotown.ecommate.product.ProductApplication - Started ProductApplication in 33.746 seconds (process running for 34.663)
2025-07-29 16:41:52.447 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - config listener notify warn timeout millis use default 60000 millis 
2025-07-29 16:41:52.447 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - nacos.cache.data.init.snapshot = true 
2025-07-29 16:41:52.447 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:41:52.464 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:41:52.464 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server, group=DEFAULT_GROUP
2025-07-29 16:41:52.464 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server-local.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:41:52.465 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server-local.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:41:52.465 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server-local.yaml, group=DEFAULT_GROUP
2025-07-29 16:41:52.465 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.ClientWorker - [fixed-ecommate-cfx-**************_8848-**************_9848] [subscribe] ecommate-product-server.yaml+DEFAULT_GROUP+ecommate-cfx
2025-07-29 16:41:52.465 [main] INFO  [ecommate-product-server] com.alibaba.nacos.client.config.impl.CacheData - [fixed-ecommate-cfx-**************_8848-**************_9848] [add-listener] ok, tenant=ecommate-cfx, dataId=ecommate-product-server.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-29 16:41:52.465 [main] INFO  [ecommate-product-server] c.a.cloud.nacos.refresh.NacosContextRefresher - [Nacos Config] Listening config: dataId=ecommate-product-server.yaml, group=DEFAULT_GROUP
2025-07-29 16:41:52.794 [RMI TCP Connection(8)-*************] INFO  [ecommate-product-server] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:41:52.794 [RMI TCP Connection(8)-*************] INFO  [ecommate-product-server] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 16:41:52.797 [RMI TCP Connection(8)-*************] INFO  [ecommate-product-server] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-29 16:42:12.270 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/supplier/product/supplier-product/trans-to-platform-product/FXG) 参数({
    "supplierProductId": 676920503282373
})]
2025-07-29 16:42:12.985 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/supplier/product/supplier-product/trans-to-platform-product/FXG) 耗时(713 ms)]
2025-07-29 16:42:17.000 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/supplier/product/supplier-product/trans-to-platform-product/XHS) 参数({
    "supplierProductId": 676920503282373
})]
2025-07-29 16:42:17.381 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/supplier/product/supplier-product/trans-to-platform-product/XHS) 耗时(380 ms)]
2025-07-29 16:42:22.449 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/supplier/product/supplier-product/trans-to-platform-product/KWAISHOP) 参数({
    "supplierProductId": 676920503282373)]
2025-07-29 16:42:22.501 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/supplier/product/supplier-product/trans-to-platform-product/KWAISHOP) 耗时(51 ms)]
2025-07-29 16:42:31.005 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/supplier/product/supplier-product/trans-to-platform-product/KWAISHOP) 参数({
    "supplierProductId": 676920503282373
})]
2025-07-29 16:42:31.391 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/supplier/product/supplier-product/trans-to-platform-product/KWAISHOP) 耗时(385 ms)]
2025-07-29 16:42:36.284 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [preHandle][开始请求 URL(/supplier/product/supplier-product/trans-to-platform-product/SPHXD) 参数({
    "supplierProductId": 676920503282373
})]
2025-07-29 16:42:36.846 [XNIO-1 task-2] INFO  [ecommate-product-server] c.y.e.c.w.a.c.interceptor.ApiAccessLogInterceptor - [afterCompletion][完成请求 URL(/supplier/product/supplier-product/trans-to-platform-product/SPHXD) 耗时(561 ms)]
2025-07-29 16:43:29.957 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] io.undertow - stopping server: Undertow - 2.3.13.Final
2025-07-29 16:43:29.960 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 16:43:29.965 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 16:43:29.965 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] ecommate-cfx deregistering service ecommate-product-server with instance: Instance{instanceId='null', ip='*************', port=38786, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
2025-07-29 16:43:29.971 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.cloud.nacos.registry.NacosServiceRegistry - De-registration finished.
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-29 16:43:29.972 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->b7f902bb-97f2-4354-8b31-76d3db8d851e
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@145cfb13[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 34]
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3dc1aa2d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.common.remote.client - Close current connection 1753778503058_192.168.48.1_54568
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.nacos.common.remote.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4c36660f[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 29]
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->b7f902bb-97f2-4354-8b31-76d3db8d851e
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] c.a.n.client.auth.ram.identify.CredentialService - [null] CredentialService is freed
2025-07-29 16:43:29.973 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
2025-07-29 16:43:29.976 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-task-handle-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-task-handle_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-29 16:43:29.977 [SpringApplicationShutdownHook] INFO  [ecommate-product-server] o.a.r.s.support.DefaultRocketMQListenerContainer - container destroyed, DefaultRocketMQListenerContainer{consumerGroup='yaotown-listing-enterprise-create-consumer_local', namespace='', namespaceV2='', nameServer='**************:9876', topic='yaotown-listing-enterprise-create_local', consumeMode=ORDERLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
