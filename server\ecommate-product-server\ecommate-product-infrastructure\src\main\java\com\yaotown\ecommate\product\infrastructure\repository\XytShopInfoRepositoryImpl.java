package com.yaotown.ecommate.product.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yaotown.ecommate.product.domain.enterprise.repository.IXytShopInfoRepository;
import com.yaotown.ecommate.product.infrastructure.mapper.OdsXytShopInfoRealtimeMapper;
import com.yaotown.ecommate.product.infrastructure.po.OdsXytShopInfoRealtimePO;
import com.yaotown.ecommate.product.types.enums.shop.AuthStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 小亚通店铺信息仓库实现类
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class XytShopInfoRepositoryImpl implements IXytShopInfoRepository {

    private final OdsXytShopInfoRealtimeMapper odsXytShopInfoRealtimeMapper;

    @Override
    public Map<String, Object> findShopInfoByAppIdAndPlatformType(String appId, String secret, String platformType) {
        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OdsXytShopInfoRealtimePO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(OdsXytShopInfoRealtimePO::getAppKey, appId)
                    .eq(OdsXytShopInfoRealtimePO::getAppSecret, secret)
                    .eq(OdsXytShopInfoRealtimePO::getPlatformType, platformType)
                    .eq(OdsXytShopInfoRealtimePO::getAuthStatus, AuthStatusEnum.AUTHORIZED.getCode())
                    .gt(OdsXytShopInfoRealtimePO::getExpiredDate, new Date())
                    .last("LIMIT 1");
            
            OdsXytShopInfoRealtimePO shopInfo = odsXytShopInfoRealtimeMapper.selectOne(queryWrapper);
            
            if (shopInfo != null) {
                log.info("[小亚通店铺信息] 查询到店铺信息: appId={}, platformType={}", appId, platformType);
                // 将PO对象转换为Map
                return convertPoToMap(shopInfo);
            } else {
                log.info("[小亚通店铺信息] 未查询到店铺信息: appId={}, platformType={}", appId, platformType);
                return null;
            }
        } catch (Exception e) {
            log.error("[小亚通店铺信息] 查询店铺信息异常: appId={}, platformType={}, error={}", appId, platformType, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将PO对象转换为Map
     *
     * @param shopInfo PO对象
     * @return Map对象
     */
    private Map<String, Object> convertPoToMap(OdsXytShopInfoRealtimePO shopInfo) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("shop_id", shopInfo.getShopId());
        resultMap.put("create_time", shopInfo.getCreateTime());
        resultMap.put("modified_time", shopInfo.getModifiedTime());
        resultMap.put("state", shopInfo.getState());
        resultMap.put("ex_shop_id", shopInfo.getExShopId());
        resultMap.put("expired_date", shopInfo.getExpiredDate());
        resultMap.put("tenant_id", shopInfo.getTenantId());
        resultMap.put("shop_type", shopInfo.getShopType());
        resultMap.put("shop_name", shopInfo.getShopName());
        resultMap.put("platform_type", shopInfo.getPlatformType());
        resultMap.put("contacts_name", shopInfo.getContactsName());
        resultMap.put("contacts_mobile", shopInfo.getContactsMobile());
        resultMap.put("shop_url", shopInfo.getShopUrl());
        resultMap.put("auth_mode", shopInfo.getAuthMode());
        resultMap.put("auth_status", shopInfo.getAuthStatus());
        resultMap.put("auth_time", shopInfo.getAuthTime());
        resultMap.put("app_key", shopInfo.getAppKey());
        resultMap.put("app_secret", shopInfo.getAppSecret());
        resultMap.put("access_token", shopInfo.getAccessToken());
        resultMap.put("refresh_token", shopInfo.getRefreshToken());
        resultMap.put("next_refresh_token_time", shopInfo.getNextRefreshTokenTime());
        resultMap.put("remarks", shopInfo.getRemarks());
        resultMap.put("logo_url", shopInfo.getLogoUrl());
        resultMap.put("account_id", shopInfo.getAccountId());
        return resultMap;
    }
} 