package com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yaotown.common.base.core.util.CommonStreamUtil;
import com.yaotown.ecommate.common.core.entity.KeyValue;
import com.yaotown.ecommate.common.core.exception.BusinessException;
import com.yaotown.ecommate.common.core.util.json.JsonUtils;
import com.yaotown.ecommate.trade.domain.common.model.entity.ShopInfoEntity;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate.ecomorder.SphxdOrderAggregate;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.*;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.CursorTypeVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.OrderSyncWayVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.model.valobj.PlatformParamVO;
import com.yaotown.ecommate.trade.domain.order.ecomlink.service.connector.AbstractChannelConnector;
import com.yaotown.ecommate.trade.types.enums.erp.ErpPlatformTypeEnum;
import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderStatusEnum;
import com.yaotown.ecommate.trade.types.enums.order.PlatformOrderWarrantyStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抖店渠道连接器
 *
 * <AUTHOR>
 * @date 2025/6/12
 */
@Slf4j
@Component
public class SphxdChannelConnector extends AbstractChannelConnector<SphxdOrderAggregate> {

    @Value("${isvapp.sphxd.url:https://api.weixin.qq.com/channels/ec}")
    private String apiUrl;

    @Override
    public String getPlatform() {
        return ErpPlatformTypeEnum.SPHXD.getValue();
    }

    private final static GetType[] getTypes = Arrays.stream(GetTypeEnum.values())
            .map(getType -> GetType.builder()
                    .value(getType.getValue())
                    .name(getType.getName())
                    .build())
            .toArray(GetType[]::new);

    @Override
    public PlatformParamVO getPlatformParam() {
        return super.getPlatformParam()
                .setGetTypes(getTypes)
                .setOrderSyncWay(OrderSyncWayVO.CURSOR)
                ;
    }

    @Override
    public Integer getOrderCount(Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        return 0;
    }

    @Override
    public List<SphxdOrderAggregate> getOrderPage(int pageIndex, int pageSize, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        return List.of();
    }

    @Override
    public KeyValue<String, List<SphxdOrderAggregate>> getOrderPageByCursor(String cursor, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        Response response = this.getOrderList(StringUtils.equals(CursorTypeVO.FIRST.getValue(), cursor) ? null : cursor, beginDate, endDate, shopInfo, getType);
        if (response == null) {
            return new KeyValue<>(CursorTypeVO.NOMORE.getValue(), List.of());
        }
        Object orderIdListObj = response.getAdditionalProperties().get("order_id_list");
        if (!(orderIdListObj instanceof List)) {
            return new KeyValue<>(CursorTypeVO.NOMORE.getValue(), List.of());
        }
        List<String> orderIdList = (List<String>) orderIdListObj;
        if (CollUtil.isEmpty(orderIdList)) {
            return new KeyValue<>(CursorTypeVO.NOMORE.getValue(), List.of());
        }

        List<SphxdOrderAggregate> orderList = orderIdList.stream()
                .map(extOrderId -> {
                    SphxdOrderAggregate order = this.getOrderById(extOrderId, shopInfo);
                    if (order == null) {
                        log.error("get order failed, orderId:{}", extOrderId);
                    }
                    return order;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        String nextKey = response.getAdditionalProperties().get("next_key").toString();
        return new KeyValue<>(StringUtils.defaultIfBlank(nextKey, CursorTypeVO.NOMORE.getValue()), orderList);
    }

    private Response getOrderList(String cursor, Date beginDate, Date endDate, ShopInfoEntity shopInfo, GetType getType) {
        OrderQueryRequest request = new OrderQueryRequest();
        request.setPageSize(this.getPlatformParam().getDefaultPageSize());
        if (StringUtils.isNotBlank(cursor))
            request.setNextKey(cursor);

        OrderQueryRequest.TimeRange timeRange = new OrderQueryRequest.TimeRange();
        timeRange.setStartTime(beginDate.getTime() / 1000);
        timeRange.setEndTime(endDate.getTime() / 1000);

        switch (GetTypeEnum.toEnum(getType.getValue())) {
            case create:
                request.setCreateTimeRange(timeRange);
                break;
            case update:
                request.setUpdateTimeRange(timeRange);
                break;
            default:
                return null;
        }

        return this.requestApi("/order/list/get", JsonUtils.toJsonString(request), shopInfo);
    }

    private SphxdOrderAggregate getOrderById(String extOrderId, ShopInfoEntity shopInfo) {
        if (StringUtils.isBlank(extOrderId))
            return null;

        Map<String, String> request = new HashMap<>();
        request.put("order_id", extOrderId);
        Response response = this.requestApi("/order/get", JsonUtils.toJsonString(request), shopInfo);
        return JsonUtils.parseObject(response.getAdditionalProperties().get("order").toString(), SphxdOrderAggregate.class);
    }

    @Override
    public EComLinkPlatformOrderEntity parsePlatformOrder(SphxdOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        if (ecommerceOrder == null || ecommerceOrder.getOrderDetail() == null) {
            return null;
        }

        SphxdOrderAggregate.OrderDetail orderDetail = ecommerceOrder.getOrderDetail();

        // 构建订单实体
        EComLinkPlatformOrderEntity orderEntity = EComLinkPlatformOrderEntity.builder()
                .extOrderId(ecommerceOrder.getOrderId())
                .createTime(ecommerceOrder.getCreateTime() != null ? new Date(ecommerceOrder.getCreateTime() * 1000) : null)
                .updateTime(ecommerceOrder.getUpdateTime() != null ? new Date(ecommerceOrder.getUpdateTime() * 1000) : null)
                .payTime(orderDetail.getPayInfo() != null && orderDetail.getPayInfo().getPayTime() != null ?
                        new Date(orderDetail.getPayInfo().getPayTime() * 1000L) : null)
                .deliveryTime(orderDetail.getDeliveryInfo() != null && orderDetail.getDeliveryInfo().getShipDoneTime() != null ?
                        new Date(orderDetail.getDeliveryInfo().getShipDoneTime() * 1000L) : null)
                .buyerId(ecommerceOrder.getOpenid())
                .orderStatus(this.parseOrderStatus(ecommerceOrder).getValue())
                .paymentTradeId(orderDetail.getPayInfo() != null ? orderDetail.getPayInfo().getTransactionId() : null)
                .totalFee(orderDetail.getPriceInfo() != null && orderDetail.getPriceInfo().getProductPrice() != null ?
                        orderDetail.getPriceInfo().getProductPrice().longValue() : 0L)
                .paidFee(orderDetail.getPriceInfo() != null && orderDetail.getPriceInfo().getOrderPrice() != null ?
                        orderDetail.getPriceInfo().getOrderPrice().longValue() : 0L)
                .postFee(orderDetail.getPriceInfo() != null && orderDetail.getPriceInfo().getFreight() != null ?
                        orderDetail.getPriceInfo().getFreight().longValue() : 0L)
                .discountFee(orderDetail.getPriceInfo() != null && orderDetail.getPriceInfo().getDiscountedPrice() != null ?
                        orderDetail.getPriceInfo().getDiscountedPrice().longValue() : 0L)
                .platform(this.getPlatform())
                .enterpriseId(shopInfo.getEnterpriseId())
                .shopId(shopInfo.getShopId())
                .build();

        // 根据订单项退货状态判断订单退货状态
        if (orderDetail.getProductInfos() != null && !orderDetail.getProductInfos().isEmpty()) {
            Set<PlatformOrderWarrantyStatusEnum> warrantyStatusSet = new HashSet<>();
            for (SphxdOrderAggregate.ProductInfo productInfo : orderDetail.getProductInfos()) {
                warrantyStatusSet.add(this.parseItemWarrantyStatus(productInfo));
            }

            if (warrantyStatusSet.size() == 1) {
                orderEntity.setWarrantyStatus(warrantyStatusSet.iterator().next().getValue());
            } else if (warrantyStatusSet.contains(PlatformOrderWarrantyStatusEnum.REFUND_CANCEL)) {
                orderEntity.setWarrantyStatus(PlatformOrderWarrantyStatusEnum.REFUND_CANCEL.getValue());
            } else {
                orderEntity.setWarrantyStatus(PlatformOrderWarrantyStatusEnum.REFUND_PARTIAL.getValue());
            }
        } else {
            orderEntity.setWarrantyStatus(PlatformOrderWarrantyStatusEnum.NONE.getValue());
        }

        // 如果订单退款成功且订单状态为待付款，则设置订单状态为已取消
        if (orderEntity.getWarrantyStatus() == PlatformOrderWarrantyStatusEnum.REFUND_SUCCESS.getValue()
                && orderEntity.getOrderStatus() == PlatformOrderStatusEnum.PENDING.getValue()) {
            orderEntity.setOrderStatus(PlatformOrderStatusEnum.CANCEL.getValue());
        }

        return orderEntity;
    }

    @Override
    public List<EComLinkPlatformOrderItemEntity> parsePlatformOrderItems(SphxdOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        if (ecommerceOrder == null || ecommerceOrder.getOrderDetail() == null) {
            return Collections.emptyList();
        }

        SphxdOrderAggregate.OrderDetail orderDetail = ecommerceOrder.getOrderDetail();
        List<SphxdOrderAggregate.ProductInfo> productInfos = orderDetail.getProductInfos();

        if (CollUtil.isEmpty(productInfos)) {
            return Collections.emptyList();
        }

        // 构建发货商品数量映射，用于判断商品状态
        Map<String, Integer> deliveryProductNums = buildDeliveryProductNums(orderDetail);

        List<EComLinkPlatformOrderItemEntity> orderItemEntities = new ArrayList<>();
        for (int i = 0; i < productInfos.size(); i++) {
            orderItemEntities.add(parseItem(productInfos.get(i), deliveryProductNums, i));
        }
        return orderItemEntities;
    }

    /**
     * 构建发货商品数量映射
     */
    private Map<String, Integer> buildDeliveryProductNums(SphxdOrderAggregate.OrderDetail orderDetail) {
        Map<String, Integer> deliveryProductNums = new HashMap<>();

        if (orderDetail.getDeliveryInfo() != null &&
            CollUtil.isNotEmpty(orderDetail.getDeliveryInfo().getDeliveryProductInfo())) {

            for (SphxdOrderAggregate.DeliveryProductInfo deliveryInfo : orderDetail.getDeliveryInfo().getDeliveryProductInfo()) {
                if (CollUtil.isNotEmpty(deliveryInfo.getProductInfos())) {
                    for (SphxdOrderAggregate.FreightProductInfo freightInfo : deliveryInfo.getProductInfos()) {
                        String skuId = freightInfo.getSkuId();
                        Integer productCnt = freightInfo.getProductCnt() != null ? freightInfo.getProductCnt() : 0;
                        deliveryProductNums.merge(skuId, productCnt, Integer::sum);
                    }
                }
            }
        }

        return deliveryProductNums;
    }

    /**
     * 解析单个商品项
     */
    private EComLinkPlatformOrderItemEntity parseItem(SphxdOrderAggregate.ProductInfo productInfo,
                                                      Map<String, Integer> deliveryProductNums,
                                                      int index) {
        EComLinkPlatformOrderItemEntity orderItemEntity = EComLinkPlatformOrderItemEntity.builder()
                .extNumIid(productInfo.getProductId())
                .extSkuId(productInfo.getSkuId())
                .extSkuTitle(productInfo.getTitle())
                .extOuterId(productInfo.getSkuCode())
                .imageUrl(productInfo.getThumbImg())
                .num(productInfo.getSkuCnt() != null ? productInfo.getSkuCnt() : 0)
                .price(productInfo.getSalePrice() != null ? productInfo.getSalePrice().longValue() : 0L)
                .itemStatus(this.parseItemStatus(productInfo, deliveryProductNums).getValue())
                .warrantyStatus(this.parseItemWarrantyStatus(productInfo).getValue())
                .build();

        // 设置扩展商品ID，格式：productId@skuId@index
        orderItemEntity.setExtItemId(String.format("%s@%s@%d",
                orderItemEntity.getExtNumIid(),
                orderItemEntity.getExtSkuId(),
                index));

        // 解析SKU规格信息
        if (CollUtil.isNotEmpty(productInfo.getSkuAttrs())) {
            String skuSpecChars = productInfo.getSkuAttrs()
                    .stream()
                    .map(attr -> attr.getAttrKey() + ":" + attr.getAttrValue())
                    .collect(Collectors.joining(";"));
            orderItemEntity.setSkuSpecChars(skuSpecChars);
        }

        return orderItemEntity;
    }

    /**
     * 解析商品状态
     */
    private PlatformOrderStatusEnum parseItemStatus(SphxdOrderAggregate.ProductInfo productInfo,
                                                   Map<String, Integer> deliveryProductNums) {
        Integer skuCnt = productInfo.getSkuCnt() != null ? productInfo.getSkuCnt() : 0;
        Integer deliveryNum = deliveryProductNums.getOrDefault(productInfo.getSkuId(), 0);

        // 如果商品数量大于已发货数量，则为待发货状态，否则为配送中状态
        return skuCnt > deliveryNum ? PlatformOrderStatusEnum.TOBESHIPPED : PlatformOrderStatusEnum.DELIVERY;
    }

    @Override
    public List<EComLinkPlatformOrderLogisticsEntity> parsePlatformOrderLogistics(SphxdOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        List<SphxdOrderAggregate.DeliveryProductInfo> deliveryProductInfos = ecommerceOrder.getOrderDetail().getDeliveryInfo().getDeliveryProductInfo();
        if (CollectionUtils.isEmpty(deliveryProductInfos))
            return null;

        List<EComLinkPlatformOrderLogisticsEntity> orderLogisticsEntities = new ArrayList<>();
        for (SphxdOrderAggregate.DeliveryProductInfo deliveryProductInfo : deliveryProductInfos) {
            EComLinkPlatformOrderLogisticsEntity orderLogisticsEntity = EComLinkPlatformOrderLogisticsEntity.builder()
                    .companyCode(deliveryProductInfo.getDeliveryId())
                    .companyName(deliveryProductInfo.getDeliveryName())
                    .deliveryNo(deliveryProductInfo.getWaybillId())
                    .deliveryTime(new Date(deliveryProductInfo.getDeliveryTime() * 1000))
                    .build();
            orderLogisticsEntities.add(orderLogisticsEntity);
        }
        return orderLogisticsEntities;
    }

    @Override
    public EComLinkPlatformOrderMemoEntity parsePlatformOrderMemo(SphxdOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        SphxdOrderAggregate.ExtInfo extInfo = ecommerceOrder.getOrderDetail().getExtInfo();
        EComLinkPlatformOrderMemoEntity orderMemoEntity = EComLinkPlatformOrderMemoEntity.builder()
                .buyerMemo(extInfo.getCustomerNotes())
                .sellerMemo(extInfo.getMerchantNotes())
                .build();

        if (StringUtils.isAllBlank(orderMemoEntity.getBuyerMemo(), orderMemoEntity.getSellerMemo()))
            return null;
        return orderMemoEntity;
    }

    @Override
    public EComLinkPlatformOrderConsigneeEntity parsePlatformOrderConsignee(SphxdOrderAggregate ecommerceOrder, ShopInfoEntity shopInfo) {
        SphxdOrderAggregate.AddressInfo addressInfo = ecommerceOrder.getOrderDetail().getDeliveryInfo().getAddressInfo();
        EComLinkPlatformOrderConsigneeEntity orderConsigneeDto = EComLinkPlatformOrderConsigneeEntity.builder()
                .receiverName(addressInfo.getUserName())
                .receiverMobile(
                        StringUtils.firstNonBlank(
                                addressInfo.getTelNumberExtInfo().getVirtualTelNumber(),
                                addressInfo.getVirtualOrderTelNumber(),
                                addressInfo.getTelNumber()
                        )
                )
                .receiverState(addressInfo.getProvinceName())
                .receiverCity(addressInfo.getCityName())
                .receiverDistrict(addressInfo.getCountyName())
                .receiverAddress(addressInfo.getDetailInfo())
                .receiverZip(addressInfo.getPostalCode())
                .build();
        return orderConsigneeDto;
    }

    private Response requestApi(String path, String requestStr, ShopInfoEntity shopInfo) {
        HttpRequest httpRequest = HttpRequest
                .post(UrlBuilder.of(StringUtils.join(apiUrl, path), StandardCharsets.UTF_8)
                        .addQuery("access_token", shopInfo.getAccessToken())
                        .build())
                .contentType(ContentType.JSON.getValue())
                .body(requestStr);
        log.info("[平台订单连接器][sphxd-API] request : {}", requestStr);
        try (HttpResponse response = httpRequest.execute()) {
            String responseStr = response.body();
            log.info("[平台订单连接器][sphxd-API] response : {}", responseStr);

            if (StringUtils.isBlank(responseStr))
                throw new BusinessException(responseStr);

            Response responseJson = JsonUtils.parseObject(responseStr, Response.class);
            assert responseJson != null;
            String code = responseJson.getAdditionalProperties().get("errcode").toString();
            if (!code.equals("0")) {
                String msg = StringUtils.defaultIfBlank(
                        responseJson.getAdditionalProperties().get("errmsg").toString(),
                        responseStr
                );
                switch (code) {
                    case "42001": // access_token失效
                    case "40001": // 获取 access_token 时 AppSecret 错误，或者 access_token 无效
                        throw new BusinessException(String.format("店铺授权已过期[%s]%s", code, msg));
                    default:
                        throw new BusinessException(String.format("[%s]%s", code, msg));
                }
            }
            return responseJson;
        }
    }

    @AllArgsConstructor
    @Getter
    enum GetTypeEnum {
        create(0, "创建时间"),
        update(1, "更新时间"),
        ;
        private final int value;
        private final String name;

        static GetTypeEnum toEnum(int value) {
            for (GetTypeEnum getType : values()) {
                if (getType.value == value)
                    return getType;
            }
            return create;
        }
    }

    @Data
    public static class OrderQueryRequest {

        /**
         * 订单创建时间范围（至少填 start_time 或 end_time）
         */
        @JsonProperty("create_time_range")
        private TimeRange createTimeRange;

        /**
         * 订单更新时间范围（至少填 start_time 或 end_time）
         */
        @JsonProperty("update_time_range")
        private TimeRange updateTimeRange;

        /**
         * 订单状态（参考 RequestOrderStatus 枚举）
         */
        private Integer status;

        /**
         * 买家身份标识
         */
        private String openid;

        /**
         * 分页参数（上一页返回）
         */
        private String nextKey;

        /**
         * 每页数量（不超过100，必填）
         */
        @JsonProperty("page_size")
        private Integer pageSize;

        /**
         * 时间范围结构体（TimeRange）
         * 适用于 create_time_range / update_time_range
         */
        @Data
        public static class TimeRange {
            /**
             * 秒级时间戳（距离 end_time 不超过7天，必填）
             */
            @JsonProperty("start_time")
            private Long startTime;
            /**
             * 秒级时间戳（距离 start_time 不超过7天，必填）
             */
            @JsonProperty("end_time")
            private Long endTime;
        }

        /**
         * 订单状态枚举（RequestOrderStatus）
         * 与接口文档枚举值完全对齐
         */
        @AllArgsConstructor
        @Getter
        public enum RequestOrderStatus {
            PENDING_PAYMENT(10, "待付款"),
            GIFT_NOT_RECEIVED(12, "礼物待收下"),
            GROUP_BUYING(13, "凑单买凑团中"),
            PENDING_DELIVERY(20, "待发货（包括部分发货）"),
            PARTIAL_DELIVERY(21, "部分发货"),
            PENDING_RECEIPT(30, "待收货（包括部分发货）"),
            COMPLETED(100, "完成"),
            CANCELED(250, "订单取消（包括未付款取消，售后取消等）");

            private final Integer code;
            private final String desc;
        }
    }

    /**
     * 返回参数对象
     * 对应接口的响应体结构
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Response {
        /**
         * 错误码（0 表示成功）
         */
        private String errcode;
        /**
         * 错误信息
         */
        private String errmsg;

        // 额外对象字段
        private Map<String, Object> additionalProperties = new HashMap<>();

        // 添加动态属性
        @JsonAnySetter
        public void addAdditionalProperty(String key, Object value) {
            this.additionalProperties.put(key, value);
        }

        // 获取动态属性
        @JsonAnyGetter
        public Map<String, Object> getAdditionalProperties() {
            return this.additionalProperties;
        }

//        /**
//         * 订单号列表
//         */
//        @JsonProperty("order_id_list")
//        private List<String> orderIdList;
//        /**
//         * 分页参数（下一页请求用）
//         */
//        @JsonProperty("next_key")
//        private String nextKey;
//        /**
//         * 是否还有下一页
//         */
//        @JsonProperty("has_more")
//        private Boolean hasMore;
//
//        /**
//         * 订单详情
//         */
//        private SphxdOrderAggregate order;
    }

    /**
     * 解析订单状态
     */
    private PlatformOrderStatusEnum parseOrderStatus(SphxdOrderAggregate ecommerceOrder) {
        if (ecommerceOrder.getStatus() == null) {
            return PlatformOrderStatusEnum.PENDING;
        }

        switch (ecommerceOrder.getStatus()) {
            case 20: // 待发货（包括部分发货）
            case 21: // 部分发货
                return PlatformOrderStatusEnum.TOBESHIPPED;
            case 30: // 待收货（包括部分发货）
                return PlatformOrderStatusEnum.DELIVERY;
            case 100: // 完成
                return PlatformOrderStatusEnum.SIGNED;
            case 250: // 订单取消
                return PlatformOrderStatusEnum.CANCEL;
            default:
                return PlatformOrderStatusEnum.PENDING;
        }
    }

    /**
     * 解析单个商品售后状态
     */
    private PlatformOrderWarrantyStatusEnum parseItemWarrantyStatus(SphxdOrderAggregate.ProductInfo productInfo) {
        Integer skuCnt = productInfo.getSkuCnt();
        Integer onAftersaleSkuCnt = productInfo.getOnAftersaleSkuCnt();
        Integer finishAftersaleSkuCnt = productInfo.getFinishAftersaleSkuCnt();

        if (skuCnt == null) skuCnt = 0;
        if (onAftersaleSkuCnt == null) onAftersaleSkuCnt = 0;
        if (finishAftersaleSkuCnt == null) finishAftersaleSkuCnt = 0;

        if (onAftersaleSkuCnt == 0 && finishAftersaleSkuCnt == 0) {
            return PlatformOrderWarrantyStatusEnum.NONE;
        }
        if (onAftersaleSkuCnt > 0) {
            return PlatformOrderWarrantyStatusEnum.REFUNDING;
        }
        if (finishAftersaleSkuCnt >= skuCnt) {
            return PlatformOrderWarrantyStatusEnum.REFUND_SUCCESS;
        }
        return PlatformOrderWarrantyStatusEnum.REFUNDING;
    }

}
