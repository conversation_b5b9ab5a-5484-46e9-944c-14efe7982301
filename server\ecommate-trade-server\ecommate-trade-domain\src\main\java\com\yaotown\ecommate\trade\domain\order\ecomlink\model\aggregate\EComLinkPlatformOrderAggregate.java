package com.yaotown.ecommate.trade.domain.order.ecomlink.model.aggregate;

import com.yaotown.ecommate.trade.domain.order.ecomlink.model.entity.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@Builder
public class EComLinkPlatformOrderAggregate {
    /**
     * 平台订单信息
     */
    private EComLinkPlatformOrderEntity platformOrder;
    /**
     * 平台订单项集合
     */
    private List<EComLinkPlatformOrderItemEntity> platformOrderItems;
    /**
     * 平台订单物流信息
     */
    private List<EComLinkPlatformOrderLogisticsEntity> platformOrderLogistics;
    /**
     * 平台订单收件人
     */
    private EComLinkPlatformOrderConsigneeEntity platformOrderConsignee;
    /**
     * 平台订单备注
     */
    private EComLinkPlatformOrderMemoEntity platformOrderMemo;

    // TODO 其他附属信息暂不同步 比如：订单收件人、订单备注、退款信息、售后信息等
}
