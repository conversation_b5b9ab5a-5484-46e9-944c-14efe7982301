package com.yaotown.ecommate.product.domain.product.management.model.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 运费模板实体
 */
@Data
@Accessors(chain = true)
public class SupplierFreightTemplateEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 所属企业id
     */
    private Long enterpriseId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 地区名称
     */
    private String regionName;

    /**
     * 地区编码，多个用逗号分隔
     */
    private String regionCodes;

    /**
     * 首件(个)
     */
    private Integer firstItem;

    /**
     * 首件运费(分)
     */
    private Long firstItemFee;

    /**
     * 续件(个)
     */
    private Integer additionalItem;

    /**
     * 续件运费(分)
     */
    private Long additionalItemFee;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 修改操作人
     */
    private Long modifierId;

    /**
     * 修改操作人名字
     */
    private String modifierName;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date updated;
} 